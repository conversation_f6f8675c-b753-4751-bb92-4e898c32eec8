import os
import sys
import ctypes
import psutil
import time
import threading
from ctypes import wintypes
import logging
from datetime import datetime
import json
import hashlib
import uuid
import base64
import winreg
import subprocess
import platform
from datetime import datetime, timedelta
import struct
import numpy as np
import random

# 添加keyboard库导入
import keyboard

from PyQt6.QtCore import Qt, QTimer, QSize, QPropertyAnimation, QRect, QEvent, QAbstractNativeEventFilter
from PyQt6.QtGui import QIcon, QPixmap, QColor, QFont, QAction, QKeySequence
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QLabel, QPushButton, QVBoxLayout,
    QHBoxLayout, QGroupBox, QTabWidget, QComboBox, QLineEdit, QCheckBox,
    QFrame, QMessageBox, QStatusBar, QStyleFactory, QSizePolicy, QSpacerItem,
    QFormLayout, QSlider, QMenu, QToolBar, QToolButton, QSplitter, QSystemTrayIcon,
    QGridLayout, QListWidget, QDialog, QDialogButtonBox, QTabWidget, QStackedWidget, QRadioButton,
    QTextEdit  # 添加QTextEdit导入
)

# 导入激活对话框
from activation_dialog import HardwareActivationDialog
# 导入战斗管理器和其他必要组件
from battle_logic import BattleManager
from monster_select import MonsterSelector
from input_simulator import InputSimulator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('game_memory_tool.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('game_memory_tool')

# 必要的Windows API常量
PROCESS_ALL_ACCESS = 0x1F0FFF
PROCESS_VM_OPERATION = 0x0008
PROCESS_VM_READ = 0x0010
PROCESS_VM_WRITE = 0x0020
PROCESS_QUERY_INFORMATION = 0x0400
PROCESS_REQUIRED_ACCESS = PROCESS_VM_OPERATION | PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_QUERY_INFORMATION

# 全局热键API常量
WM_HOTKEY = 0x0312
MOD_ALT = 0x0001
MOD_CONTROL = 0x0002
MOD_SHIFT = 0x0004
MOD_WIN = 0x0008
VK_HOME = 0x24  # Home键的虚拟键码

# 加载必要的Windows API
kernel32 = ctypes.WinDLL('kernel32', use_last_error=True)
user32 = ctypes.WinDLL('user32', use_last_error=True)

# 定义Windows API函数
OpenProcess = kernel32.OpenProcess
OpenProcess.argtypes = [wintypes.DWORD, wintypes.BOOL, wintypes.DWORD]
OpenProcess.restype = wintypes.HANDLE

ReadProcessMemory = kernel32.ReadProcessMemory
ReadProcessMemory.argtypes = [wintypes.HANDLE, wintypes.LPCVOID, wintypes.LPVOID, ctypes.c_size_t, ctypes.POINTER(ctypes.c_size_t)]
ReadProcessMemory.restype = wintypes.BOOL

WriteProcessMemory = kernel32.WriteProcessMemory
WriteProcessMemory.argtypes = [wintypes.HANDLE, wintypes.LPVOID, wintypes.LPCVOID, ctypes.c_size_t, ctypes.POINTER(ctypes.c_size_t)]
WriteProcessMemory.restype = wintypes.BOOL

CloseHandle = kernel32.CloseHandle
CloseHandle.argtypes = [wintypes.HANDLE]
CloseHandle.restype = wintypes.BOOL

def is_admin():
    """检查程序是否以管理员权限运行"""
    return False

# 内存操作类
class MemoryHelper:
    def __init__(self):
        self.process_handle = None
        self.process_id = None
        
    def open_process(self, process_id):
        """打开进程以允许内存操作"""
        self.process_id = process_id
        
        # 首先尝试使用较低的权限
        self.process_handle = OpenProcess(PROCESS_REQUIRED_ACCESS, False, process_id)
        success = self.process_handle is not None and self.process_handle != 0
        
        if not success:
            error_code = ctypes.get_last_error()
            logger.error(f"使用正常权限打开进程失败，错误码: {error_code}")
            
            # 尝试使用更低的权限（只读）
            self.process_handle = OpenProcess(PROCESS_VM_READ | PROCESS_QUERY_INFORMATION, False, process_id)
            success = self.process_handle is not None and self.process_handle != 0
            
            if success:
                logger.warning("已使用只读权限打开进程，写入内存操作将不可用")
            else:
                error_code = ctypes.get_last_error()
                logger.error(f"使用降低的权限打开进程仍然失败，错误码: {error_code}")
                
        else:
            logger.info(f"成功打开进程 ID: {process_id}")
            
        return success
    
    def close_process(self):
        """关闭当前打开的进程句柄"""
        if self.process_handle:
            CloseHandle(self.process_handle)
            self.process_handle = None
            self.process_id = None
            logger.info("已关闭进程句柄")
    
    def read_int(self, address):
        """从内存中读取一个整数"""
        buffer = ctypes.c_int()
        bytes_read = ctypes.c_size_t()
        result = ReadProcessMemory(self.process_handle, address, ctypes.byref(buffer), 
                                  ctypes.sizeof(buffer), ctypes.byref(bytes_read))
        if result:
            return buffer.value
        else:
            error_code = ctypes.get_last_error()
            logger.error(f"读取整数失败，地址: 0x{address:X}, 错误码: {error_code}")
            return None
    
    def read_float(self, address):
        """从内存中读取一个浮点数"""
        buffer = ctypes.c_float()
        bytes_read = ctypes.c_size_t()
        result = ReadProcessMemory(self.process_handle, address, ctypes.byref(buffer), 
                                  ctypes.sizeof(buffer), ctypes.byref(bytes_read))
        if result:
            return buffer.value
        else:
            error_code = ctypes.get_last_error()
            logger.error(f"读取浮点数失败，地址: 0x{address:X}, 错误码: {error_code}")
            return None
    
    def read_long(self, address):
        """从内存中读取一个长整数"""
        buffer = ctypes.c_int64()
        bytes_read = ctypes.c_size_t()
        result = ReadProcessMemory(self.process_handle, address, ctypes.byref(buffer), 
                                  ctypes.sizeof(buffer), ctypes.byref(bytes_read))
        if result:
            return buffer.value
        else:
            error_code = ctypes.get_last_error()
            logger.error(f"读取长整数失败，地址: 0x{address:X}, 错误码: {error_code}")
            return None
    
    def write_int(self, address, value):
        """向内存写入一个整数"""
        buffer = ctypes.c_int(value)
        bytes_written = ctypes.c_size_t()
        return WriteProcessMemory(self.process_handle, address, ctypes.byref(buffer), 
                                 ctypes.sizeof(buffer), ctypes.byref(bytes_written))
    
    def write_float(self, address, value):
        """向内存写入一个浮点数"""
        buffer = ctypes.c_float(value)
        bytes_written = ctypes.c_size_t()
        return WriteProcessMemory(self.process_handle, address, ctypes.byref(buffer), 
                                 ctypes.sizeof(buffer), ctypes.byref(bytes_written))
    
    def write_bytes(self, address, data):
        """向内存写入字节数组"""
        buffer = (ctypes.c_ubyte * len(data))(*data)
        bytes_written = ctypes.c_size_t()
        return WriteProcessMemory(self.process_handle, address, buffer, 
                                 ctypes.sizeof(buffer), ctypes.byref(bytes_written))

    def read_pointer_chain(self, base_address, offsets):
        """读取多级指针链"""
        address = base_address
        for offset in offsets:
            address = self.read_long(address + offset)
            if not address:
                return 0
        return address

    def get_module_base(self, module_name):
        """获取指定模块的基址"""
        try:
            if not self.process_id:
                logger.error("未指定进程ID，无法获取模块基址")
                return 0
            
            # 定义必要的结构和常量
            TH32CS_SNAPMODULE = 0x00000008
            TH32CS_SNAPMODULE32 = 0x00000010
            INVALID_HANDLE_VALUE = -1
            
            class MODULEENTRY32(ctypes.Structure):
                _fields_ = [
                    ("dwSize", ctypes.c_ulong),
                    ("th32ModuleID", ctypes.c_ulong),
                    ("th32ProcessID", ctypes.c_ulong),
                    ("GlblcntUsage", ctypes.c_ulong),
                    ("ProccntUsage", ctypes.c_ulong),
                    ("modBaseAddr", ctypes.c_void_p),
                    ("modBaseSize", ctypes.c_ulong),
                    ("hModule", ctypes.c_void_p),
                    ("szModule", ctypes.c_char * 256),
                    ("szExePath", ctypes.c_char * 260)
                ]
            
            # 获取进程模块快照
            CreateToolhelp32Snapshot = ctypes.windll.kernel32.CreateToolhelp32Snapshot
            CreateToolhelp32Snapshot.argtypes = [ctypes.c_ulong, ctypes.c_ulong]
            CreateToolhelp32Snapshot.restype = ctypes.c_void_p
            
            Module32First = ctypes.windll.kernel32.Module32First
            Module32First.argtypes = [ctypes.c_void_p, ctypes.POINTER(MODULEENTRY32)]
            Module32First.restype = ctypes.c_int
            
            Module32Next = ctypes.windll.kernel32.Module32Next
            Module32Next.argtypes = [ctypes.c_void_p, ctypes.POINTER(MODULEENTRY32)]
            Module32Next.restype = ctypes.c_int
            
            # 尝试获取模块
            hModuleSnap = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE | TH32CS_SNAPMODULE32, self.process_id)
            if hModuleSnap == INVALID_HANDLE_VALUE:
                error_code = ctypes.get_last_error()
                logger.error(f"无法创建模块快照，错误码: {error_code}")
                return 0
            
            me32 = MODULEENTRY32()
            me32.dwSize = ctypes.sizeof(MODULEENTRY32)
            
            logger.info(f"正在搜索模块: {module_name}")
            
            # 获取第一个模块
            if Module32First(hModuleSnap, ctypes.byref(me32)):
                # 遍历所有模块
                while True:
                    module_name_str = me32.szModule.decode('utf-8', errors='ignore').lower()
                    if module_name.lower() in module_name_str:
                        logger.info(f"找到模块 {module_name_str}，基址: 0x{me32.modBaseAddr:X}")
                        CloseHandle(hModuleSnap)
                        return me32.modBaseAddr
                    
                    if not Module32Next(hModuleSnap, ctypes.byref(me32)):
                        break
            
            logger.error(f"找不到模块: {module_name}")
            CloseHandle(hModuleSnap)
            return 0
        except Exception as e:
            logger.error(f"获取模块基址失败: {str(e)}")
            return 0

class AppStyles:
    """应用样式类"""
    def __init__(self):
        # 应用的颜色方案
        self.colors = {
            "bg_dark": "#0F1C2E",
            "bg_medium": "#1A2C42",
            "bg_light": "#263C58",
            "accent": "#00A8FF",
            "accent_dark": "#0076B3",
            "text": "#E0E0E0",
            "text_dark": "#A0A0A0",
            "success": "#00E676",
            "warning": "#FFD600",
            "error": "#FF1744"
        }
        
        # 应用的样式表
        self.main_style = f"""
            QMainWindow, QDialog {{
                background-color: {self.colors['bg_dark']};
                color: {self.colors['text']};
            }}
            QWidget {{
                background-color: {self.colors['bg_dark']};
                color: {self.colors['text']};
            }}
            QTabWidget::pane {{
                border: 1px solid {self.colors['bg_light']};
                background-color: {self.colors['bg_dark']};
            }}
            QTabBar::tab {{
                background-color: {self.colors['bg_medium']};
                color: {self.colors['text']};
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }}
            QTabBar::tab:selected {{
                background-color: {self.colors['bg_light']};
                color: {self.colors['accent']};
            }}
            QTabBar::tab:hover {{
                background-color: {self.colors['bg_light']};
            }}
            QPushButton {{
                background-color: {self.colors['accent']};
                color: white;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.colors['accent_dark']};
            }}
            QPushButton:pressed {{
                background-color: {self.colors['accent_dark']};
                padding: 7px 11px;
            }}
            QPushButton:disabled {{
                background-color: #555555;
                color: #999999;
            }}
            QLineEdit, QComboBox {{
                background-color: {self.colors['bg_light']};
                color: {self.colors['text']};
                border: 1px solid {self.colors['bg_light']};
                border-radius: 4px;
                padding: 5px;
            }}
            QLineEdit:focus, QComboBox:focus {{
                border: 1px solid {self.colors['accent']};
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 15px;
                border-left-width: 1px;
                border-left-color: {self.colors['bg_light']};
                border-left-style: solid;
            }}
            QGroupBox {{
                background-color: {self.colors['bg_medium']};
                border: 1px solid {self.colors['bg_light']};
                border-radius: 6px;
                margin-top: 1ex;
                padding-top: 10px;
                font-weight: bold;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 5px;
                color: {self.colors['accent']};
            }}
            QCheckBox {{
                color: {self.colors['text']};
            }}
            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
            }}
            QCheckBox::indicator:unchecked {{
                border: 1px solid {self.colors['text_dark']};
                background-color: {self.colors['bg_medium']};
                border-radius: 3px;
            }}
            QCheckBox::indicator:checked {{
                border: 1px solid {self.colors['accent']};
                background-color: {self.colors['accent']};
                border-radius: 3px;
            }}
            QStatusBar {{
                background-color: {self.colors['bg_medium']};
                color: {self.colors['text']};
            }}
            QLabel {{
                color: {self.colors['text']};
            }}
            QSlider::groove:horizontal {{
                border: 1px solid {self.colors['bg_light']};
                background: {self.colors['bg_medium']};
                height: 8px;
                border-radius: 4px;
            }}
            QSlider::handle:horizontal {{
                background: {self.colors['accent']};
                border: 1px solid {self.colors['accent']};
                width: 16px;
                margin: -5px 0;
                border-radius: 8px;
            }}
            QMenu {{
                background-color: {self.colors['bg_medium']};
                color: {self.colors['text']};
                border: 1px solid {self.colors['bg_light']};
            }}
            QMenu::item:selected {{
                background-color: {self.colors['accent']};
            }}
        """
        
        # 特殊按钮样式
        self.success_button = f"""
            QPushButton {{
                background-color: {self.colors['success']};
                color: black;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #00C853;
            }}
            QPushButton:pressed {{
                background-color: #00B848;
                padding: 7px 11px;
            }}
        """
        
        self.warning_button = f"""
            QPushButton {{
                background-color: {self.colors['warning']};
                color: black;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #FFC400;
            }}
            QPushButton:pressed {{
                background-color: #FFAB00;
                padding: 7px 11px;
            }}
        """
        
        self.error_button = f"""
            QPushButton {{
                background-color: {self.colors['error']};
                color: white;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #D50000;
            }}
            QPushButton:pressed {{
                background-color: #B71C1C;
                padding: 7px 11px;
            }}
        """
    
    def apply_to_app(self, app):
        """应用样式到整个应用"""
        app.setStyle(QStyleFactory.create("Fusion"))
        app.setStyleSheet(self.main_style)


class FeatureCard(QFrame):
    """功能卡片组件"""
    def __init__(self, title, description, parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.setFrameShadow(QFrame.Shadow.Raised)
        self.setObjectName("featureCard")
        
        # 自定义卡片样式
        self.setStyleSheet("""
            #featureCard {
                background-color: #263C58;
                border-radius: 8px;
                border: 1px solid #3A5274;
            }
        """)
        
        # 创建布局
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #00A8FF;")
        layout.addWidget(title_label)
        
        # 描述
        desc_label = QLabel(description)
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #A0A0A0;")
        layout.addWidget(desc_label)
        
        # 按钮容器
        self.buttons_layout = QHBoxLayout()
        layout.addLayout(self.buttons_layout)
        
        # 设置间距
        layout.setContentsMargins(15, 15, 15, 15)
        
    def add_button(self, text, callback=None, style=None):
        """添加按钮到卡片"""
        button = QPushButton(text)
        
        if style == "success":
            button.setStyleSheet(AppStyles().success_button)
        elif style == "warning":
            button.setStyleSheet(AppStyles().warning_button)
        elif style == "error":
            button.setStyleSheet(AppStyles().error_button)
            
        if callback:
            button.clicked.connect(callback)
            
        self.buttons_layout.addWidget(button)
        return button


class GameMemoryTool(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # 设置应用图标
        app_icon = QIcon("icon.ico") 
        self.setWindowIcon(app_icon)
        
        # 设置系统托盘图标也使用相同图标
        if hasattr(self, 'tray_icon') and self.tray_icon:
            self.tray_icon.setIcon(app_icon)
        
        self.memory = MemoryHelper()
        self.process_id = None
        self.module_base = 0
        
        # 窗口状态跟踪
        self._window_visible = True
        
        # 游戏版本数据
        self.game_versions = {
            "4.5核心8880版[64位]": {
                "level1_offset": "70",
                "level2_offset": "10",
                "level3_offset": "20",
                "base_offset": "DC8918",
                "character_base": "368",
                "attack_speed": "4fa",
                "move_speed": "6cc",
                "air_lock": "8d8",
                "height_base": "180",
                "attack_range": "1211B30",
                "show_hp": "547109",
                "x_coord": "98",
                "y_coord": "9c",
                "z_coord": "a0",
                "qsk_offset": "DC9F98",
                "skill_speed_adjust": "1211C08",
                "attack_speed_data": "2853a0",
                "wing": "1211C14",
                "view_offset": "0120B4E0",
                "view_offset1": "4BC",
                "map_offset": "D99DDC",
                "teleport_speed": "37B31BA0"  # 添加瞬移加速基值地址
            },
            "4.5核心8232版[64位]": {
                "level1_offset": "70",
                "level2_offset": "10",
                "level3_offset": "20",
                "base_offset": "DC7918",
                "character_base": "368",
                "attack_speed": "4fa",
                "move_speed": "6cc",
                "air_lock": "8d8",
                "height_base": "180",
                "attack_range": "1210AE0",
                "show_hp": "546759",
                "x_coord": "98",
                "y_coord": "9c",
                "z_coord": "a0",
                "qsk_offset": "DC8F98",
                "skill_speed_adjust": "1210BB8",
                "attack_speed_data": "284e60",
                "wing": "1210BC4",
                "view_offset": "0120A4E0",
                "view_offset1": "4BC",
                "map_offset": "D98DDC",
                "teleport_speed": "37B31BA0"  # 添加瞬移加速基值地址
            }
        }
        
        # 当前选择的版本
        self.current_version = None
        
        # 配置数据
        self.offsets = {
            "base_offset": 0,
            "level1_offset": 0,
            "level2_offset": 0,
            "level3_offset": 0,
            "character_base": 0,
            "move_speed": 0,
            "attack_speed": 0,
            "air_lock": 0,
            "height_base": 0,
            "x_coord": 0,
            "y_coord": 0,
            "z_coord": 0
        }
        
        # 功能状态
        self.features_active = {
            "attack_speed": False,
            "move_speed": False,
            "air_lock": False,
            "fly": False,
            "stealth": False,
            "teleport_speed": False,  # 添加瞬移加速功能状态
            "view_range": False,      # 添加视野扩展功能状态
            "show_hp": False,         # 添加显血功能状态
            "map_extend": False,      # 添加地图扩展功能状态
            "wing_nocd": False,       # 添加翅膀无CD功能状态
            "skill_speed_adjust": False, # 添加攻击速度微调功能状态
            "qsk": False,             # 添加QSK秒起功能状态
            "attack_range": False     # 添加攻击距离功能状态
        }
        
        # 定时器
        self.timers = {}
        
        # 战斗管理器
        self.battle_manager = None
        
        # 设置界面
        self.setup_ui()
        
        # 保存坐标
        self.saved_coordinates = {}
        
        # 初始化系统托盘
        self.setup_tray_icon()
        
        # 设置全局热键
        self.register_global_hotkey()
        
        # 添加定期检查激活状态的定时器
        self.activation_timer = QTimer(self)
        self.activation_timer.timeout.connect(self.check_activation_status)
        self.activation_timer.start(1800000)  # 每30分钟检查一次
    
    def setup_ui(self):
        """设置UI界面"""
        self.setWindowTitle("Aion私服专用")
        self.setMinimumSize(900, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)
        
        # 添加顶部布局
        top_layout = QHBoxLayout()
        
        # 标题和版本
        title_layout = QVBoxLayout()
        title_label = QLabel("Aion4.6-5.8")
        title_label.setStyleSheet("font-size: 22px; font-weight: bold; color: #00A8FF;")
        version_label = QLabel("私服 专业版")
        version_label.setStyleSheet("color: #A0A0A0;")
        title_layout.addWidget(title_label)
        title_layout.addWidget(version_label)
        
        top_layout.addLayout(title_layout)
        top_layout.addStretch()
        
        # 添加状态指示
        status_layout = QHBoxLayout()
        
        process_label = QLabel("进程状态:")
        self.process_status = QLabel("未连接")
        self.process_status.setStyleSheet("color: #FF1744; font-weight: bold;")
        
        status_layout.addWidget(process_label)
        status_layout.addWidget(self.process_status)
        
        top_layout.addLayout(status_layout)
        
        main_layout.addLayout(top_layout)
        
        # 创建水平分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧控制面板
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 10, 0)
        
        # 进程选择组
        process_group = QGroupBox("进程控制中心")
        process_layout = QVBoxLayout(process_group)
        
        # 进程选择下拉框
        self.process_combo = QComboBox()
        self.process_combo.setMinimumHeight(30)
        self.process_combo.setPlaceholderText("选择游戏进程...")
        process_layout.addWidget(self.process_combo)
        
        # 进程控制按钮
        btn_layout = QHBoxLayout()
        self.refresh_btn = QPushButton("扫描进程")
        self.refresh_btn.clicked.connect(self.refresh_processes)
        
        self.load_btn = QPushButton("加载游戏")
        self.load_btn.clicked.connect(self.load_game)
        self.load_btn.setStyleSheet(AppStyles().success_button)
        
        btn_layout.addWidget(self.refresh_btn)
        btn_layout.addWidget(self.load_btn)
        process_layout.addLayout(btn_layout)
        
        left_layout.addWidget(process_group)
        
        # 游戏版本选择
        version_group = QGroupBox("游戏核心版本")
        version_layout = QVBoxLayout(version_group)
        
        self.version_combo = QComboBox()
        self.version_combo.setMinimumHeight(30)
        self.version_combo.addItems(self.game_versions.keys())
        self.version_combo.setCurrentIndex(0)
        version_layout.addWidget(self.version_combo)
        
        left_layout.addWidget(version_group)
        
        # 诊断信息区域
        diag_group = QGroupBox("诊断信息")
        diag_layout = QVBoxLayout(diag_group)
        
        self.diag_text = QLabel("等待连接游戏进程...")
        self.diag_text.setWordWrap(True)
        self.diag_text.setStyleSheet("color: #A0A0A0;")
        diag_layout.addWidget(self.diag_text)
        
        self.run_diag_btn = QPushButton("运行诊断")
        self.run_diag_btn.clicked.connect(self.run_diagnostics)
        diag_layout.addWidget(self.run_diag_btn)
        
        left_layout.addWidget(diag_group)
        
        # 填充剩余空间
        left_layout.addStretch()
        
        # 右侧功能选项卡
        tabs = QTabWidget()
        
        # 基础功能选项卡
        basic_tab = QWidget()
        basic_layout = QVBoxLayout(basic_tab)
        
        # 技能加速卡片
        attack_card = FeatureCard(
            "技能加速控制", 
            "修改角色的技能施放速度，提高DPS和战斗效率"
        )
        
        self.attack_speed_check = QCheckBox("启用技能加速")
        self.attack_speed_check.toggled.connect(self.toggle_attack_speed)
        attack_card.buttons_layout.addWidget(self.attack_speed_check)
        
        speed_form = QFormLayout()
        self.attack_speed_value = QLineEdit("1000")
        self.attack_speed_value.setPlaceholderText("输入速度值")
        self.attack_speed_value.setMaximumWidth(100)
        speed_form.addRow("加速值:", self.attack_speed_value)
        
        attack_layout = QVBoxLayout()
        attack_layout.addWidget(self.attack_speed_check)
        attack_layout.addLayout(speed_form)
        
        attack_card_container = QVBoxLayout()
        attack_card_container.addLayout(attack_layout)
        attack_card.buttons_layout.addLayout(attack_card_container)
        
        basic_layout.addWidget(attack_card)
        
        # 移动加速卡片
        move_card = FeatureCard(
            "移动加速控制", 
            "提高角色的移动速度，快速穿越地图和躲避危险"
        )
        
        self.move_speed_check = QCheckBox("启用移动加速")
        self.move_speed_check.toggled.connect(self.toggle_move_speed)
        
        move_form = QFormLayout()
        self.move_speed_value = QLineEdit("20.0")
        self.move_speed_value.setPlaceholderText("输入速度值")
        self.move_speed_value.setMaximumWidth(100)
        move_form.addRow("速度值:", self.move_speed_value)
        
        move_layout = QVBoxLayout()
        move_layout.addWidget(self.move_speed_check)
        move_layout.addLayout(move_form)
        
        move_card_container = QVBoxLayout()
        move_card_container.addLayout(move_layout)
        move_card.buttons_layout.addLayout(move_card_container)
        
        basic_layout.addWidget(move_card)
        
        # 添加伸缩项以将卡片推到顶部
        basic_layout.addStretch()
        
        # 高级功能选项卡
        advanced_tab = QWidget()
        advanced_layout = QVBoxLayout(advanced_tab)
        
        # 锁空功能卡片
        air_card = FeatureCard(
            "锁空控制", 
            "让角色固定在空中，可用于躲避地面危险或突破地图限制"
        )
        
        self.air_lock_check = QCheckBox("启用锁空功能")
        self.air_lock_check.toggled.connect(self.toggle_air_lock)
        
        air_layout = QVBoxLayout()
        air_layout.addWidget(self.air_lock_check)
        
        hotkey_label = QLabel("热键: Insert - 锁定, Delete - 解除")
        hotkey_label.setStyleSheet("color: #A0A0A0;")
        air_layout.addWidget(hotkey_label)
        
        air_card_container = QVBoxLayout()
        air_card_container.addLayout(air_layout)
        air_card.buttons_layout.addLayout(air_card_container)
        
        advanced_layout.addWidget(air_card)
        
        # 飞天功能卡片
        fly_card = FeatureCard(
            "飞天控制", 
            "允许角色在空中自由升降，突破地形限制"
        )
        
        self.fly_check = QCheckBox("启用飞天功能")
        self.fly_check.toggled.connect(self.toggle_fly)
        
        fly_form = QFormLayout()
        self.fly_value = QLineEdit("5.0")
        self.fly_value.setPlaceholderText("输入高度增量")
        self.fly_value.setMaximumWidth(100)
        fly_form.addRow("高度增量:", self.fly_value)
        
        fly_buttons = QHBoxLayout()
        self.fly_up_btn = QPushButton("上升 (PgUp)")
        self.fly_up_btn.clicked.connect(self.fly_up)
        self.fly_up_btn.setEnabled(False)
        
        self.fly_down_btn = QPushButton("下降 (PgDn)")
        self.fly_down_btn.clicked.connect(self.fly_down)
        self.fly_down_btn.setEnabled(False)
        
        fly_buttons.addWidget(self.fly_up_btn)
        fly_buttons.addWidget(self.fly_down_btn)
        
        fly_layout = QVBoxLayout()
        fly_layout.addWidget(self.fly_check)
        fly_layout.addLayout(fly_form)
        fly_layout.addLayout(fly_buttons)
        
        fly_card_container = QVBoxLayout()
        fly_card_container.addLayout(fly_layout)
        fly_card.buttons_layout.addLayout(fly_card_container)
        
        advanced_layout.addWidget(fly_card)
        
        # 瞬移加速功能卡片
        teleport_speed_card = FeatureCard(
            "瞬移加速控制", 
            "提高角色的瞬移速度，可快速在地图间穿梭"
        )

        self.teleport_speed_check = QCheckBox("启用瞬移加速")
        self.teleport_speed_check.toggled.connect(self.toggle_teleport_speed)

        teleport_speed_form = QFormLayout()
        self.teleport_speed_value = QLineEdit("1856.65")
        self.teleport_speed_value.setPlaceholderText("输入瞬移速度值")
        self.teleport_speed_value.setMaximumWidth(100)
        teleport_speed_form.addRow("速度值:", self.teleport_speed_value)

        teleport_speed_layout = QVBoxLayout()
        teleport_speed_layout.addWidget(self.teleport_speed_check)
        teleport_speed_layout.addLayout(teleport_speed_form)

        teleport_speed_card_container = QVBoxLayout()
        teleport_speed_card_container.addLayout(teleport_speed_layout)
        teleport_speed_card.buttons_layout.addLayout(teleport_speed_card_container)

        basic_layout.addWidget(teleport_speed_card)

        # 视野扩展功能卡片
        view_range_card = FeatureCard(
            "视野扩展控制", 
            "扩大游戏视野范围，获得更广阔的视角"
        )

        self.view_range_check = QCheckBox("启用视野扩展")
        self.view_range_check.toggled.connect(self.toggle_view_range)

        view_range_form = QFormLayout()
        self.view_range_value = QLineEdit("60")
        self.view_range_value.setPlaceholderText("输入视野范围值")
        self.view_range_value.setMaximumWidth(100)
        view_range_form.addRow("视野值:", self.view_range_value)

        view_range_layout = QVBoxLayout()
        view_range_layout.addWidget(self.view_range_check)
        view_range_layout.addLayout(view_range_form)

        view_range_card_container = QVBoxLayout()
        view_range_card_container.addLayout(view_range_layout)
        view_range_card.buttons_layout.addLayout(view_range_card_container)

        advanced_layout.addWidget(view_range_card)
        
        # 翅膀无CD功能卡片
        wing_nocd_card = FeatureCard(
            "翅膀无CD控制", 
            "移除翅膀技能冷却时间，可以连续使用翅膀"
        )
        
        self.wing_nocd_check = QCheckBox("启用翅膀无CD")
        self.wing_nocd_check.toggled.connect(self.toggle_wing_nocd)
        
        wing_nocd_layout = QVBoxLayout()
        wing_nocd_layout.addWidget(self.wing_nocd_check)
        
        wing_nocd_card_container = QVBoxLayout()
        wing_nocd_card_container.addLayout(wing_nocd_layout)
        wing_nocd_card.buttons_layout.addLayout(wing_nocd_card_container)
        
        advanced_layout.addWidget(wing_nocd_card)
        
        # 添加伸缩项
        advanced_layout.addStretch()
        
        # 其他功能选项卡
        other_tab = QWidget()
        other_layout = QVBoxLayout(other_tab)
        
        # 显血功能卡片
        show_hp_card = FeatureCard(
            "显血控制", 
            "显示所有单位的血量，包括被隐藏的血量"
        )
        
        self.show_hp_check = QCheckBox("启用显血功能")
        self.show_hp_check.toggled.connect(self.toggle_show_hp)
        
        show_hp_layout = QVBoxLayout()
        show_hp_layout.addWidget(self.show_hp_check)
        
        show_hp_card_container = QVBoxLayout()
        show_hp_card_container.addLayout(show_hp_layout)
        show_hp_card.buttons_layout.addLayout(show_hp_card_container)
        
        other_layout.addWidget(show_hp_card)
        
        # 地图扩展功能卡片
        map_extend_card = FeatureCard(
            "地图扩展控制", 
            "扩大游戏小地图显示范围，获得更广阔的地图视图"
        )
        
        self.map_extend_check = QCheckBox("启用地图扩展")
        self.map_extend_check.toggled.connect(self.toggle_map_extend)
        
        map_extend_form = QFormLayout()
        self.map_extend_value = QLineEdit("150")
        self.map_extend_value.setPlaceholderText("输入地图范围值")
        self.map_extend_value.setMaximumWidth(100)
        map_extend_form.addRow("范围值:", self.map_extend_value)
        
        map_extend_layout = QVBoxLayout()
        map_extend_layout.addWidget(self.map_extend_check)
        map_extend_layout.addLayout(map_extend_form)
        
        map_extend_card_container = QVBoxLayout()
        map_extend_card_container.addLayout(map_extend_layout)
        map_extend_card.buttons_layout.addLayout(map_extend_card_container)
        
        other_layout.addWidget(map_extend_card)
        
        # 坐标传送功能卡片 - 精简设计
        teleport_card = FeatureCard(
            "坐标传送", 
            "保存和读取游戏中的坐标位置，快速传送到指定地点"
        )
        
        # 创建紧凑型布局的坐标功能界面
        coords_tab = QTabWidget()
        coords_tab.setStyleSheet("QTabWidget::pane { border: none; }")
        coords_tab.setMinimumHeight(400)  # 适当增加高度确保有足够空间
        
        # ---- 传送控制选项卡 - 紧凑对称设计 ----
        coords_page = QWidget()
        coords_layout = QVBoxLayout(coords_page)
        coords_layout.setContentsMargins(5, 5, 5, 5)
        coords_layout.setSpacing(5)  # 减少整体间距

        # 创建坐标输入框的通用样式 - 稍微减小字体大小和内边距
        coord_style = """
        QLineEdit {
            font-size: 11pt;
            font-weight: bold;
            padding: 2px;
            border: 1px solid #AAA;
            border-radius: 4px;
            background-color: #F5F5F5;
        }
        """

        # 使用表格布局设计更紧凑的坐标区域
        coords_container = QWidget()
        coords_container.setMaximumHeight(250)  # 限制最大高度
        coords_container_layout = QHBoxLayout(coords_container)
        coords_container_layout.setContentsMargins(0, 0, 0, 0)
        coords_container_layout.setSpacing(15)  # 左右分组之间的间距

        # 左侧当前坐标组
        current_coords_group = QGroupBox("当前坐标")
        current_layout = QVBoxLayout(current_coords_group)
        current_layout.setSpacing(6)

        current_form = QFormLayout()
        current_form.setSpacing(5)
        current_form.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        current_form.setVerticalSpacing(10)

        self.current_x = QLineEdit("0")
        self.current_x.setReadOnly(True)
        self.current_x.setFixedWidth(90)  # 稍微减小宽度
        self.current_x.setFixedHeight(30)
        self.current_x.setStyleSheet(coord_style + "QLineEdit { background-color: #E8E8E8; }")
        self.current_x.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.current_y = QLineEdit("0")
        self.current_y.setReadOnly(True)
        self.current_y.setFixedWidth(90)
        self.current_y.setFixedHeight(30)
        self.current_y.setStyleSheet(coord_style + "QLineEdit { background-color: #E8E8E8; }")
        self.current_y.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.current_z = QLineEdit("0")
        self.current_z.setReadOnly(True)
        self.current_z.setFixedWidth(90)
        self.current_z.setFixedHeight(30)
        self.current_z.setStyleSheet(coord_style + "QLineEdit { background-color: #E8E8E8; }")
        self.current_z.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 使用粗体标签
        x_label = QLabel("X坐标:")
        x_label.setStyleSheet("font-weight: bold;")
        y_label = QLabel("Y坐标:")
        y_label.setStyleSheet("font-weight: bold;")
        z_label = QLabel("Z坐标:")
        z_label.setStyleSheet("font-weight: bold;")

        current_form.addRow(x_label, self.current_x)
        current_form.addRow(y_label, self.current_y)
        current_form.addRow(z_label, self.current_z)
        current_layout.addLayout(current_form)
        
        self.get_coords_btn = QPushButton("获取当前坐标")
        self.get_coords_btn.clicked.connect(self.get_current_coordinates)
        self.get_coords_btn.setEnabled(False)
        self.get_coords_btn.setFixedHeight(30)
        self.get_coords_btn.setStyleSheet("font-weight: bold;")
        current_layout.addWidget(self.get_coords_btn)
        
        # 右侧目标坐标组
        target_coords_group = QGroupBox("目标坐标")
        target_layout = QVBoxLayout(target_coords_group)
        target_layout.setSpacing(6)

        target_form = QFormLayout()
        target_form.setSpacing(5)
        target_form.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        target_form.setVerticalSpacing(10)

        self.target_x = QLineEdit("0")
        self.target_x.setFixedWidth(90)
        self.target_x.setFixedHeight(30)
        self.target_x.setStyleSheet(coord_style)
        self.target_x.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.target_y = QLineEdit("0")
        self.target_y.setFixedWidth(90)
        self.target_y.setFixedHeight(30)
        self.target_y.setStyleSheet(coord_style)
        self.target_y.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.target_z = QLineEdit("0")
        self.target_z.setFixedWidth(90)
        self.target_z.setFixedHeight(30)
        self.target_z.setStyleSheet(coord_style)
        self.target_z.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 使用粗体标签 - 与左侧保持一致
        x_label2 = QLabel("X坐标:")
        x_label2.setStyleSheet("font-weight: bold;")
        y_label2 = QLabel("Y坐标:")
        y_label2.setStyleSheet("font-weight: bold;")
        z_label2 = QLabel("Z坐标:")
        z_label2.setStyleSheet("font-weight: bold;")

        target_form.addRow(x_label2, self.target_x)
        target_form.addRow(y_label2, self.target_y)
        target_form.addRow(z_label2, self.target_z)
        target_layout.addLayout(target_form)
        
        self.teleport_btn = QPushButton("传送到坐标")
        self.teleport_btn.clicked.connect(self.execute_teleport)
        self.teleport_btn.setEnabled(False)
        self.teleport_btn.setStyleSheet(AppStyles().warning_button + "font-weight: bold;")
        self.teleport_btn.setFixedHeight(30)
        target_layout.addWidget(self.teleport_btn)
        
        # 添加两个坐标组到容器
        coords_container_layout.addWidget(current_coords_group)
        coords_container_layout.addWidget(target_coords_group)
        coords_layout.addWidget(coords_container)

        # 添加分隔符
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        coords_layout.addWidget(separator)

        # 其余快速保存/读取区域和已保存坐标列表保持不变
        # ...

        # 添加快速保存/读取区域
        quick_layout = QHBoxLayout()
        quick_layout.setSpacing(8)

        # 快速保存区
        quick_save_group = QGroupBox("快速保存")
        quick_save_layout = QHBoxLayout(quick_save_group)
        quick_save_layout.setContentsMargins(8, 8, 8, 8)
        quick_save_layout.setSpacing(8)

        self.coord_name = QLineEdit()
        self.coord_name.setPlaceholderText("输入坐标名称")
        self.coord_name.setMinimumWidth(120)
        quick_save_layout.addWidget(self.coord_name)
        
        self.save_coords_btn = QPushButton("保存")
        self.save_coords_btn.clicked.connect(self.save_coordinates)
        self.save_coords_btn.setEnabled(False)
        self.save_coords_btn.setFixedHeight(28)
        self.save_coords_btn.setFixedWidth(60)
        quick_save_layout.addWidget(self.save_coords_btn)

        quick_layout.addWidget(quick_save_group)

        # 快速加载区
        quick_load_group = QGroupBox("快速加载")
        quick_load_layout = QHBoxLayout(quick_load_group)
        quick_load_layout.setContentsMargins(8, 8, 8, 8)
        quick_load_layout.setSpacing(8)
        
        self.saved_coords_combo = QComboBox()
        self.saved_coords_combo.setPlaceholderText("选择保存的坐标...")
        self.saved_coords_combo.setMinimumWidth(120)
        quick_load_layout.addWidget(self.saved_coords_combo)
        
        self.load_coords_btn = QPushButton("加载")
        self.load_coords_btn.clicked.connect(self.load_saved_coordinate)
        self.load_coords_btn.setEnabled(False)
        self.load_coords_btn.setFixedHeight(28)
        self.load_coords_btn.setFixedWidth(60)
        quick_load_layout.addWidget(self.load_coords_btn)

        quick_layout.addWidget(quick_load_group)
        coords_layout.addLayout(quick_layout)

        # 添加已保存坐标列表
        saved_list_group = QGroupBox("已保存坐标列表")
        saved_list_layout = QVBoxLayout(saved_list_group)
        saved_list_layout.setContentsMargins(8, 8, 8, 8)
        saved_list_layout.setSpacing(5)

        # 添加ListView显示已保存坐标
        self.saved_coords_list = QListWidget()
        self.saved_coords_list.setSelectionMode(QListWidget.SelectionMode.SingleSelection)
        self.saved_coords_list.itemSelectionChanged.connect(self.on_saved_coords_selection)
        saved_list_layout.addWidget(self.saved_coords_list)

        # 操作按钮
        list_btns_layout = QHBoxLayout()
        list_btns_layout.setSpacing(5)

        self.delete_coord_btn = QPushButton("删除坐标")
        self.delete_coord_btn.clicked.connect(self.delete_saved_coordinate)
        self.delete_coord_btn.setEnabled(False)
        self.delete_coord_btn.setFixedHeight(28)
        list_btns_layout.addWidget(self.delete_coord_btn)

        list_btns_layout.addStretch()

        self.load_from_list_btn = QPushButton("加载选中")
        self.load_from_list_btn.clicked.connect(self.load_from_list)
        self.load_from_list_btn.setEnabled(False)
        self.load_from_list_btn.setFixedHeight(28)
        list_btns_layout.addWidget(self.load_from_list_btn)

        saved_list_layout.addLayout(list_btns_layout)
        coords_layout.addWidget(saved_list_group)

        # 将整个布局添加到卡片中
        teleport_card_container = QVBoxLayout()
        teleport_card_container.addWidget(coords_tab)
        teleport_card.buttons_layout.addLayout(teleport_card_container)

        # 添加标签页到选项卡
        coords_tab.addTab(coords_page, "坐标传送")
        
        other_layout.addWidget(teleport_card)
        
        # 反隐功能卡片
        stealth_card = FeatureCard(
            "反隐控制", 
            "显示隐形单位，防止被潜行职业偷袭"
        )
        
        self.stealth_check = QCheckBox("启用反隐功能")
        self.stealth_check.toggled.connect(self.toggle_stealth)
        
        stealth_layout = QVBoxLayout()
        stealth_layout.addWidget(self.stealth_check)
        
        stealth_card_container = QVBoxLayout()
        stealth_card_container.addLayout(stealth_layout)
        stealth_card.buttons_layout.addLayout(stealth_card_container)
        
        basic_layout.addWidget(stealth_card)
        
        # 添加伸缩项
        other_layout.addStretch()
        
        # 创建安全功能标签页
        security_tab = QWidget()
        security_layout = QVBoxLayout(security_tab)
        
        # 攻击速度微调功能卡片
        skill_speed_adjust_card = FeatureCard(
            "攻击速度微调", 
            "精确调整技能施放速度，避免服务器限制检测"
        )
        
        self.skill_speed_adjust_check = QCheckBox("启用攻击速度微调")
        self.skill_speed_adjust_check.toggled.connect(self.toggle_skill_speed_adjust)
        
        skill_speed_adjust_form = QFormLayout()
        self.skill_speed_adjust_value = QLineEdit("1000")
        self.skill_speed_adjust_value.setPlaceholderText("输入速度值")
        self.skill_speed_adjust_value.setMaximumWidth(100)
        skill_speed_adjust_form.addRow("速度值:", self.skill_speed_adjust_value)
        
        skill_speed_adjust_layout = QVBoxLayout()
        skill_speed_adjust_layout.addWidget(self.skill_speed_adjust_check)
        skill_speed_adjust_layout.addLayout(skill_speed_adjust_form)
        
        skill_speed_adjust_card_container = QVBoxLayout()
        skill_speed_adjust_card_container.addLayout(skill_speed_adjust_layout)
        skill_speed_adjust_card.buttons_layout.addLayout(skill_speed_adjust_card_container)
        
        security_layout.addWidget(skill_speed_adjust_card)
        
        # QSK秒起功能卡片
        qsk_card = FeatureCard(
            "QSK秒起功能", 
            "启用QSK秒起，快速释放技能"
        )
        
        self.qsk_check = QCheckBox("启用QSK秒起")
        self.qsk_check.toggled.connect(self.toggle_qsk)
        
        qsk_layout = QVBoxLayout()
        qsk_layout.addWidget(self.qsk_check)
        
        qsk_card_container = QVBoxLayout()
        qsk_card_container.addLayout(qsk_layout)
        qsk_card.buttons_layout.addLayout(qsk_card_container)
        
        security_layout.addWidget(qsk_card)
        
        # 攻击距离功能卡片
        attack_range_card = FeatureCard(
            "攻击距离控制", 
            "增加攻击距离，提高战斗范围"
        )
        
        self.attack_range_check = QCheckBox("启用攻击距离增加")
        self.attack_range_check.toggled.connect(self.toggle_attack_range)
        
        attack_range_form = QFormLayout()
        self.attack_range_value = QLineEdit("10")
        self.attack_range_value.setPlaceholderText("输入距离值")
        self.attack_range_value.setMaximumWidth(100)
        attack_range_form.addRow("距离值:", self.attack_range_value)
        
        attack_range_layout = QVBoxLayout()
        attack_range_layout.addWidget(self.attack_range_check)
        attack_range_layout.addLayout(attack_range_form)
        
        attack_range_card_container = QVBoxLayout()
        attack_range_card_container.addLayout(attack_range_layout)
        attack_range_card.buttons_layout.addLayout(attack_range_card_container)
        
        security_layout.addWidget(attack_range_card)
        
        # 添加伸缩项
        security_layout.addStretch()
        
        # 创建副本辅助选项卡
        dungeon_tab = QWidget()
        dungeon_layout = QVBoxLayout(dungeon_tab)
        
        # 副本辅助功能卡片
        dungeon_card = FeatureCard(
            "副本辅助控制", 
            "自动战斗和副本辅助功能"
        )
        
        # 创建副本辅助控制区域
        dungeon_control_layout = QVBoxLayout()
        
        # 添加怪物选择区域
        monster_group = QGroupBox("怪物选择")
        monster_layout = QVBoxLayout(monster_group)
        
        self.monster_list = QListWidget()
        self.monster_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        monster_layout.addWidget(self.monster_list)
        
        # 添加刷新怪物列表按钮
        refresh_monster_btn = QPushButton("刷新怪物列表")
        refresh_monster_btn.clicked.connect(self.refresh_monster_list)
        monster_layout.addWidget(refresh_monster_btn)
        
        dungeon_control_layout.addWidget(monster_group)
        
        # 添加战斗设置区域
        battle_group = QGroupBox("战斗设置")
        battle_layout = QVBoxLayout(battle_group)
        
        # 添加自动战斗选项
        self.auto_attack_check = QCheckBox("自动攻击")
        self.auto_attack_check.setChecked(True)
        battle_layout.addWidget(self.auto_attack_check)
        
        self.auto_loot_check = QCheckBox("自动拾取")
        self.auto_loot_check.setChecked(True)
        battle_layout.addWidget(self.auto_loot_check)
        
        self.auto_potion_check = QCheckBox("自动使用药水")
        self.auto_potion_check.setChecked(True)
        battle_layout.addWidget(self.auto_potion_check)
        
        # 添加战斗日志区域
        battle_log_label = QLabel("战斗日志:")
        battle_layout.addWidget(battle_log_label)
        
        self.battle_log = QTextEdit()
        self.battle_log.setReadOnly(True)
        self.battle_log.setMaximumHeight(150)
        battle_layout.addWidget(self.battle_log)
        
        dungeon_control_layout.addWidget(battle_group)
        
        # 添加控制按钮
        control_layout = QHBoxLayout()
        
        self.start_auto_battle_btn = QPushButton("开始副本辅助")
        self.start_auto_battle_btn.clicked.connect(self.toggle_auto_battle)
        self.start_auto_battle_btn.setStyleSheet(AppStyles().success_button)
        control_layout.addWidget(self.start_auto_battle_btn)
        
        self.stop_auto_battle_btn = QPushButton("停止副本辅助")
        self.stop_auto_battle_btn.clicked.connect(self.toggle_auto_battle)
        self.stop_auto_battle_btn.setEnabled(False)
        self.stop_auto_battle_btn.setStyleSheet(AppStyles().danger_button)
        control_layout.addWidget(self.stop_auto_battle_btn)
        
        dungeon_control_layout.addLayout(control_layout)
        
        # 添加到卡片
        dungeon_card_container = QVBoxLayout()
        dungeon_card_container.addLayout(dungeon_control_layout)
        dungeon_card.buttons_layout.addLayout(dungeon_card_container)
        
        dungeon_layout.addWidget(dungeon_card)
        dungeon_layout.addStretch()
        
        # 添加副本辅助选项卡
        tabs.addTab(dungeon_tab, "副本辅助")
        
        # 创建选项卡
        tabs.addTab(basic_tab, "基础功能")
        tabs.addTab(advanced_tab, "高级功能")
        tabs.addTab(other_tab, "其他功能")
        tabs.addTab(security_tab, "安全功能")
        
        # 将左侧面板和选项卡添加到分割器
        splitter.addWidget(left_panel)
        splitter.addWidget(tabs)
        
        # 设置分割比例
        splitter.setStretchFactor(0, 1)  # 左侧占1份
        splitter.setStretchFactor(1, 3)  # 右侧占3份
        
        # 这里是错误的地方：应该用addWidget而不是addLayout
        main_layout.addWidget(splitter)  # 修改这行，将addLayout改为addWidget
        
        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪 - 请选择游戏进程")
        
        # 初始化界面
        self.refresh_processes()
        
        # 创建定时器更新状态
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_ui_status)
        self.update_timer.start(1000)  # 每秒更新一次
        
        # 自动搜索并加载aion进程
        QTimer.singleShot(500, self.auto_find_aion_process)
    
    def refresh_processes(self):
        """刷新进程列表"""
        self.process_combo.clear()
        process_list = []
        
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                process_list.append(f"{proc.name()} ({proc.pid})")
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
        
        self.process_combo.addItems(process_list)
        self.status_bar.showMessage("进程列表已更新")
    
    def load_game(self):
        """加载游戏进程"""
        try:
            selected_process = self.process_combo.currentText()
            if not selected_process:
                self.show_error("选择错误", "请先选择一个游戏进程")
                return
                
            # 解析进程ID
            try:
                pid_part = selected_process.split('(')[1].strip(')')
                pid = int(pid_part)
                process_name = selected_process.split('(')[0].strip()
            except (IndexError, ValueError) as e:
                self.show_error("解析错误", f"无法解析进程ID: {str(e)}")
                return
                
            logger.info(f"尝试加载进程: {process_name} (PID: {pid})")
            self.status_bar.showMessage(f"正在加载进程: {process_name} (PID: {pid})...")
            
            # 尝试打开进程
            if self.memory.open_process(pid):
                self.process_id = pid
                
                # 获取模块基址
                logger.info(f"尝试获取模块 Game.dll 的基址")
                self.status_bar.showMessage(f"正在获取模块基址: Game.dll...")
                
                self.module_base = self.memory.get_module_base("Game.dll")
                if self.module_base == 0:
                    logger.error(f"无法获取模块 Game.dll 的基址")
                    
                    # 尝试列出所有可用模块
                    self.status_bar.showMessage(f"警告: 无法找到模块 Game.dll，尝试列出所有模块...")
                    
                    try:
                        process = psutil.Process(pid)
                        modules = process.memory_maps()
                        
                        module_list = "找不到指定模块，可用模块列表:\n"
                        for i, module in enumerate(modules):
                            module_name = os.path.basename(module.path)
                            module_list += f"{i+1}. {module_name}\n"
                            if i >= 9:  # 只显示前10个
                                module_list += "...更多模块省略...\n"
                                break
                        
                        self.show_error("模块错误", f"无法找到模块: Game.dll\n\n{module_list}")
                        
                        # 提示用户输入正确的模块名称
                        from PyQt6.QtWidgets import QInputDialog
                        new_module, ok = QInputDialog.getText(
                            self, "模块名称", "输入正确的游戏模块名称:", 
                            text="Game.dll"
                        )
                        
                        if ok and new_module:
                            self.module_base = self.memory.get_module_base(new_module)
                            if self.module_base == 0:
                                self.show_error("模块错误", f"仍然无法找到模块: {new_module}")
                                self.memory.close_process()
                                self.process_id = None
                                return
                        else:
                            self.memory.close_process()
                            self.process_id = None
                            return
                            
                    except Exception as e:
                        logger.error(f"列出模块时出错: {str(e)}")
                        self.show_error("模块错误", f"无法找到模块 Game.dll，且列出可用模块失败")
                        self.memory.close_process()
                        self.process_id = None
                        return
                
                # 获取游戏版本信息
                logger.info(f"获取游戏版本信息")
                self.current_version = self.version_combo.currentText()
                
                # 更新偏移量
                logger.info("更新偏移量...")
                self.update_offsets()
                
                # 更新界面状态
                self.process_status.setText("已连接")
                self.process_status.setStyleSheet("color: #00E676; font-weight: bold;")
                self.status_bar.showMessage(f"已加载: {process_name} (PID: {pid}) - 模块基址: 0x{self.module_base:X}")
                
                # 启用功能按钮
                self.enable_feature_controls(True)
                
                # 显示成功消息
                self.show_info("加载成功", f"成功加载游戏进程: {process_name}\n模块基址: 0x{self.module_base:X}")
                
                # 更新诊断信息
                self.diag_text.setText(
                    f"进程: {process_name} (PID: {pid})\n"
                    f"模块基址: 0x{self.module_base:X}\n"
                    f"游戏版本: {self.current_version}"
                )
                
            else:
                logger.error(f"无法打开进程 {process_name} (PID: {pid})")
                self.show_error("打开进程失败", 
                    f"无法打开进程 {process_name} (PID: {pid})\n\n"
                    f"可能原因:\n"
                    f"1. 进程受保护\n"
                    f"2. 杀毒软件拦截\n")
        except Exception as e:
            logger.error(f"加载游戏时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error("意外错误", f"加载游戏时发生未预期的错误:\n{str(e)}")
    
    def update_offsets(self):
        """更新游戏偏移量"""
        if not self.current_version or self.current_version not in self.game_versions:
            logger.error("未选择有效的游戏版本")
            return
            
        version_data = self.game_versions[self.current_version]
        for key, value in version_data.items():
            if key in self.offsets:
                self.offsets[key] = int(value, 16)
                
        logger.info(f"已更新偏移量: {self.offsets}")
    
    def enable_feature_controls(self, enabled):
        """启用或禁用功能控件"""
        # 基础功能
        self.attack_speed_check.setEnabled(enabled)
        self.attack_speed_value.setEnabled(enabled)
        self.move_speed_check.setEnabled(enabled)
        self.move_speed_value.setEnabled(enabled)
        
        # 高级功能
        self.air_lock_check.setEnabled(enabled)
        self.fly_check.setEnabled(enabled)
        self.fly_value.setEnabled(enabled)
        self.fly_up_btn.setEnabled(enabled and self.features_active["fly"])
        self.fly_down_btn.setEnabled(enabled and self.features_active["fly"])
        self.teleport_speed_check.setEnabled(enabled)
        self.teleport_speed_value.setEnabled(enabled)
        self.view_range_check.setEnabled(enabled)
        self.view_range_value.setEnabled(enabled)
        self.wing_nocd_check.setEnabled(enabled)
        self.skill_speed_adjust_check.setEnabled(enabled)
        self.skill_speed_adjust_value.setEnabled(enabled)
        self.qsk_check.setEnabled(enabled)
        self.attack_range_check.setEnabled(enabled)
        self.attack_range_value.setEnabled(enabled)
        
        # 其他功能
        self.stealth_check.setEnabled(enabled)
        self.show_hp_check.setEnabled(enabled)
        self.map_extend_check.setEnabled(enabled)
        self.map_extend_value.setEnabled(enabled)
        self.get_coords_btn.setEnabled(enabled)
        self.teleport_btn.setEnabled(enabled)
        self.save_coords_btn.setEnabled(enabled)
        self.load_coords_btn.setEnabled(enabled)
        
        # 副本辅助功能
        self.auto_attack_check.setEnabled(enabled)
        self.auto_loot_check.setEnabled(enabled)
        self.auto_potion_check.setEnabled(enabled)
        self.start_auto_battle_btn.setEnabled(enabled)
        self.stop_auto_battle_btn.setEnabled(enabled)
        
        # 重置复选框状态
        if not enabled:
            self.attack_speed_check.setChecked(False)
            self.move_speed_check.setChecked(False)
            self.air_lock_check.setChecked(False)
            self.fly_check.setChecked(False)
            self.stealth_check.setChecked(False)
            self.teleport_speed_check.setChecked(False)
            self.view_range_check.setChecked(False)
            self.show_hp_check.setChecked(False)
            self.map_extend_check.setChecked(False)
            self.wing_nocd_check.setChecked(False)
    
    def update_ui_status(self):
        """更新UI状态"""
        # 检查进程是否还在运行
        if self.process_id:
            try:
                process = psutil.Process(self.process_id)
                if process.is_running():
                    # 进程还在运行，保持连接状态
                    pass
                else:
                    # 进程已终止，关闭连接
                    self.memory.close_process()
                    self.process_id = None
                    self.module_base = 0
                    self.process_status.setText("连接断开")
                    self.process_status.setStyleSheet("color: #FF1744; font-weight: bold;")
                    self.status_bar.showMessage("游戏进程已终止，连接已断开")
                    self.enable_feature_controls(False)
                    self.diag_text.setText("连接已断开，请重新连接游戏进程")
            except psutil.NoSuchProcess:
                # 进程不存在，关闭连接
                self.memory.close_process()
                self.process_id = None
                self.module_base = 0
                self.process_status.setText("连接断开")
                self.process_status.setStyleSheet("color: #FF1744; font-weight: bold;")
                self.status_bar.showMessage("游戏进程已终止，连接已断开")
                self.enable_feature_controls(False)
                self.diag_text.setText("连接已断开，请重新连接游戏进程")
    
    def toggle_attack_speed(self, checked):
        """切换技能加速状态"""
        if not self.process_id or not self.module_base:
            self.attack_speed_check.setChecked(False)
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        self.features_active["attack_speed"] = checked
        
        if checked:
            try:
                speed_value = int(self.attack_speed_value.text())
                
                # 获取攻速地址
                attack_speed_offset = int(self.game_versions[self.current_version]["attack_speed_data"], 16)
                attack_speed_addr = self.module_base + attack_speed_offset
                
                # 构造攻速修改指令
                speed_bytes = speed_value.to_bytes(2, byteorder='little')
                patch_bytes = bytearray([0x66, 0xB8]) + speed_bytes + bytearray([0x90, 0x90, 0x90])
                
                # 写入内存
                result = self.memory.write_bytes(attack_speed_addr, patch_bytes)
                
                if result:
                    self.status_bar.showMessage(f"技能加速已启动: {speed_value}")
                    logger.info(f"技能加速已启用，值: {speed_value}")
                else:
                    self.attack_speed_check.setChecked(False)
                    self.show_error("写入失败", "无法修改内存，可能是权限不足")
                    
            except ValueError:
                self.attack_speed_check.setChecked(False)
                self.show_error("输入错误", "请输入有效的速度值")
        else:
            # 关闭技能加速，恢复原始值
            self.status_bar.showMessage("技能加速已关闭")
            logger.info("技能加速已关闭")
            
            # 这里应该添加恢复原始指令的代码
    
    def toggle_move_speed(self, checked):
        """切换移动加速状态"""
        if not self.process_id or not self.module_base:
            self.move_speed_check.setChecked(False)
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        self.features_active["move_speed"] = checked
        
        if checked:
            try:
                speed_value = float(self.move_speed_value.text())
                
                # 创建定时器，持续更新移动速度
                if "move_speed" not in self.timers:
                    self.timers["move_speed"] = QTimer(self)
                    self.timers["move_speed"].timeout.connect(self.update_move_speed)
                
                self.timers["move_speed"].start(100)  # 每100ms更新一次
                self.status_bar.showMessage(f"移动加速已启动: {speed_value}")
                logger.info(f"移动加速已启用，值: {speed_value}")
                
            except ValueError:
                self.move_speed_check.setChecked(False)
                self.show_error("输入错误", "请输入有效的速度值")
        else:
            # 关闭移动加速
            if "move_speed" in self.timers:
                self.timers["move_speed"].stop()
            
            self.status_bar.showMessage("移动加速已关闭")
            logger.info("移动加速已关闭")
    
    def update_move_speed(self):
        """定时更新移动速度"""
        if not self.process_id or not self.features_active["move_speed"]:
            return
            
        try:
            speed_value = float(self.move_speed_value.text())
            
            # 获取人物基址
            base_offset = int(self.game_versions[self.current_version]["base_offset"], 16)
            level1_offset = int(self.game_versions[self.current_version]["level1_offset"], 16)
            level2_offset = int(self.game_versions[self.current_version]["level2_offset"], 16)
            level3_offset = int(self.game_versions[self.current_version]["level3_offset"], 16)
            character_offset = int(self.game_versions[self.current_version]["character_base"], 16)
            move_speed_offset = int(self.game_versions[self.current_version]["move_speed"], 16)
            
            # 读取多级指针
            base_addr = self.memory.read_long(self.module_base + base_offset)
            if not base_addr:
                return
                
            level1_addr = self.memory.read_long(base_addr + level1_offset)
            if not level1_addr:
                return
                
            level2_addr = self.memory.read_long(level1_addr + level2_offset)
            if not level2_addr:
                return
                
            level3_addr = self.memory.read_long(level2_addr + level3_offset)
            if not level3_addr:
                return
                
            character_addr = self.memory.read_long(level3_addr + character_offset)
            if not character_addr:
                return
                
            move_speed_addr = character_addr + move_speed_offset
            
            # 写入移动速度
            self.memory.write_float(move_speed_addr, speed_value)
            
        except Exception as e:
            logger.error(f"更新移动速度时出错: {str(e)}")
            # 出错时停止计时器
            if "move_speed" in self.timers:
                self.timers["move_speed"].stop()
            self.move_speed_check.setChecked(False)
    
    def toggle_air_lock(self, checked):
        """切换锁空状态"""
        if not self.process_id or not self.module_base:
            self.air_lock_check.setChecked(False)
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        self.features_active["air_lock"] = checked
        
        if checked:
            # 绑定热键
            self.key_binding_insert = QAction("Lock Air", self)
            self.key_binding_insert.setShortcut("Insert")
            self.key_binding_insert.triggered.connect(self.lock_air)
            self.addAction(self.key_binding_insert)
            
            self.key_binding_delete = QAction("Unlock Air", self)
            self.key_binding_delete.setShortcut("Delete")
            self.key_binding_delete.triggered.connect(self.unlock_air)
            self.addAction(self.key_binding_delete)
            
            self.status_bar.showMessage("锁空功能已启用，按Insert锁定，Delete解除")
            logger.info("锁空功能已启用")
        else:
            # 解除热键绑定
            if hasattr(self, 'key_binding_insert'):
                self.removeAction(self.key_binding_insert)
            if hasattr(self, 'key_binding_delete'):
                self.removeAction(self.key_binding_delete)
                
            self.status_bar.showMessage("锁空功能已关闭")
            logger.info("锁空功能已关闭")
            
            # 确保解除锁空
            self.unlock_air()
    
    def lock_air(self):
        """锁空"""
        if not self.process_id or not self.features_active["air_lock"]:
            return
        
        # 获取锁空地址
        base_offset = int(self.game_versions[self.current_version]["base_offset"], 16)
        level1_offset = int(self.game_versions[self.current_version]["level1_offset"], 16)
        level2_offset = int(self.game_versions[self.current_version]["level2_offset"], 16)
        level3_offset = int(self.game_versions[self.current_version]["level3_offset"], 16)
        character_offset = int(self.game_versions[self.current_version]["character_base"], 16)
        air_lock_offset = int(self.game_versions[self.current_version]["air_lock"], 16)
        
        # 读取多级指针
        base_addr = self.memory.read_long(self.module_base + base_offset)
        if not base_addr:
            return
            
        level1_addr = self.memory.read_long(base_addr + level1_offset)
        if not level1_addr:
            return
            
        level2_addr = self.memory.read_long(level1_addr + level2_offset)
        if not level2_addr:
            return
            
        level3_addr = self.memory.read_long(level2_addr + level3_offset)
        if not level3_addr:
            return
            
        character_addr = self.memory.read_long(level3_addr + character_offset)
        if not character_addr:
            return
            
        air_lock_addr = character_addr + air_lock_offset
        
        # 写入锁空值
        result = self.memory.write_int(air_lock_addr, 5)
        if result:
            self.status_bar.showMessage("锁空已激活")
            logger.info("锁空已激活")
        else:
            self.show_error("操作失败", "无法写入内存，可能是权限不足")
    
    def unlock_air(self):
        """解除锁空"""
        if not self.process_id or not self.features_active["air_lock"]:
            return
        
        # 获取锁空地址
        base_offset = int(self.game_versions[self.current_version]["base_offset"], 16)
        level1_offset = int(self.game_versions[self.current_version]["level1_offset"], 16)
        level2_offset = int(self.game_versions[self.current_version]["level2_offset"], 16)
        level3_offset = int(self.game_versions[self.current_version]["level3_offset"], 16)
        character_offset = int(self.game_versions[self.current_version]["character_base"], 16)
        air_lock_offset = int(self.game_versions[self.current_version]["air_lock"], 16)
        
        # 读取多级指针
        base_addr = self.memory.read_long(self.module_base + base_offset)
        if not base_addr:
            return
            
        level1_addr = self.memory.read_long(base_addr + level1_offset)
        if not level1_addr:
            return
            
        level2_addr = self.memory.read_long(level1_addr + level2_offset)
        if not level2_addr:
            return
            
        level3_addr = self.memory.read_long(level2_addr + level3_offset)
        if not level3_addr:
            return
            
        character_addr = self.memory.read_long(level3_addr + character_offset)
        if not character_addr:
            return
            
        air_lock_addr = character_addr + air_lock_offset
        
        # 写入解除锁空值
        result = self.memory.write_int(air_lock_addr, 0)
        if result:
            self.status_bar.showMessage("锁空已解除")
            logger.info("锁空已解除")
        else:
            self.show_error("操作失败", "无法写入内存，可能是权限不足")
    
    def toggle_fly(self, checked):
        """切换飞天状态"""
        if not self.process_id or not self.module_base:
            self.fly_check.setChecked(False)
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        self.features_active["fly"] = checked
        self.fly_up_btn.setEnabled(checked)
        self.fly_down_btn.setEnabled(checked)
        
        if checked:
            # 绑定热键
            self.key_binding_pgup = QAction("Fly Up", self)
            self.key_binding_pgup.setShortcut("PgUp")
            self.key_binding_pgup.triggered.connect(self.fly_up)
            self.addAction(self.key_binding_pgup)
            
            self.key_binding_pgdn = QAction("Fly Down", self)
            self.key_binding_pgdn.setShortcut("PgDown")
            self.key_binding_pgdn.triggered.connect(self.fly_down)
            self.addAction(self.key_binding_pgdn)
            
            self.status_bar.showMessage("飞天功能已启用，使用PgUp上升，PgDown下降")
            logger.info("飞天功能已启用")
        else:
            # 关闭飞天功能
            if hasattr(self, 'key_binding_pgup'):
                self.removeAction(self.key_binding_pgup)
            if hasattr(self, 'key_binding_pgdn'):
                self.removeAction(self.key_binding_pgdn)
            
            self.status_bar.showMessage("飞天功能已关闭")
            logger.info("飞天功能已关闭")
    
    def fly_up(self):
        """上升"""
        if not self.process_id or not self.features_active["fly"]:
            return
        
        try:
            height_value = float(self.fly_value.text())
            
            # 获取高度地址
            base_offset = int(self.game_versions[self.current_version]["base_offset"], 16)
            level1_offset = int(self.game_versions[self.current_version]["level1_offset"], 16)
            level2_offset = int(self.game_versions[self.current_version]["level2_offset"], 16)
            level3_offset = int(self.game_versions[self.current_version]["level3_offset"], 16)
            height_offset = int(self.game_versions[self.current_version]["height_base"], 16)
            z_coord_offset = int(self.game_versions[self.current_version]["z_coord"], 16)
            
            # 读取多级指针
            base_addr = self.memory.read_long(self.module_base + base_offset)
            if not base_addr:
                return
                
            level1_addr = self.memory.read_long(base_addr + level1_offset)
            if not level1_addr:
                return
                
            level2_addr = self.memory.read_long(level1_addr + level2_offset)
            if not level2_addr:
                return
                
            level3_addr = self.memory.read_long(level2_addr + level3_offset)
            if not level3_addr:
                return
                
            height_addr = self.memory.read_long(level3_addr + height_offset)
            if not height_addr:
                return
                
            z_addr = height_addr + z_coord_offset
            
            # 读取当前高度
            current_height = self.memory.read_float(z_addr)
            if current_height is None:
                self.show_error("读取失败", "无法读取角色高度")
                return
                
            # 计算新高度
            new_height = current_height + height_value
            
            # 写入新高度
            result = self.memory.write_float(z_addr, new_height)
            if result:
                self.status_bar.showMessage(f"上升: {height_value}米，当前高度: {new_height:.2f}米")
                logger.info(f"角色上升: {height_value}米，新高度: {new_height:.2f}米")
            else:
                self.show_error("写入失败", "无法修改角色高度")
                
        except ValueError:
            self.show_error("输入错误", "请输入有效的高度值")
    
    def fly_down(self):
        """下降"""
        if not self.process_id or not self.features_active["fly"]:
            return
        
        try:
            height_value = float(self.fly_value.text())
            
            # 获取高度地址
            base_offset = int(self.game_versions[self.current_version]["base_offset"], 16)
            level1_offset = int(self.game_versions[self.current_version]["level1_offset"], 16)
            level2_offset = int(self.game_versions[self.current_version]["level2_offset"], 16)
            level3_offset = int(self.game_versions[self.current_version]["level3_offset"], 16)
            height_offset = int(self.game_versions[self.current_version]["height_base"], 16)
            z_coord_offset = int(self.game_versions[self.current_version]["z_coord"], 16)
            
            # 读取多级指针
            base_addr = self.memory.read_long(self.module_base + base_offset)
            if not base_addr:
                return
                
            level1_addr = self.memory.read_long(base_addr + level1_offset)
            if not level1_addr:
                return
                
            level2_addr = self.memory.read_long(level1_addr + level2_offset)
            if not level2_addr:
                return
                
            level3_addr = self.memory.read_long(level2_addr + level3_offset)
            if not level3_addr:
                return
                
            height_addr = self.memory.read_long(level3_addr + height_offset)
            if not height_addr:
                return
                
            z_addr = height_addr + z_coord_offset
            
            # 读取当前高度
            current_height = self.memory.read_float(z_addr)
            if current_height is None:
                self.show_error("读取失败", "无法读取角色高度")
                return
                
            # 计算新高度
            new_height = current_height - height_value
            
            # 写入新高度
            result = self.memory.write_float(z_addr, new_height)
            if result:
                self.status_bar.showMessage(f"下降: {height_value}米，当前高度: {new_height:.2f}米")
                logger.info(f"角色下降: {height_value}米，新高度: {new_height:.2f}米")
            else:
                self.show_error("写入失败", "无法修改角色高度")
                
        except ValueError:
            self.show_error("输入错误", "请输入有效的高度值")
    
    def toggle_stealth(self, checked):
        """切换反隐状态"""
        if not self.process_id or not self.module_base:
            self.stealth_check.setChecked(False)
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        self.features_active["stealth"] = checked
        
        if checked:
            # 实现反隐功能，具体方法取决于游戏机制
            self.status_bar.showMessage("反隐功能已启用")
            logger.info("反隐功能已启用")
        else:
            # 关闭反隐功能
            self.status_bar.showMessage("反隐功能已关闭")
            logger.info("反隐功能已关闭")
    
    def get_current_coordinates(self):
        """获取当前坐标"""
        if not self.process_id or not self.module_base:
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        # 获取坐标地址
        base_offset = int(self.game_versions[self.current_version]["base_offset"], 16)
        level1_offset = int(self.game_versions[self.current_version]["level1_offset"], 16)
        level2_offset = int(self.game_versions[self.current_version]["level2_offset"], 16)
        level3_offset = int(self.game_versions[self.current_version]["level3_offset"], 16)
        height_offset = int(self.game_versions[self.current_version]["height_base"], 16)
        x_coord_offset = int(self.game_versions[self.current_version]["x_coord"], 16)
        y_coord_offset = int(self.game_versions[self.current_version]["y_coord"], 16)
        z_coord_offset = int(self.game_versions[self.current_version]["z_coord"], 16)
        
        # 读取多级指针
        base_addr = self.memory.read_long(self.module_base + base_offset)
        if not base_addr:
            self.show_error("读取失败", "无法读取基址")
            return
            
        level1_addr = self.memory.read_long(base_addr + level1_offset)
        if not level1_addr:
            self.show_error("读取失败", "无法读取级别1指针")
            return
            
        level2_addr = self.memory.read_long(level1_addr + level2_offset)
        if not level2_addr:
            self.show_error("读取失败", "无法读取级别2指针")
            return
            
        level3_addr = self.memory.read_long(level2_addr + level3_offset)
        if not level3_addr:
            self.show_error("读取失败", "无法读取级别3指针")
            return
            
        height_addr = self.memory.read_long(level3_addr + height_offset)
        if not height_addr:
            self.show_error("读取失败", "无法读取高度基址")
            return
        
        # 读取坐标
        x = self.memory.read_float(height_addr + x_coord_offset)
        y = self.memory.read_float(height_addr + y_coord_offset)
        z = self.memory.read_float(height_addr + z_coord_offset)
        
        if x is None or y is None or z is None:
            self.show_error("读取失败", "无法读取坐标值")
            return
        
        # 更新坐标显示
        self.current_x.setText(f"{x:.2f}")
        self.current_y.setText(f"{y:.2f}")
        self.current_z.setText(f"{z:.2f}")
        
        self.status_bar.showMessage(f"当前坐标: X={x:.2f}, Y={y:.2f}, Z={z:.2f}")
        logger.info(f"读取当前坐标: X={x:.2f}, Y={y:.2f}, Z={z:.2f}")
    
    def execute_teleport(self):
        """执行坐标传送"""
        if not self.process_id or not self.module_base:
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        try:
            # 获取目标坐标
            x = float(self.target_x.text())
            y = float(self.target_y.text())
            z = float(self.target_z.text())
            
            # 获取坐标地址
            base_offset = int(self.game_versions[self.current_version]["base_offset"], 16)
            level1_offset = int(self.game_versions[self.current_version]["level1_offset"], 16)
            level2_offset = int(self.game_versions[self.current_version]["level2_offset"], 16)
            level3_offset = int(self.game_versions[self.current_version]["level3_offset"], 16)
            height_offset = int(self.game_versions[self.current_version]["height_base"], 16)
            x_coord_offset = int(self.game_versions[self.current_version]["x_coord"], 16)
            y_coord_offset = int(self.game_versions[self.current_version]["y_coord"], 16)
            z_coord_offset = int(self.game_versions[self.current_version]["z_coord"], 16)
            
            # 读取多级指针
            base_addr = self.memory.read_long(self.module_base + base_offset)
            if not base_addr:
                self.show_error("读取失败", "无法读取基址")
                return
                
            level1_addr = self.memory.read_long(base_addr + level1_offset)
            if not level1_addr:
                self.show_error("读取失败", "无法读取级别1指针")
                return
                
            level2_addr = self.memory.read_long(level1_addr + level2_offset)
            if not level2_addr:
                self.show_error("读取失败", "无法读取级别2指针")
                return
                
            level3_addr = self.memory.read_long(level2_addr + level3_offset)
            if not level3_addr:
                self.show_error("读取失败", "无法读取级别3指针")
                return
                
            height_addr = self.memory.read_long(level3_addr + height_offset)
            if not height_addr:
                self.show_error("读取失败", "无法读取高度基址")
                return
            
            # 写入坐标
            x_result = self.memory.write_float(height_addr + x_coord_offset, x)
            y_result = self.memory.write_float(height_addr + y_coord_offset, y)
            z_result = self.memory.write_float(height_addr + z_coord_offset, z)
            
            if x_result and y_result and z_result:
                self.status_bar.showMessage(f"已传送到: X={x:.2f}, Y={y:.2f}, Z={z:.2f}")
                logger.info(f"传送到坐标: X={x:.2f}, Y={y:.2f}, Z={z:.2f}")
                
                # 更新当前坐标显示
                self.get_current_coordinates()
            else:
                self.show_error("写入失败", "无法写入坐标值")
                
        except ValueError:
            self.show_error("输入错误", "请输入有效的坐标值")
    
    def save_coordinates(self):
        """保存当前坐标到列表"""
        name = self.coord_name.text().strip()
        if not name:
            self.show_error("输入错误", "请输入坐标名称")
            return
        
        x = self.current_x.text()
        y = self.current_y.text()
        z = self.current_z.text()
        
        if x == "0" and y == "0" and z == "0":
            self.show_error("数据错误", "请先获取当前坐标")
            return
            
            # 保存坐标
        coords = f"{x},{y},{z}"
        self.saved_coordinates[name] = coords
        
        # 更新下拉列表和列表视图
        self.saved_coords_combo.clear()
        self.saved_coords_combo.addItems(self.saved_coordinates.keys())
        self.update_saved_coords_list()
        
        # 设置当前选中项
        index = self.saved_coords_combo.findText(name)
        if index >= 0:
            self.saved_coords_combo.setCurrentIndex(index)
        
        self.status_bar.showMessage(f"已保存坐标 {name}: X={x}, Y={y}, Z={z}")
        
        # 清空名称输入框，便于下次输入
        self.coord_name.clear()
    
    def load_saved_coordinate(self):
        """加载保存的坐标"""
        if not self.process_id or not self.module_base:
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        # 获取选中的坐标名
        name = self.saved_coords_combo.currentText()
        if not name or name not in self.saved_coordinates:
            self.show_error("选择错误", "请选择有效的保存坐标")
            return
        
        # 获取保存的坐标
        x, y, z = self.saved_coordinates[name].split(",")
        
        # 设置目标坐标
        self.target_x.setText(f"{x:.2f}")
        self.target_y.setText(f"{y:.2f}")
        self.target_z.setText(f"{z:.2f}")
        
        self.status_bar.showMessage(f"已加载坐标 '{name}': X={x:.2f}, Y={y:.2f}, Z={z:.2f}")
        logger.info(f"加载坐标 '{name}': X={x:.2f}, Y={y:.2f}, Z={z:.2f}")
    
    def run_diagnostics(self):
        """运行诊断"""
        if not self.process_id or not self.module_base:
            self.diag_text.setText("未连接游戏进程，无法运行诊断")
            return
        
        diag_info = []
        diag_info.append(f"进程: {self.process_id}")
        diag_info.append(f"模块基址: 0x{self.module_base:X}")
        diag_info.append(f"游戏版本: {self.current_version}")
        
        # 测试读取基本地址
        base_offset = int(self.game_versions[self.current_version]["base_offset"], 16)
        base_addr = self.memory.read_long(self.module_base + base_offset)
        diag_info.append(f"基本地址测试: {'成功' if base_addr else '失败'}")
        
        if base_addr:
            # 测试读取玩家指针
            level1_offset = int(self.game_versions[self.current_version]["level1_offset"], 16)
            level1_addr = self.memory.read_long(base_addr + level1_offset)
            diag_info.append(f"玩家指针测试: {'成功' if level1_addr else '失败'}")
            
            if level1_addr:
                # 测试读取玩家坐标
                level2_offset = int(self.game_versions[self.current_version]["level2_offset"], 16)
                level2_addr = self.memory.read_long(level1_addr + level2_offset)
                
                if level2_addr:
                    level3_offset = int(self.game_versions[self.current_version]["level3_offset"], 16)
                    level3_addr = self.memory.read_long(level2_addr + level3_offset)
                    
                    if level3_addr:
                        height_offset = int(self.game_versions[self.current_version]["height_base"], 16)
                        height_addr = self.memory.read_long(level3_addr + height_offset)
                        
                        if height_addr:
                            x_coord_offset = int(self.game_versions[self.current_version]["x_coord"], 16)
                            y_coord_offset = int(self.game_versions[self.current_version]["y_coord"], 16)
                            z_coord_offset = int(self.game_versions[self.current_version]["z_coord"], 16)
                            
                            x = self.memory.read_float(height_addr + x_coord_offset)
                            y = self.memory.read_float(height_addr + y_coord_offset)
                            z = self.memory.read_float(height_addr + z_coord_offset)
                            
                            if x is not None and y is not None and z is not None:
                                diag_info.append(f"坐标读取测试: 成功")
                                diag_info.append(f"当前坐标: X={x:.2f}, Y={y:.2f}, Z={z:.2f}")
                            else:
                                diag_info.append(f"坐标读取测试: 失败")
                        else:
                            diag_info.append(f"高度基址测试: 失败")
                    else:
                        diag_info.append(f"级别3指针测试: 失败")
                else:
                    diag_info.append(f"级别2指针测试: 失败")
        
        # 显示诊断信息
        self.diag_text.setText("\n".join(diag_info))
        self.status_bar.showMessage("诊断完成")
        logger.info("运行诊断:\n" + "\n".join(diag_info))
    
    def show_error(self, title, message):
        """显示错误消息"""
        QMessageBox.critical(self, title, message)
        logger.error(f"{title}: {message}")
    
    def show_info(self, title, message):
        """显示信息消息"""
        QMessageBox.information(self, title, message)
        logger.info(f"{title}: {message}")
    
    def closeEvent(self, event):
        """窗口关闭事件，最小化到托盘而不是退出"""
        if self.tray_icon.isVisible():
            self.hide()
            self.tray_icon.showMessage(
                "Aion专用", 
                "程序已最小化到系统托盘，按Home键可呼出界面", 
                self.tray_icon.icon(), 
                2000
            )
            event.ignore()
        else:
            # 关闭所有计时器
            for timer in self.timers.values():
                timer.stop()
            
            # 关闭进程句柄
            if self.process_id:
                self.memory.close_process()
            
            event.accept()

    def setup_tray_icon(self):
        """设置系统托盘图标"""
        # 创建托盘图标菜单
        self.tray_menu = QMenu()
        
        # 添加菜单项
        show_action = QAction("显示主窗口", self)
        show_action.triggered.connect(self.show_mainwindow)
        
        exit_action = QAction("退出程序", self)
        exit_action.triggered.connect(self.safe_exit)
        
        self.tray_menu.addAction(show_action)
        self.tray_menu.addSeparator()
        self.tray_menu.addAction(exit_action)
        
        # 创建系统托盘图标
        self.tray_icon = QSystemTrayIcon(self)
        
        # 设置图标
        try:
            icon = QIcon("icon.ico")  # 尝试加载自定义图标
        except:
            icon = QIcon.fromTheme("applications-system")  # 使用系统图标
        
        self.tray_icon.setIcon(icon)
        self.tray_icon.setToolTip("Aion专用")
        
        # 设置托盘图标菜单
        self.tray_icon.setContextMenu(self.tray_menu)
        
        # 响应托盘图标点击事件
        self.tray_icon.activated.connect(self.tray_icon_activated)
        
        # 显示托盘图标
        self.tray_icon.show()
        
        # 设置初始提示
        self.tray_icon.showMessage(
            "Aion专用", 
            "程序已最小化到系统托盘，按Home键可呼出界面", 
            icon, 
            3000
        )

    # 创建原生事件过滤器类
    class WinEventFilter(QAbstractNativeEventFilter):
        def __init__(self, keybind_callback):
            super().__init__()
            self.keybind_callback = keybind_callback
        
        def nativeEventFilter(self, eventType, message):
            if eventType == "windows_generic_MSG":
                msg = ctypes.wintypes.MSG.from_address(int(message))
                if msg.message == WM_HOTKEY:
                    self.keybind_callback()
                    return True, 0
            return False, 0

    # 更改GameMemoryTool类中的register_global_hotkey方法
    def register_global_hotkey(self):
        """注册全局热键 - 使用Windows API"""
        try:
            # 定义RegisterHotKey函数
            user32.RegisterHotKey.argtypes = [wintypes.HWND, ctypes.c_int, ctypes.c_uint, ctypes.c_uint]
            user32.RegisterHotKey.restype = wintypes.BOOL
            
            # 获取窗口句柄
            hwnd = int(self.winId())
            
            # 注册Home键为全局热键 (ID=1, 无修饰键, Home键)
            result = user32.RegisterHotKey(hwnd, 1, 0, VK_HOME)
            
            if result:
                # 创建并安装事件过滤器
                self.win_event_filter = GameMemoryTool.WinEventFilter(self.toggle_window_visibility)
                QApplication.instance().installNativeEventFilter(self.win_event_filter)
                logger.info("已成功使用Windows API注册Home键为全局热键")
            else:
                # 回退到keyboard库
                self.register_keyboard_hotkey()
        except Exception as e:
            # 回退到keyboard库
            self.register_keyboard_hotkey()

    def register_keyboard_hotkey(self):
        """使用keyboard库注册热键（备用方法）"""
        try:
            # 清理所有现有热键
            keyboard.unhook_all()
            
            # 直接钩住Home键
            keyboard.add_hotkey('home', self.toggle_window_visibility, suppress=False)
            logger.info("已使用keyboard库注册Home键为全局热键")
        except Exception as e:
            logger.error(f"使用keyboard库注册热键失败: {str(e)}")

    def safe_exit(self):
        """安全退出程序"""
        logger.info("开始安全退出程序...")
        
        # 关闭所有计时器
        for timer in self.timers.values():
            timer.stop()
        
        # 注销Windows API注册的热键
        try:
            user32.UnregisterHotKey.argtypes = [wintypes.HWND, ctypes.c_int]
            user32.UnregisterHotKey.restype = wintypes.BOOL
            
            hwnd = int(self.winId())
            user32.UnregisterHotKey(hwnd, 1)
            logger.info("已注销Windows API热键")
        except Exception as e:
            logger.error(f"注销Windows API热键失败: {str(e)}")
        
        # 注销keyboard库注册的热键
        try:
            keyboard.unhook_all()
            logger.info("已清理所有keyboard钩子")
        except Exception as e:
            logger.error(f"清理keyboard钩子失败: {str(e)}")
        
        # 隐藏系统托盘图标
        if hasattr(self, 'tray_icon'):
            self.tray_icon.hide()
        
        # 关闭进程句柄
        if self.process_id:
            self.memory.close_process()
        
        # 退出应用
        QApplication.instance().quit()
        
        # 强制结束进程
        import os
        os._exit(0)

    def auto_find_aion_process(self):
        """自动搜索并加载aion进程"""
        for i in range(self.process_combo.count()):
            process_text = self.process_combo.itemText(i)
            if "aion" in process_text.lower():
                logger.info(f"自动识别到aion进程: {process_text}")
                self.process_combo.setCurrentIndex(i)
                self.status_bar.showMessage(f"已自动选择: {process_text}")
                
                # 自动加载游戏进程
                QTimer.singleShot(100, self.load_game)
                return
        
        # 如果没有找到aion进程，提示用户
        logger.warning("未能自动识别到aion进程")
        self.status_bar.showMessage("未识别到aion进程，请手动选择并加载")

    def remove_always_on_top(self):
        """这个方法已经不再使用"""
        pass

    def toggle_window_visibility(self):
        """Home键按下时切换窗口显示/隐藏状态"""
        # 使用全局变量来跟踪窗口状态
        if not hasattr(self, "_window_visible"):
            self._window_visible = True
        
        # 反转窗口可见状态
        self._window_visible = not self._window_visible
        
        if self._window_visible:
            # 显示窗口和系统托盘图标
            logger.info("显示主窗口")
            self.show()
            self.tray_icon.show()
            self.setWindowState(self.windowState() & ~Qt.WindowState.WindowMinimized | Qt.WindowState.WindowActive)
            self.activateWindow()
            self.raise_()
        else:
            # 隐藏窗口和系统托盘图标
            logger.info("隐藏主窗口和托盘图标")
            self.hide()
            self.tray_icon.hide()
            
        # 确保事件处理立即完成
        QApplication.instance().processEvents()

    def show_mainwindow(self):
        """显示主窗口并置于最前"""
        # 设置窗口为可见状态
        self._window_visible = True
        # 确保窗口和托盘图标可见
        self.show()
        self.tray_icon.show()
        # 从最小化状态恢复
        self.setWindowState(self.windowState() & ~Qt.WindowState.WindowMinimized | Qt.WindowState.WindowActive)
        # 激活窗口
        self.activateWindow()
        # 将窗口置于最前
        self.raise_()
        # 记录日志
        logger.info("显示主窗口和托盘图标")

    def tray_icon_activated(self, reason):
        """托盘图标被激活时的回调函数"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            # 直接显示窗口
            self._window_visible = True
            self.show()
            self.tray_icon.show()
            self.setWindowState(self.windowState() & ~Qt.WindowState.WindowMinimized | Qt.WindowState.WindowActive)
            self.activateWindow()
            self.raise_()

    def toggle_teleport_speed(self, checked):
        """切换瞬移加速状态"""
        if not self.process_id or not self.module_base:
            self.teleport_speed_check.setChecked(False)
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        self.features_active["teleport_speed"] = checked
        
        if checked:
            try:
                teleport_speed_value = float(self.teleport_speed_value.text())
                
                # 获取瞬移加速地址
                teleport_speed_addr = int(self.game_versions[self.current_version]["teleport_speed"], 16)
                
                # 写入瞬移加速值
                result = self.memory.write_float(teleport_speed_addr, teleport_speed_value)
                
                if result:
                    self.status_bar.showMessage(f"瞬移加速已启用，值: {teleport_speed_value}")
                    logger.info(f"瞬移加速已启用，值: {teleport_speed_value}")
                    
                    # 创建定时器，持续更新瞬移速度
                    if "teleport_speed" not in self.timers:
                        self.timers["teleport_speed"] = QTimer(self)
                        self.timers["teleport_speed"].timeout.connect(self.update_teleport_speed)
                    
                    self.timers["teleport_speed"].start(1000)  # 每秒更新一次
                else:
                    self.teleport_speed_check.setChecked(False)
                    self.show_error("写入失败", "无法修改瞬移加速值，可能是地址错误或权限不足")
                    
            except ValueError:
                self.teleport_speed_check.setChecked(False)
                self.show_error("输入错误", "请输入有效的速度值")
        else:
            # 关闭瞬移加速
            if "teleport_speed" in self.timers:
                self.timers["teleport_speed"].stop()
            
            # 恢复原始值
            teleport_speed_addr = int(self.game_versions[self.current_version]["teleport_speed"], 16)
            self.memory.write_float(teleport_speed_addr, 1856.65)  # 恢复默认值
            
            self.status_bar.showMessage("瞬移加速已关闭")
            logger.info("瞬移加速已关闭")

    def update_teleport_speed(self):
        """定时更新瞬移速度"""
        if not self.process_id or not self.features_active["teleport_speed"]:
            return
            
        try:
            teleport_speed_value = float(self.teleport_speed_value.text())
            
            # 获取瞬移加速地址
            teleport_speed_addr = int(self.game_versions[self.current_version]["teleport_speed"], 16)
            
            # 写入瞬移加速值
            self.memory.write_float(teleport_speed_addr, teleport_speed_value)
            
        except Exception as e:
            logger.error(f"更新瞬移速度时出错: {str(e)}")
            # 出错时停止计时器
            if "teleport_speed" in self.timers:
                self.timers["teleport_speed"].stop()
            self.teleport_speed_check.setChecked(False)

    def toggle_view_range(self, checked):
        """切换视野扩展状态"""
        if not self.process_id or not self.module_base:
            self.view_range_check.setChecked(False)
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        self.features_active["view_range"] = checked
        
        if checked:
            try:
                view_value = float(self.view_range_value.text())
                
                # 创建定时器，持续更新视野范围
                if "view_range" not in self.timers:
                    self.timers["view_range"] = QTimer(self)
                    self.timers["view_range"].timeout.connect(self.update_view_range)
                
                self.timers["view_range"].start(100)  # 每100ms更新一次
                self.status_bar.showMessage(f"视野扩展已启动: {view_value}")
                logger.info(f"视野扩展已启用，值: {view_value}")
                
            except ValueError:
                self.view_range_check.setChecked(False)
                self.show_error("输入错误", "请输入有效的视野值")
        else:
            # 关闭视野扩展
            if "view_range" in self.timers:
                self.timers["view_range"].stop()
            
            # 恢复默认视野
            view_offset = int(self.game_versions[self.current_version]["view_offset"], 16)
            view_offset1 = int(self.game_versions[self.current_version]["view_offset1"], 16)
            
            view_base1 = self.module_base + view_offset
            view_base2 = self.memory.read_long(view_base1)
            if view_base2:
                view_base3 = view_base2 + view_offset1
                self.memory.write_float(view_base3, 35.0)  # 恢复默认视野值
            
            self.status_bar.showMessage("视野扩展已关闭")
            logger.info("视野扩展已关闭")
    
    def update_view_range(self):
        """定时更新视野范围"""
        if not self.process_id or not self.features_active["view_range"]:
            return
            
        try:
            view_value = float(self.view_range_value.text())
            
            # 获取视野地址
            view_offset = int(self.game_versions[self.current_version]["view_offset"], 16)
            view_offset1 = int(self.game_versions[self.current_version]["view_offset1"], 16)
            
            view_base1 = self.module_base + view_offset
            view_base2 = self.memory.read_long(view_base1)
            if not view_base2:
                return
                
            view_base3 = view_base2 + view_offset1
            
            # 写入视野值
            self.memory.write_float(view_base3, view_value)
            
        except Exception as e:
            logger.error(f"更新视野范围时出错: {str(e)}")
            # 出错时停止计时器
            if "view_range" in self.timers:
                self.timers["view_range"].stop()
            self.view_range_check.setChecked(False)

    def toggle_show_hp(self, checked):
        """切换显血状态"""
        if not self.process_id or not self.module_base:
            self.show_hp_check.setChecked(False)
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        self.features_active["show_hp"] = checked
        
        try:
            # 获取显血地址
            show_hp_offset = int(self.game_versions[self.current_version]["show_hp"], 16)
            show_hp_addr = self.module_base + show_hp_offset
            
            if checked:
                # 写入NOP指令
                patch_bytes = bytearray([0x90, 0x90])
                result = self.memory.write_bytes(show_hp_addr, patch_bytes)
                
                if result:
                    self.status_bar.showMessage("显血功能已启用")
                    logger.info("显血功能已启用")
                else:
                    self.show_hp_check.setChecked(False)
                    self.show_error("写入失败", "无法修改内存，可能是权限不足")
            else:
                # 恢复原始指令
                original_bytes = bytearray([0x74, 0x1B])  # 原始跳转指令
                result = self.memory.write_bytes(show_hp_addr, original_bytes)
                
                if result:
                    self.status_bar.showMessage("显血功能已关闭")
                    logger.info("显血功能已关闭")
                else:
                    self.show_error("写入失败", "无法恢复原始内存值")
                    
        except Exception as e:
            logger.error(f"切换显血功能时出错: {str(e)}")
            self.show_hp_check.setChecked(False)
            self.show_error("操作失败", f"显血功能出错: {str(e)}")

    def toggle_map_extend(self, checked):
        """切换地图扩展状态"""
        if not self.process_id or not self.module_base:
            self.map_extend_check.setChecked(False)
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        self.features_active["map_extend"] = checked
        
        if checked:
            try:
                map_value = float(self.map_extend_value.text())
                
                # 创建定时器，持续更新地图范围
                if "map_extend" not in self.timers:
                    self.timers["map_extend"] = QTimer(self)
                    self.timers["map_extend"].timeout.connect(self.update_map_extend)
                
                self.timers["map_extend"].start(100)  # 每100ms更新一次
                self.status_bar.showMessage(f"地图扩展已启动: {map_value}")
                logger.info(f"地图扩展已启用，值: {map_value}")
                
            except ValueError:
                self.map_extend_check.setChecked(False)
                self.show_error("输入错误", "请输入有效的地图范围值")
        else:
            # 关闭地图扩展
            if "map_extend" in self.timers:
                self.timers["map_extend"].stop()
            
            # 恢复默认地图范围
            map_offset = int(self.game_versions[self.current_version]["map_offset"], 16)
            map_addr = self.module_base + map_offset
            self.memory.write_float(map_addr, 90.0)  # 恢复默认地图值
            
            self.status_bar.showMessage("地图扩展已关闭")
            logger.info("地图扩展已关闭")
    
    def update_map_extend(self):
        """定时更新地图范围"""
        if not self.process_id or not self.features_active["map_extend"]:
            return
            
        try:
            map_value = float(self.map_extend_value.text())
            
            # 获取地图地址
            map_offset = int(self.game_versions[self.current_version]["map_offset"], 16)
            map_addr = self.module_base + map_offset
            
            # 写入地图值
            self.memory.write_float(map_addr, map_value)
            
        except Exception as e:
            logger.error(f"更新地图范围时出错: {str(e)}")
            # 出错时停止计时器
            if "map_extend" in self.timers:
                self.timers["map_extend"].stop()
            self.map_extend_check.setChecked(False)

    def toggle_wing_nocd(self, checked):
        """切换翅膀无CD状态"""
        if not self.process_id or not self.module_base:
            self.wing_nocd_check.setChecked(False)
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        self.features_active["wing_nocd"] = checked
        
        if checked:
            # 创建定时器，持续更新翅膀CD
            if "wing_nocd" not in self.timers:
                self.timers["wing_nocd"] = QTimer(self)
                self.timers["wing_nocd"].timeout.connect(self.update_wing_nocd)
            
            self.timers["wing_nocd"].start(100)  # 每100ms更新一次
            self.status_bar.showMessage("翅膀无CD已启用")
            logger.info("翅膀无CD功能已启用")
        else:
            # 关闭翅膀无CD
            if "wing_nocd" in self.timers:
                self.timers["wing_nocd"].stop()
            
            self.status_bar.showMessage("翅膀无CD已关闭")
            logger.info("翅膀无CD功能已关闭")
    
    def update_wing_nocd(self):
        """定时更新翅膀CD"""
        if not self.process_id or not self.features_active["wing_nocd"]:
            return
            
        try:
            # 获取翅膀地址
            wing_offset = int(self.game_versions[self.current_version]["wing"], 16)
            wing_addr = self.module_base + wing_offset
            
            # 写入0值清除CD
            self.memory.write_int(wing_addr, 0)
            
        except Exception as e:
            logger.error(f"更新翅膀CD时出错: {str(e)}")
            # 出错时停止计时器
            if "wing_nocd" in self.timers:
                self.timers["wing_nocd"].stop()
            self.wing_nocd_check.setChecked(False)
            
    def toggle_skill_speed_adjust(self, checked):
        """切换攻击速度微调状态"""
        if not self.process_id or not self.module_base:
            self.skill_speed_adjust_check.setChecked(False)
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        self.features_active["skill_speed_adjust"] = checked
        
        if checked:
            try:
                speed_value = int(self.skill_speed_adjust_value.text())
                
                # 获取攻击速度微调地址
                skill_speed_adjust_offset = int(self.game_versions[self.current_version]["skill_speed_adjust"], 16)
                
                # 写入攻击速度微调值
                result = self.memory.write_float(self.module_base + skill_speed_adjust_offset, speed_value)
                
                if result:
                    self.status_bar.showMessage(f"攻击速度微调已启用，值: {speed_value}")
                    logger.info(f"攻击速度微调已启用，值: {speed_value}")
                else:
                    self.skill_speed_adjust_check.setChecked(False)
                    self.show_error("写入失败", "无法修改攻击速度微调值")
            except ValueError:
                self.skill_speed_adjust_check.setChecked(False)
                self.show_error("输入错误", "请输入有效的速度值")
        else:
            # 关闭攻击速度微调功能
            skill_speed_adjust_offset = int(self.game_versions[self.current_version]["skill_speed_adjust"], 16)
            self.memory.write_float(self.module_base + skill_speed_adjust_offset, 1.0)  # 恢复默认值
            
            self.status_bar.showMessage("攻击速度微调已关闭")
            logger.info("攻击速度微调已关闭")

    def toggle_qsk(self, checked):
        """切换QSK秒起状态"""
        if not self.process_id or not self.module_base:
            self.qsk_check.setChecked(False)
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        self.features_active["qsk"] = checked
        
        if checked:
            # 获取QSK秒起地址
            qsk_offset = int(self.game_versions[self.current_version]["qsk_offset"], 16)
            qsk_addr = self.module_base + qsk_offset
            
            # 写入QSK秒起值
            result = self.memory.write_int(qsk_addr, 0)
            
            if result:
                self.status_bar.showMessage("QSK秒起已启用")
                logger.info("QSK秒起已启用")
            else:
                self.qsk_check.setChecked(False)
                self.show_error("写入失败", "无法修改内存，可能是权限不足")
        else:
            # 关闭QSK秒起，恢复原始值
            # 根据源码分析，QSK秒起启用时写入0，关闭时需要恢复原始值
            # 这里写入1作为原始值
            result = self.memory.write_int(qsk_addr, 1)
            
            if result:
                self.status_bar.showMessage("QSK秒起已关闭")
                logger.info("QSK秒起已关闭")
            else:
                self.qsk_check.setChecked(True)  # 如果恢复失败，保持开启状态
                self.show_error("写入失败", "无法恢复原始值，可能是权限不足")
                
    def toggle_attack_range(self, checked):
        """切换攻击距离状态"""
        if not self.process_id or not self.module_base:
            self.attack_range_check.setChecked(False)
            self.show_error("操作失败", "请先加载游戏进程")
            return
        
        self.features_active["attack_range"] = checked
        
        if checked:
            try:
                range_value = float(self.attack_range_value.text())
                
                # 创建定时器，持续更新攻击距离
                if "attack_range" not in self.timers:
                    self.timers["attack_range"] = QTimer(self)
                    self.timers["attack_range"].timeout.connect(self.update_attack_range)
                
                self.timers["attack_range"].start(100)  # 每100毫秒更新一次
                    
                # 立即更新一次攻击距离
                self.update_attack_range()
                
                self.status_bar.showMessage(f"攻击距离已增加: {range_value}")
                logger.info(f"攻击距离增加已启用，值: {range_value}")
                
            except ValueError:
                self.attack_range_check.setChecked(False)
                self.show_error("输入错误", "请输入有效的距离值")
            except Exception as e:
                logger.error(f"设置攻击距离时出错: {str(e)}")
                self.attack_range_check.setChecked(False)
                self.show_error("操作失败", f"设置攻击距离功能出错: {str(e)}")
        else:
            # 关闭攻击距离增加
            if "attack_range" in self.timers:
                self.timers["attack_range"].stop()
            
            # 恢复默认攻击距离
            try:
                attack_range_offset = int(self.game_versions[self.current_version]["attack_range"], 16)
                attack_range_addr = self.module_base + attack_range_offset
                self.memory.write_float(attack_range_addr, 3.0)  # 恢复默认攻击距离值
                
                self.status_bar.showMessage("攻击距离增加已关闭")
                logger.info("攻击距离增加已关闭")
            except Exception as e:
                logger.error(f"恢复默认攻击距离时出错: {str(e)}")

    def update_attack_range(self):
        """定时更新攻击距离"""
        if not self.process_id or not self.features_active.get("attack_range", False):
            return
            
        try:
            range_value = float(self.attack_range_value.text())
            
            # 获取攻击距离地址
            attack_range_offset = int(self.game_versions[self.current_version]["attack_range"], 16)
            attack_range_addr = self.module_base + attack_range_offset
            
            # 写入攻击距离值
            result = self.memory.write_float(attack_range_addr, range_value)
            
            if not result:
                logger.error("写入攻击距离值失败")
                self.status_bar.showMessage("攻击距离修改失败，可能是权限不足")
                self.attack_range_check.setChecked(False)
                if "attack_range" in self.timers:
                    self.timers["attack_range"].stop()
            
        except Exception as e:
            logger.error(f"更新攻击距离时出错: {str(e)}")
            # 出错时停止计时器
            if "attack_range" in self.timers:
                self.timers["attack_range"].stop()
            self.attack_range_check.setChecked(False)
    
    def on_saved_coords_selection(self):
        """当从列表中选择坐标时启用加载和删除按钮"""
        selected = len(self.saved_coords_list.selectedItems()) > 0
        self.load_from_list_btn.setEnabled(selected)
        self.delete_coord_btn.setEnabled(selected)

    def load_from_list(self):
        """从列表中加载选择的坐标"""
        selected_item = self.saved_coords_list.currentItem()
        if not selected_item:
            return
            
        name = selected_item.text()
        if name not in self.saved_coordinates:
            return
            
        coords = self.saved_coordinates[name].split(",")
        if len(coords) != 3:
            return
            
        # 更新目标坐标输入框
        self.target_x.setText(coords[0])
        self.target_y.setText(coords[1])
        self.target_z.setText(coords[2])
        
        self.status_bar.showMessage(f"已加载坐标 {name}: X={coords[0]}, Y={coords[1]}, Z={coords[2]}")
    
    def delete_saved_coordinate(self):
        """删除选中的保存坐标"""
        selected_item = self.saved_coords_list.currentItem()
        if not selected_item:
            return
            
        name = selected_item.text()
        if name in self.saved_coordinates:
            del self.saved_coordinates[name]
            
            # 更新下拉列表和列表视图
            self.saved_coords_combo.clear()
            self.saved_coords_combo.addItems(self.saved_coordinates.keys())
            self.update_saved_coords_list()
            
            self.status_bar.showMessage(f"已删除坐标: {name}")

    def update_saved_coords_list(self):
        """更新保存的坐标列表显示"""
        self.saved_coords_list.clear()
        for name in self.saved_coordinates.keys():
            self.saved_coords_list.addItem(name)

    def check_activation_status(self):
        """定期检查激活状态"""
        is_activated, message, expiry_date = LicenseChecker.check_activation()
        if not is_activated or (expiry_date and datetime.now() > expiry_date):
            QMessageBox.critical(self, "激活已过期", 
                               "您的软件许可已过期。\n\n"
                               "请联系软件供应商获取新的激活码。")
            self.close()
            QApplication.quit()

    def setup_menu(self):
        # 现有的菜单代码...
        
        # 添加帮助菜单
        help_menu = self.menuBar().addMenu("帮助")
        
        # 添加查看机器ID选项
        show_machine_id_action = QAction("查看机器ID", self)
        show_machine_id_action.triggered.connect(self.show_machine_id)
        help_menu.addAction(show_machine_id_action)
        
        # 添加关于选项
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def show_machine_id(self):
        """显示当前机器的硬件ID"""
        machine_id = HardwareInfo.generate_machine_id()
        
        # 创建一个对话框显示机器ID
        dialog = QDialog(self)
        dialog.setWindowTitle("机器ID")
        dialog.setMinimumWidth(400)
        
        layout = QVBoxLayout()
        
        # 添加说明文本
        info_label = QLabel("以下是您的机器硬件ID，请复制此ID联系供应商获取激活码：")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 显示机器ID的文本框
        id_text = QLineEdit(machine_id)
        id_text.setReadOnly(True)
        id_text.setStyleSheet("font-family: Consolas, monospace; font-size: 12pt; padding: 5px;")
        layout.addWidget(id_text)
        
        # 添加复制按钮
        copy_button = QPushButton("复制到剪贴板")
        def copy_to_clipboard():
            QApplication.clipboard().setText(machine_id)
            copy_button.setText("已复制!")
            QTimer.singleShot(1500, lambda: copy_button.setText("复制到剪贴板"))
        
        copy_button.clicked.connect(copy_to_clipboard)
        layout.addWidget(copy_button)
        
        # 添加关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(dialog.accept)
        layout.addWidget(close_button)
        
        dialog.setLayout(layout)
        dialog.exec()

    def show_activation_dialog(self):
        """显示激活对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("软件激活")
        dialog.setMinimumWidth(400)
        
        layout = QVBoxLayout()
        
        # 显示机器ID
        machine_id = HardwareInfo.generate_machine_id()
        id_group = QGroupBox("您的机器ID")
        id_layout = QVBoxLayout()
        
        id_text = QLineEdit(machine_id)
        id_text.setReadOnly(True)
        id_layout.addWidget(id_text)
        
        copy_button = QPushButton("复制机器ID")
        copy_button.clicked.connect(lambda: QApplication.clipboard().setText(machine_id))
        id_layout.addWidget(copy_button)
        
        id_group.setLayout(id_layout)
        layout.addWidget(id_group)
        
        # 输入激活码部分
        key_group = QGroupBox("输入激活码")
        key_layout = QVBoxLayout()
        
        key_text = QLineEdit()
        key_text.setPlaceholderText("请输入您的激活码")
        key_layout.addWidget(key_text)
        
        key_group.setLayout(key_layout)
        layout.addWidget(key_group)
        
        # 按钮区域
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(lambda: self.activate_software(key_text.text(), dialog))
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)
        
        dialog.setLayout(layout)
        dialog.exec()


class LoginSystem:
    """激活码验证和用户管理系统"""
    def __init__(self):
        self.users_file = "users.dat"
        self.users = self.load_users()
        # 有效的激活码列表 - 实际应用中应使用更安全的验证方式
        self.valid_keys = ["AAAA-BBBB-CCCC-DDDD", "XXXX-YYYY-ZZZZ-1234"]
        self.valid_keys_file = "valid_keys.dat"
        self.valid_keys = self.load_valid_keys()
        
    def load_users(self):
        """加载已注册用户信息"""
        try:
            with open(self.users_file, "r") as f:
                # 使用简单的base64编码增加一些混淆
                encoded_data = f.read()
                decoded_data = base64.b64decode(encoded_data).decode('utf-8')
                return json.loads(decoded_data)
        except:
            return {}
            
    def save_users(self):
        """保存用户信息"""
        with open(self.users_file, "w") as f:
            # 使用简单的base64编码增加一些混淆
            encoded_data = base64.b64encode(json.dumps(self.users).encode('utf-8')).decode('utf-8')
            f.write(encoded_data)
            
    def hash_password(self, password, salt=None):
        """使用SHA-256哈希并加盐处理密码"""
        if salt is None:
            salt = uuid.uuid4().hex
        hashed = hashlib.sha256((password + salt).encode()).hexdigest()
        return f"{salt}:{hashed}"
        
    def verify_password(self, stored_password, provided_password):
        """验证密码"""
        salt, hashed = stored_password.split(":")
        return hashed == hashlib.sha256((provided_password + salt).encode()).hexdigest()
        
    def register_user(self, username, password, activation_key):
        """注册新用户"""
        # 检查激活码是否有效
        if activation_key not in self.valid_keys:
            return False, "激活码无效"
            
        # 检查用户名是否已存在
        if username in self.users:
            return False, "用户名已存在"
            
        # 保存用户信息
        self.users[username] = {
            "password": self.hash_password(password),
            "activation_key": activation_key
        }
        self.save_users()
        return True, "注册成功"
        
    def login_user(self, username, password):
        """用户登录验证"""
        if username not in self.users:
            return False, "用户名不存在"
            
        stored_password = self.users[username]["password"]
        if not self.verify_password(stored_password, password):
            return False, "密码错误"
            
        return True, "登录成功"

    def load_valid_keys(self):
        """从导出文件加载有效激活码"""
        try:
            if os.path.exists(self.valid_keys_file):
                with open(self.valid_keys_file, "r") as f:
                    encoded_data = f.read()
                    decoded_data = base64.b64decode(encoded_data).decode('utf-8')
                    return json.loads(decoded_data).get("valid_keys", [])
            return ["AAAA-BBBB-CCCC-DDDD", "XXXX-YYYY-ZZZZ-1234"]  # 默认激活码，当文件不存在时使用
        except:
            return ["AAAA-BBBB-CCCC-DDDD", "XXXX-YYYY-ZZZZ-1234"]


class LoginDialog(QDialog):
    """登录界面对话框"""
    def __init__(self, login_system, parent=None):
        super().__init__(parent)
        self.login_system = login_system
        self.setWindowTitle("登录系统")
        self.setMinimumWidth(400)
        self.setMinimumHeight(300)
        self.setup_ui()
        
    def setup_ui(self):
        """设置登录界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tabs = QTabWidget()
        login_tab = QWidget()
        register_tab = QWidget()
        
        # 设置登录标签页
        login_layout = QVBoxLayout(login_tab)
        self.login_username = QLineEdit()
        self.login_username.setPlaceholderText("用户名")
        self.login_password = QLineEdit()
        self.login_password.setPlaceholderText("密码")
        self.login_password.setEchoMode(QLineEdit.EchoMode.Password)
        
        login_form = QFormLayout()
        login_form.addRow("用户名:", self.login_username)
        login_form.addRow("密码:", self.login_password)
        
        login_btn = QPushButton("登录")
        login_btn.clicked.connect(self.login)
        
        login_layout.addLayout(login_form)
        login_layout.addWidget(login_btn)
        login_layout.addStretch()
        
        # 设置注册标签页
        register_layout = QVBoxLayout(register_tab)
        self.register_username = QLineEdit()
        self.register_username.setPlaceholderText("用户名")
        self.register_password = QLineEdit()
        self.register_password.setPlaceholderText("密码")
        self.register_password.setEchoMode(QLineEdit.EchoMode.Password)
        self.register_password_confirm = QLineEdit()
        self.register_password_confirm.setPlaceholderText("确认密码")
        self.register_password_confirm.setEchoMode(QLineEdit.EchoMode.Password)
        self.activation_key = QLineEdit()
        self.activation_key.setPlaceholderText("激活码 (格式: XXXX-XXXX-XXXX-XXXX)")
        
        register_form = QFormLayout()
        register_form.addRow("用户名:", self.register_username)
        register_form.addRow("密码:", self.register_password)
        register_form.addRow("确认密码:", self.register_password_confirm)
        register_form.addRow("激活码:", self.activation_key)
        
        register_btn = QPushButton("注册")
        register_btn.clicked.connect(self.register)
        
        register_layout.addLayout(register_form)
        register_layout.addWidget(register_btn)
        register_layout.addStretch()
        
        # 添加标签页
        self.tabs.addTab(login_tab, "登录")
        self.tabs.addTab(register_tab, "注册")
        
        layout.addWidget(self.tabs)
        
        # 状态消息标签
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: red;")
        layout.addWidget(self.status_label)
        
    def login(self):
        """处理登录按钮点击"""
        username = self.login_username.text()
        password = self.login_password.text()
        
        if not username or not password:
            self.status_label.setText("请输入用户名和密码")
            return
            
        success, message = self.login_system.login_user(username, password)
        if success:
            self.accept()  # 关闭对话框并返回接受状态
        else:
            self.status_label.setText(message)
            
    def register(self):
        """处理注册按钮点击"""
        username = self.register_username.text()
        password = self.register_password.text()
        password_confirm = self.register_password_confirm.text()
        key = self.activation_key.text()
        
        # 验证输入
        if not username or not password or not password_confirm or not key:
            self.status_label.setText("请填写所有字段")
            return
            
        if password != password_confirm:
            self.status_label.setText("两次输入的密码不一致")
            return
            
        # 注册用户
        success, message = self.login_system.register_user(username, password, key)
        if success:
            self.status_label.setText("注册成功，请登录")
            self.tabs.setCurrentIndex(0)  # 切换到登录标签页
        else:
            self.status_label.setText(message)


class HardwareInfo:
    """获取并处理硬件信息 - 与注册机完全相同的逻辑"""
    
    @staticmethod
    def get_cpu_id():
        """获取CPU ID"""
        if platform.system() == "Windows":
            try:
                # 使用WMI获取处理器ID
                result = subprocess.check_output('wmic cpu get ProcessorId').decode('utf-8').strip()
                lines = result.split('\n')
                if len(lines) >= 2:
                    return lines[1].strip()
            except:
                pass
        return "UNKNOWN_CPU"
    
    @staticmethod
    def get_disk_serial():
        """获取系统磁盘序列号"""
        if platform.system() == "Windows":
            try:
                result = subprocess.check_output('wmic diskdrive get SerialNumber').decode('utf-8').strip()
                lines = result.split('\n')
                if len(lines) >= 2:
                    return lines[1].strip()
            except:
                pass
        return "UNKNOWN_DISK"
    
    @staticmethod
    def get_bios_serial():
        """获取BIOS序列号"""
        if platform.system() == "Windows":
            try:
                result = subprocess.check_output('wmic bios get SerialNumber').decode('utf-8').strip()
                lines = result.split('\n')
                if len(lines) >= 2:
                    return lines[1].strip()
            except:
                pass
        return "UNKNOWN_BIOS"
    
    @staticmethod
    def get_mac_address():
        """获取第一个网络适配器的MAC地址"""
        if platform.system() == "Windows":
            try:
                result = subprocess.check_output('getmac /fo csv /nh').decode('utf-8')
                mac = result.split(',')[0].strip('"')
                return mac
            except:
                pass
        return "00:00:00:00:00:00"
    
    @staticmethod
    def get_machine_guid():
        """获取Windows的MachineGUID"""
        if platform.system() == "Windows":
            try:
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                    r"SOFTWARE\Microsoft\Cryptography")
                value, _ = winreg.QueryValueEx(key, "MachineGuid")
                winreg.CloseKey(key)
                return value
            except:
                pass
        return str(uuid.uuid4())
    
    # 确保其他方法完全相同...
    
    @staticmethod
    def generate_machine_id():
        """生成基于多种硬件信息的唯一机器ID"""
        # 收集硬件信息
        cpu_id = HardwareInfo.get_cpu_id()
        disk_serial = HardwareInfo.get_disk_serial()
        bios_serial = HardwareInfo.get_bios_serial()
        mac_address = HardwareInfo.get_mac_address()
        machine_guid = HardwareInfo.get_machine_guid()
        
        # 创建唯一标识字符串
        hardware_str = f"{cpu_id}|{disk_serial}|{bios_serial}|{mac_address}|{machine_guid}"
        
        # 使用SHA256哈希
        machine_hash = hashlib.sha256(hardware_str.encode()).hexdigest()
        
        # 返回格式化的机器ID (8-4-4-4-12格式)
        return f"{machine_hash[:8]}-{machine_hash[8:12]}-{machine_hash[12:16]}-{machine_hash[16:20]}-{machine_hash[20:32]}"


class LicenseChecker:
    """许可证检查器"""
    
    SECRET_KEY = "3fa85f64-5717-4562-b3fc-2c963f66afa6"  # 与注册机相同的密钥
    ENCRYPTION_KEY = b"YourStrongEncryptionKey123!@#$%^&*()"  # 用于本地加密的密钥
    API_ENDPOINT = "https://api.yourdomain.com/validate"  # 替换为实际的API端点
    BACKUP_ENDPOINTS = [  # 备用验证服务器
        "https://backup1.yourdomain.com/validate",
        "https://backup2.yourdomain.com/validate"
    ]
    
    @staticmethod
    def get_secure_time():
        """获取可信的当前时间，优先使用网络时间"""
        try:
            # 尝试从多个NTP服务器获取时间
            ntp_servers = [
                'https://worldtimeapi.org/api/ip',
                'https://timeapi.io/api/Time/current/zone?timeZone=UTC',
                'https://www.timeapi.io/api/Time/current/zone?timeZone=UTC',
                'http://api.m.taobao.com/rest/api3.do?api=mtop.common.getTimestamp'  # 淘宝API
            ]
            
            for server_url in ntp_servers:
                try:
                    response = requests.get(server_url, timeout=3)
                    if response.status_code == 200:
                        time_data = response.json()
                        if 'datetime' in time_data:
                            return datetime.fromisoformat(time_data['datetime'].replace('Z', '+00:00'))
                        elif 'dateTime' in time_data:
                            return datetime.fromisoformat(time_data['dateTime'].replace('Z', '+00:00'))
                        elif 'data' in time_data and 't' in time_data['data']:  # 淘宝API
                            timestamp = int(time_data['data']['t']) / 1000.0
                            return datetime.fromtimestamp(timestamp)
                except:
                    continue
            
            # 如果网络时间获取失败，使用系统时间，但记录警告
            logger.warning("无法获取网络时间，使用系统时间")
            return datetime.now()
        except Exception as e:
            logger.error(f"获取安全时间出错: {str(e)}")
            return datetime.now()  # 回退到系统时间
    
    @staticmethod
    def encrypt_data(data, key):
        """使用Fernet对数据进行加密"""
        f = Fernet(key)
        return f.encrypt(json.dumps(data).encode())
    
    @staticmethod
    def decrypt_data(encrypted_data, key):
        """解密数据"""
        try:
            f = Fernet(key)
            return json.loads(f.decrypt(encrypted_data).decode())
        except Exception as e:
            logger.error(f"解密失败: {str(e)}")
            return None
    
    @staticmethod
    def generate_signature(data, key):
        """生成HMAC签名以验证数据完整性"""
        message = json.dumps(data, sort_keys=True).encode()
        signature = hmac.new(key.encode(), message, hashlib.sha256).hexdigest()
        return signature
    
    @staticmethod
    def online_verify(activation_key, machine_id):
        """在线验证激活码"""
        try:
            # 准备验证数据
            verification_data = {
                "activation_key": activation_key,
                "machine_id": machine_id,
                "timestamp": datetime.now().isoformat(),
                "app_version": "1.0.0"  # 替换为实际版本号
            }
            
            # 添加签名
            verification_data["signature"] = LicenseChecker.generate_signature(
                verification_data, LicenseChecker.SECRET_KEY
            )
            
            # 尝试主服务器
            try:
                response = requests.post(
                    LicenseChecker.API_ENDPOINT, 
                    json=verification_data,
                    timeout=5  # 5秒超时
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("valid", False):
                        # 保存成功的验证结果用于离线验证
                        LicenseChecker.save_last_successful_validation(result)
                        return True, result.get("message", "验证成功")
                    else:
                        return False, result.get("message", "验证失败")
            except requests.RequestException:
                # 主服务器失败，尝试备用服务器
                for backup_url in LicenseChecker.BACKUP_ENDPOINTS:
                    try:
                        response = requests.post(
                            backup_url, 
                            json=verification_data,
                            timeout=5
                        )
                        
                        if response.status_code == 200:
                            result = response.json()
                            if result.get("valid", False):
                                # 保存成功的验证结果用于离线验证
                                LicenseChecker.save_last_successful_validation(result)
                                return True, result.get("message", "验证成功")
                            else:
                                return False, result.get("message", "验证失败")
                    except requests.RequestException:
                        continue
            
            # 所有服务器都失败，使用离线模式
            logger.warning("无法连接到验证服务器，尝试使用离线验证模式")
            cached_result = LicenseChecker.get_cached_validation()
            if cached_result:
                # 使用缓存的验证结果
                logger.info("使用缓存的验证结果")
                return True, "使用缓存验证 - " + cached_result.get("message", "验证成功")
            return False, "无法连接到验证服务器，且无有效的验证缓存"
            
        except Exception as e:
            logger.error(f"在线验证出错: {str(e)}")
            return False, f"在线验证出错: {str(e)}"

    @staticmethod
    def save_last_successful_validation(result):
        """保存最后一次成功的验证结果"""
        try:
            # 加密保存
            encrypted_data = LicenseChecker.encrypt_data(result, LicenseChecker.ENCRYPTION_KEY)
            with open(".activation", "wb") as f:
                f.write(encrypted_data)
        except Exception as e:
            logger.error(f"保存验证结果失败: {str(e)}")

    @staticmethod
    def get_cached_validation():
        """获取缓存的验证结果"""
        try:
            if os.path.exists(".activation"):
                with open(".activation", "rb") as f:
                    encrypted_data = f.read()
                return LicenseChecker.decrypt_data(encrypted_data, LicenseChecker.ENCRYPTION_KEY)
        except Exception as e:
            logger.error(f"获取缓存验证结果失败: {str(e)}")
            return None

    @staticmethod
    def check_activation():
        """检查激活状态
        
        返回:
            tuple: (is_activated, message, expiry_date)
                is_activated: 布尔值，表示是否已激活
                message: 字符串，激活状态消息
                expiry_date: datetime对象，激活过期时间，如果未激活则为None
        """
        try:
            # 获取机器ID
            machine_id = HardwareInfo.generate_machine_id()
            
            # 检查本地激活文件
            activation_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), ".activation")
            
            if not os.path.exists(activation_file):
                return False, "未找到激活信息", None
            
            # 读取激活数据
            try:
                with open(activation_file, "rb") as f:
                    encrypted_data = f.read()
                
                # 生成解密密钥
                key = LicenseChecker.generate_encryption_key(machine_id)
                
                # 解密数据
                activation_data = LicenseChecker.decrypt_data(encrypted_data, key)
                
                if not activation_data:
                    return False, "激活信息已损坏", None
                
                # 验证激活信息
                if activation_data.get("machine_id") != machine_id:
                    return False, "激活信息与当前设备不匹配", None
                
                # 检查过期时间
                expiry_date_str = activation_data.get("expires_at")
                if expiry_date_str:
                    expiry_date = datetime.fromisoformat(expiry_date_str)
                    if datetime.now() > expiry_date:
                        return False, "激活已过期", expiry_date
                    
                    # 如果即将过期（30天内），添加提示
                    days_left = (expiry_date - datetime.now()).days
                    if days_left <= 30:
                        return True, f"激活有效，剩余{days_left}天", expiry_date
                    
                    return True, "激活有效", expiry_date
                else:
                    # 无过期时间，视为永久激活
                    return True, "永久激活", None
                
            except Exception as e:
                logger.error(f"读取激活信息出错: {str(e)}")
                return False, f"读取激活信息出错: {str(e)}", None
            
        except Exception as e:
            logger.error(f"检查激活状态出错: {str(e)}")
            return False, f"检查激活状态出错: {str(e)}", None

    @staticmethod
    def generate_encryption_key(machine_id):
        """根据机器ID生成加密密钥"""
        try:
            # 使用机器ID和固定盐值生成密钥
            salt = b"LicenseCheckerStaticSalt"
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(machine_id.encode()))
            return key
        except Exception as e:
            logger.error(f"生成加密密钥失败: {str(e)}")
            # 返回一个默认密钥（不安全，但可防止程序崩溃）
            return LicenseChecker.ENCRYPTION_KEY


def debug_activation_process():
    """调试激活验证过程"""
    try:
        machine_id = HardwareInfo.generate_machine_id()
        print(f"当前机器ID: {machine_id}")
        
        # 检查license.dat
        if os.path.exists("license.dat"):
            try:
                with open("license.dat", "r") as f:
                    encoded_data = f.read()
                    decoded_data = base64.b64decode(encoded_data).decode('utf-8')
                    activation_info = json.loads(decoded_data)
                
                stored_machine_id = activation_info.get("machine_id", "")
                activation_key = activation_info.get("activation_key", "")
                
                print(f"存储的机器ID: {stored_machine_id}")
                print(f"存储的激活码: {activation_key}")
                print(f"ID匹配: {stored_machine_id == machine_id}")
                
                # 手动验证一下
                is_valid, message, expiry_date = LicenseChecker.verify_activation_key(
                    activation_key, machine_id
                )
                print(f"验证结果: {is_valid}")
                print(f"验证消息: {message}")
                
                # 如果验证失败，显示详细信息
                if not is_valid:
                    parts = activation_key.split('-')
                    date_code = parts[1][:2] + parts[2][:2] + parts[3][:2]
                    
                    expiry_year = int("20" + date_code[:2])
                    expiry_month = int(date_code[2:4])
                    expiry_day = int(date_code[4:6])
                    expiry_date = datetime(expiry_year, expiry_month, expiry_day, 23, 59, 59).replace(microsecond=0)
                    
                    # 提取哈希部分
                    hash_parts = [
                        parts[0],
                        parts[1][2:],
                        parts[2][2:],
                        parts[3][2:],
                        parts[4]
                    ]
                    
                    # 计算期望的哈希
                    expiry_stamp = int(expiry_date.replace(microsecond=0).timestamp())
                    to_encrypt = f"{machine_id}|{expiry_stamp}|{LicenseChecker.SECRET_KEY}"
                    expected_hash = hashlib.sha256(to_encrypt.encode()).hexdigest()
                    expected_parts = [expected_hash[i:i+5] for i in range(0, 25, 5)]
                    
                    print("\n哈希比较:")
                    for i in range(5):
                        match = "✓" if hash_parts[i].lower() == expected_parts[i].lower() else "✗"
                        print(f"部分 {i+1}: {hash_parts[i]} vs {expected_parts[i]} {match}")
            except Exception as e:
                print(f"读取license.dat出错: {str(e)}")
    except Exception as e:
        print(f"调试过程出错: {str(e)}")

def manual_validate_key():
    """手动验证激活码"""
    from datetime import datetime
    
    print("=== 手动激活码验证 ===")
    machine_id = HardwareInfo.generate_machine_id()
    print(f"当前机器ID: {machine_id}")
    
    # 输入激活码
    activation_key = input("请输入激活码: ").strip()
    
    # 验证激活码
    is_valid, message, expiry_date = LicenseChecker.verify_activation_key(activation_key, machine_id)
    print(f"验证结果: {is_valid}")
    print(f"消息: {message}")
    
    # 测试写入license.dat
    if is_valid:
        try:
            # 保存激活信息
            activation_info = {
                "machine_id": machine_id,
                "activation_key": activation_key,
                "activated_date": datetime.now().isoformat(),
                "expires_at": expiry_date.isoformat() if expiry_date else None
            }
            
            # 加密保存
            encoded_data = base64.b64encode(json.dumps(activation_info).encode('utf-8')).decode('utf-8')
            
            with open("license.dat", "w") as f:
                f.write(encoded_data)
            
            print("已保存激活信息到license.dat")
        except Exception as e:
            print(f"保存激活信息失败: {str(e)}")

# 修改主函数，移除登录检查
def main():
    # 添加调试信息
    debug_activation_process()
    
    # 创建应用
    app = QApplication(sys.argv)
    
    # 设置全局应用图标
    app_icon = QIcon("icon.ico")  # 替换为您实际的图标文件
    app.setWindowIcon(app_icon)
    
    # 设置应用不自动退出
    app.setQuitOnLastWindowClosed(False)
    
    # 应用样式
    AppStyles().apply_to_app(app)
    
    # 首先检查激活是否过期
    is_activated, message, expiry_date = LicenseChecker.check_activation()
    
    # 如果激活已过期，直接提示并退出
    if is_activated and expiry_date and datetime.now() > expiry_date:
        QMessageBox.critical(None, "激活已过期", 
                            f"您的软件许可已于 {expiry_date.strftime('%Y-%m-%d')} 到期。\n\n"
                            "请联系软件供应商获取新的激活码。")
        sys.exit(0)
    
    # 始终显示激活对话框，无论是否已激活
    activation_dialog = HardwareActivationDialog(None, LicenseChecker, HardwareInfo)
    if activation_dialog.exec() != QDialog.DialogCode.Accepted:
        # 用户取消激活或激活失败，退出应用
        sys.exit(0)
    
    # 激活成功后直接创建并显示主窗口
    window = GameMemoryTool()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    # 取消注释下面一行以进行手动测试
    # manual_validate_key()
    main()