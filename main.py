import os
import sys
import ctypes
import psutil
import time
import threading
from ctypes import wintypes
import logging
from datetime import datetime
import json
import hashlib
import uuid
import base64
import winreg
import subprocess
import platform
from datetime import datetime, timedelta
import struct
import numpy as np

# 添加keyboard库导入
import keyboard

from PyQt6.QtCore import Qt, QTimer, QSize, QPropertyAnimation, QRect, QEvent, QAbstractNativeEventFilter
from PyQt6.QtGui import QIcon, QPixmap, QColor, QFont, QAction, QKeySequence
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QLabel, QPushButton, QVBoxLayout,
    QHBoxLayout, QGroupBox, QTabWidget, QComboBox, QLineEdit, QCheckBox,
    QFrame, QMessageBox, QStatusBar, QStyleFactory, QSizePolicy, QSpacerItem,
    QFormLayout, QSlider, QMenu, QToolBar, QToolButton, QSplitter, QSystemTrayIcon,
    QGridLayout, QListWidget, QDialog, QDialogButtonBox, QTabWidget, QStackedWidget, QRadioButton
)

from memory_reader import MemoryReader
from battle_logic import BattleManager
from monster_select import MonsterSelector
from input_simulator import InputSimulator
from ui.main_window import MainWindow
from auto_combat_system import AutoCombatSystem

# 添加增强AI相关导入
from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from collections import deque
import queue

# ==================== 增强AI战斗决策系统 ====================

class AttackIntention(Enum):
    """攻击意图枚举 - 基于Aion服务端AI"""
    FINISH_ATTACK = "finish_attack"
    SWITCH_TARGET = "switch_target"
    SKILL_ATTACK = "skill_attack"
    SIMPLE_ATTACK = "simple_attack"
    DEFENSIVE_ACTION = "defensive_action"
    BUFF_SELF = "buff_self"
    HEAL_SELF = "heal_self"

class BattleState(Enum):
    """战斗状态枚举"""
    IDLE = "idle"
    SEARCHING = "searching"
    ENGAGING = "engaging"
    FIGHTING = "fighting"
    RETREATING = "retreating"

@dataclass
class Target:
    """目标信息"""
    id: int
    name: str = ""
    hp_percent: float = 100.0
    distance: float = 0.0
    threat_level: int = 1
    last_seen: float = 0.0
    is_alive: bool = True
    aggro_level: int = 0

@dataclass
class Skill:
    """技能信息"""
    id: str
    key: str
    cooldown: float
    damage: int
    range: float
    mana_cost: int
    cast_time: float
    priority: int
    skill_type: str  # attack, buff, heal, debuff
    last_used: float = 0.0
    is_ready: bool = True

class EnhancedBattleAI:
    """增强战斗AI系统 - 基于Aion服务端逻辑"""
    
    def __init__(self, memory_reader, input_simulator, monster_selector):
        """初始化增强战斗AI"""
        self.memory = memory_reader
        self.input = input_simulator
        self.monster_selector = monster_selector
        
        # 核心AI状态
        self.is_active = False
        self.battle_state = BattleState.IDLE
        self.current_target: Optional[Target] = None
        self.target_list: List[Target] = []
        self.aggro_list: Dict[int, int] = {}  # 仇恨列表
        
        # AI思考循环
        self.think_thread = None
        self.think_active = False
        self.think_interval = 0.1  # 100ms思考间隔
        
        # 技能系统 - 基于服务端NPC技能逻辑
        self.skills: Dict[str, Skill] = {}
        self.skill_queue = queue.Queue()
        self.global_cooldown = 0.05
        self.last_skill_time = 0.0
        
        # 目标选择算法
        self.target_selection_algorithm = "most_hated"  # most_hated, nearest, weakest
        self.target_switch_cooldown = 1.0  # 目标切换冷却
        self.last_target_switch = 0.0
        
        # 战斗参数
        self.attack_range = 30.0
        self.retreat_hp_threshold = 20.0
        self.heal_hp_threshold = 50.0
        self.buff_duration = 300.0  # 5分钟buff持续时间
        
        # 智能决策系统
        self.decision_weights = {
            "attack": 1.0,
            "skill": 1.5,
            "heal": 2.0,
            "buff": 0.8,
            "retreat": 3.0
        }
        
        # 学习系统
        self.battle_history = deque(maxlen=100)
        self.success_patterns = {}
        self.failure_patterns = {}
        
        # 初始化技能系统
        self.initialize_skills()
        
        # 游戏日志回调
        self.game_log_callback = None
        
        logger.info("增强战斗AI系统初始化完成")

    def initialize_skills(self):
        """初始化技能系统 - 基于服务端技能配置"""
        # 基础攻击技能 (1-9)
        attack_skills = [
            Skill("attack1", "1", 0.05, 100, 25.0, 10, 0.5, 1, "attack"),
            Skill("attack2", "2", 0.05, 120, 25.0, 15, 0.6, 2, "attack"),
            Skill("attack3", "3", 0.05, 140, 25.0, 20, 0.7, 3, "attack"),
            Skill("attack4", "4", 0.05, 160, 25.0, 25, 0.8, 4, "attack"),
            Skill("attack5", "5", 0.05, 180, 25.0, 30, 0.9, 5, "attack"),
            Skill("attack6", "6", 0.05, 200, 25.0, 35, 1.0, 6, "attack"),
            Skill("attack7", "7", 0.05, 220, 25.0, 40, 1.1, 7, "attack"),
            Skill("attack8", "8", 0.05, 240, 25.0, 45, 1.2, 8, "attack"),
            Skill("attack9", "9", 0.05, 260, 25.0, 50, 1.3, 9, "attack"),
        ]
        
        # Alt组合技能 (Alt+1到Alt+9高级技能)
        alt_skills = [
            Skill("alt_attack1", "alt+1", 2.0, 300, 30.0, 80, 1.5, 10, "skill"),
            Skill("alt_attack2", "alt+2", 2.5, 350, 30.0, 90, 1.8, 11, "skill"),
            Skill("alt_attack3", "alt+3", 3.0, 400, 30.0, 100, 2.0, 12, "skill"),
            Skill("alt_attack4", "alt+4", 3.5, 450, 30.0, 110, 2.2, 13, "skill"),
            Skill("alt_attack5", "alt+5", 4.0, 500, 30.0, 120, 2.5, 14, "skill"),
            Skill("alt_attack6", "alt+6", 4.5, 550, 30.0, 130, 2.8, 15, "skill"),
            Skill("alt_attack7", "alt+7", 5.0, 600, 30.0, 140, 3.0, 16, "skill"),
            Skill("alt_attack8", "alt+8", 5.5, 650, 30.0, 150, 3.2, 17, "skill"),
            Skill("alt_attack9", "alt+9", 6.0, 700, 30.0, 160, 3.5, 18, "skill"),
        ]
        
        # 辅助技能
        support_skills = [
            Skill("heal", "h", 5.0, 0, 0.0, 100, 2.0, 100, "heal"),
            Skill("buff", "b", 10.0, 0, 0.0, 50, 1.0, 90, "buff"),
        ]
        
        # 合并所有技能
        all_skills = attack_skills + alt_skills + support_skills
        
        for skill in all_skills:
            self.skills[skill.id] = skill
        
        logger.info(f"已初始化 {len(self.skills)} 个技能")

    def start(self):
        """启动增强战斗AI"""
        if self.is_active:
            return
        
        self.is_active = True
        self.battle_state = BattleState.SEARCHING
        
        # 启动AI思考线程
        self.think_active = True
        self.think_thread = threading.Thread(target=self._think_loop, daemon=True)
        self.think_thread.start()
        
        self._log("增强战斗AI已启动")

    def stop(self):
        """停止增强战斗AI"""
        if not self.is_active:
            return
        
        self.is_active = False
        self.think_active = False
        self.battle_state = BattleState.IDLE
        
        if self.think_thread and self.think_thread.is_alive():
            self.think_thread.join(timeout=1.0)
        
        self._log("增强战斗AI已停止")

    def _think_loop(self):
        """AI思考循环 - 核心决策引擎"""
        while self.think_active:
            try:
                # 执行AI思考
                self._think()
                time.sleep(self.think_interval)
            except Exception as e:
                logger.error(f"AI思考循环错误: {e}")
                time.sleep(0.5)

    def _think(self):
        """AI思考方法 - 基于Aion服务端AI逻辑"""
        if not self.is_active:
            return
        
        # 1. 更新目标列表
        self._update_targets()
        
        # 2. 选择攻击意图
        intention = self._choose_attack_intention()
        
        # 3. 执行攻击意图
        self._execute_intention(intention)
        
        # 4. 更新战斗状态
        self._update_battle_state()

    def _update_targets(self):
        """更新目标列表"""
        try:
            # 从怪物选择器获取目标
            monsters = self.monster_selector.get_nearby_monsters(self.attack_range)
            
            # 更新目标列表
            self.target_list.clear()
            current_time = time.time()
            
            for monster in monsters:
                target = Target(
                    id=monster.get('id', 0),
                    name=monster.get('name', '未知怪物'),
                    hp_percent=monster.get('hp_percent', 100.0),
                    distance=monster.get('distance', 999.0),
                    threat_level=self._calculate_threat_level(monster),
                    last_seen=current_time,
                    is_alive=monster.get('hp_percent', 0) > 0
                )
                self.target_list.append(target)
            
        except Exception as e:
            logger.error(f"更新目标列表错误: {e}")

    def _choose_attack_intention(self) -> AttackIntention:
        """选择攻击意图 - 基于Aion服务端chooseAttackIntention逻辑"""
        current_time = time.time()
        
        # 检查当前目标
        most_hated = self._get_most_hated_target()
        
        # 1. 如果没有目标或目标已死亡，结束攻击
        if not most_hated or not most_hated.is_alive:
            return AttackIntention.FINISH_ATTACK
        
        # 2. 如果当前目标不是最仇恨目标，切换目标
        if (self.current_target is None or 
            self.current_target.id != most_hated.id):
            if current_time - self.last_target_switch > self.target_switch_cooldown:
                return AttackIntention.SWITCH_TARGET
        
        # 3. 检查是否需要治疗
        player_hp = self._get_player_hp_percent()
        if player_hp < self.heal_hp_threshold:
            return AttackIntention.HEAL_SELF
        
        # 4. 检查是否需要buff
        if self._should_buff():
            return AttackIntention.BUFF_SELF
        
        # 5. 选择技能攻击
        best_skill = self._choose_best_skill(most_hated)
        if best_skill:
            return AttackIntention.SKILL_ATTACK
        
        # 6. 默认普通攻击
        return AttackIntention.SIMPLE_ATTACK

    def _execute_intention(self, intention: AttackIntention):
        """执行攻击意图"""
        current_time = time.time()
        
        if intention == AttackIntention.FINISH_ATTACK:
            self._finish_attack()
        
        elif intention == AttackIntention.SWITCH_TARGET:
            self._switch_target()
        
        elif intention == AttackIntention.SKILL_ATTACK:
            self._execute_skill_attack()
        
        elif intention == AttackIntention.SIMPLE_ATTACK:
            self._execute_simple_attack()
        
        elif intention == AttackIntention.HEAL_SELF:
            self._execute_heal()
        
        elif intention == AttackIntention.BUFF_SELF:
            self._execute_buff()

    def _get_most_hated_target(self) -> Optional[Target]:
        """获取最仇恨目标 - 基于服务端getAggroList().getMostHated()"""
        if not self.target_list:
            return None
        
        # 根据仇恨值、距离、血量等因素选择目标
        best_target = None
        best_score = -1
        
        for target in self.target_list:
            if not target.is_alive:
                continue
            
            # 计算目标评分
            score = self._calculate_target_score(target)
            
            if score > best_score:
                best_score = score
                best_target = target
        
        return best_target

    def _calculate_target_score(self, target: Target) -> float:
        """计算目标评分 - 多因素评估算法"""
        score = 0.0
        
        # 仇恨值权重 (最重要)
        aggro = self.aggro_list.get(target.id, 0)
        score += aggro * 10.0
        
        # 距离权重（越近越好）
        if target.distance > 0:
            score += (50.0 - target.distance) * 2.0
        
        # 血量权重（优先攻击残血）
        if target.hp_percent < 30:
            score += 20.0
        elif target.hp_percent < 60:
            score += 10.0
        
        # 威胁等级权重
        score += target.threat_level * 5.0
        
        return score

    def _choose_best_skill(self, target: Target) -> Optional[Skill]:
        """选择最佳技能 - 基于服务端SkillAttackManager.chooseNextSkill"""
        if not target:
            return None
        
        current_time = time.time()
        available_skills = []
        
        # 筛选可用技能
        for skill in self.skills.values():
            if (skill.skill_type in ["attack", "skill"] and
                current_time - skill.last_used >= skill.cooldown and
                target.distance <= skill.range):
                available_skills.append(skill)
        
        if not available_skills:
            return None
        
        # 根据优先级和伤害选择最佳技能
        best_skill = max(available_skills, 
                        key=lambda s: s.damage * (1.0 / s.priority))
        
        return best_skill

    def _execute_skill_attack(self):
        """执行技能攻击"""
        if not self.current_target:
            return
        
        skill = self._choose_best_skill(self.current_target)
        if not skill:
            return
        
        current_time = time.time()
        
        # 检查全局冷却
        if current_time - self.last_skill_time < self.global_cooldown:
            return
        
        # 执行技能
        try:
            self.input.press_key(skill.key)
            skill.last_used = current_time
            self.last_skill_time = current_time
            
            # 更新仇恨值
            if self.current_target.id in self.aggro_list:
                self.aggro_list[self.current_target.id] += skill.damage // 10
            else:
                self.aggro_list[self.current_target.id] = skill.damage // 10
            
            self._log(f"使用技能: {skill.id} -> {self.current_target.name}")
            
        except Exception as e:
            logger.error(f"执行技能攻击错误: {e}")

    def _execute_simple_attack(self):
        """执行普通攻击"""
        if not self.current_target:
            return
        
        try:
            # 使用基础攻击技能
            basic_skill = self.skills.get("attack1")
            if basic_skill:
                current_time = time.time()
                if current_time - basic_skill.last_used >= basic_skill.cooldown:
                    self.input.press_key(basic_skill.key)
                    basic_skill.last_used = current_time
                    self.last_skill_time = current_time
                    
                    self._log(f"普通攻击: {self.current_target.name}")
            
        except Exception as e:
            logger.error(f"执行普通攻击错误: {e}")

    def _switch_target(self):
        """切换目标"""
        new_target = self._get_most_hated_target()
        if new_target and new_target != self.current_target:
            old_target_name = self.current_target.name if self.current_target else "无"
            self.current_target = new_target
            self.last_target_switch = time.time()
            
            # 选择目标
            try:
                self.monster_selector.select_monster_by_id(new_target.id)
                self._log(f"切换目标: {old_target_name} -> {new_target.name}")
            except Exception as e:
                logger.error(f"切换目标错误: {e}")

    def _finish_attack(self):
        """结束攻击"""
        self.current_target = None
        self.battle_state = BattleState.SEARCHING
        self._log("结束攻击，搜索新目标")

    def _execute_heal(self):
        """执行治疗"""
        heal_skill = self.skills.get("heal")
        if heal_skill:
            current_time = time.time()
            if current_time - heal_skill.last_used >= heal_skill.cooldown:
                try:
                    self.input.press_key(heal_skill.key)
                    heal_skill.last_used = current_time
                    self._log("使用治疗技能")
                except Exception as e:
                    logger.error(f"执行治疗错误: {e}")

    def _execute_buff(self):
        """执行buff"""
        buff_skill = self.skills.get("buff")
        if buff_skill:
            current_time = time.time()
            if current_time - buff_skill.last_used >= buff_skill.cooldown:
                try:
                    self.input.press_key(buff_skill.key)
                    buff_skill.last_used = current_time
                    self._log("使用buff技能")
                except Exception as e:
                    logger.error(f"执行buff错误: {e}")

    def _update_battle_state(self):
        """更新战斗状态"""
        if not self.target_list:
            self.battle_state = BattleState.SEARCHING
        elif self.current_target:
            self.battle_state = BattleState.FIGHTING
        else:
            self.battle_state = BattleState.ENGAGING

    def _calculate_threat_level(self, monster) -> int:
        """计算威胁等级"""
        # 基于怪物等级、血量、距离等计算威胁等级
        level = monster.get('level', 1)
        hp = monster.get('hp_percent', 100)
        distance = monster.get('distance', 999)
        
        threat = level
        if hp > 80:
            threat += 2
        if distance < 10:
            threat += 1
        
        return min(threat, 10)  # 最大威胁等级为10

    def _get_player_hp_percent(self) -> float:
        """获取玩家血量百分比"""
        try:
            # 从内存读取器获取玩家血量
            return self.memory.get_player_hp_percent()
        except:
            return 100.0  # 默认满血

    def _should_buff(self) -> bool:
        """判断是否需要buff"""
        # 简单的buff判断逻辑
        buff_skill = self.skills.get("buff")
        if not buff_skill:
            return False
        
        current_time = time.time()
        return current_time - buff_skill.last_used >= self.buff_duration

    def _log(self, message: str):
        """记录日志"""
        logger.info(message)
        if self.game_log_callback:
            self.game_log_callback(message)

    def set_log_callback(self, callback):
        """设置游戏日志回调"""
        self.game_log_callback = callback

    def get_status(self) -> Dict:
        """获取AI状态"""
        return {
            "is_active": self.is_active,
            "battle_state": self.battle_state.value,
            "current_target": self.current_target.name if self.current_target else "无",
            "target_count": len(self.target_list),
            "skills_ready": sum(1 for s in self.skills.values() 
                              if time.time() - s.last_used >= s.cooldown)
        }

    def update_settings(self, settings: Dict):
        """更新AI设置"""
        if "attack_range" in settings:
            self.attack_range = settings["attack_range"]
        if "heal_threshold" in settings:
            self.heal_hp_threshold = settings["heal_threshold"]
        if "target_algorithm" in settings:
            self.target_selection_algorithm = settings["target_algorithm"]
        
        self._log("AI设置已更新")

    def update(self):
        """更新方法 - 兼容原有接口"""
        # 这个方法保持为空，因为AI思考在独立线程中运行
        pass

# ==================== 增强AI战斗决策系统结束 ====================

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('game_memory_tool.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('game_memory_tool')

# 必要的Windows API常量
PROCESS_ALL_ACCESS = 0x1F0FFF
PROCESS_VM_OPERATION = 0x0008
PROCESS_VM_READ = 0x0010
PROCESS_VM_WRITE = 0x0020
PROCESS_QUERY_INFORMATION = 0x0400
PROCESS_REQUIRED_ACCESS = PROCESS_VM_OPERATION | PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_QUERY_INFORMATION

# 全局热键API常量
WM_HOTKEY = 0x0312
MOD_ALT = 0x0001
MOD_CONTROL = 0x0002
MOD_SHIFT = 0x0004
MOD_WIN = 0x0008
VK_HOME = 0x24  # Home键的虚拟键码

# 加载必要的Windows API
kernel32 = ctypes.WinDLL('kernel32', use_last_error=True)
user32 = ctypes.WinDLL('user32', use_last_error=True)

# 定义Windows API函数
OpenProcess = kernel32.OpenProcess
OpenProcess.argtypes = [wintypes.DWORD, wintypes.BOOL, wintypes.DWORD]
OpenProcess.restype = wintypes.HANDLE

ReadProcessMemory = kernel32.ReadProcessMemory
ReadProcessMemory.argtypes = [wintypes.HANDLE, wintypes.LPCVOID, wintypes.LPVOID, ctypes.c_size_t, ctypes.POINTER(ctypes.c_size_t)]
ReadProcessMemory.restype = wintypes.BOOL

WriteProcessMemory = kernel32.WriteProcessMemory
WriteProcessMemory.argtypes = [wintypes.HANDLE, wintypes.LPVOID, wintypes.LPCVOID, ctypes.c_size_t, ctypes.POINTER(ctypes.c_size_t)]
WriteProcessMemory.restype = wintypes.BOOL

CloseHandle = kernel32.CloseHandle
CloseHandle.argtypes = [wintypes.HANDLE]
CloseHandle.restype = wintypes.BOOL

# 游戏版本数据
game_versions = {
    "4.5核心8880版[64位]": {
        "level1_offset": "70",
        "level2_offset": "10",
        "level3_offset": "20",
        "base_offset": "DC8918",
        "character_base": "368",
        "attack_speed": "4fa",
        "move_speed": "6cc",
        "air_lock": "8d8",
        "height_base": "180",
        "attack_range": "1211B30",
        "show_hp": "547109",
        "x_coord": "98",
        "y_coord": "9c",
        "z_coord": "a0",
        "qsk_offset": "DC9F98",
        "skill_speed_adjust": "1211C08",
        "attack_speed_data": "2853a0",
        "wing": "1211C14",
        "view_offset": "0120B4E0",
        "view_offset1": "4BC",
        "map_offset": "D99DDC",
        "stealth_offset": "547109",  # 添加反隐地址
        "teleport_speed": "37B31BA0"  # 添加瞬移加速基值地址
    },
    "4.5核心8232版[64位]": {
        "level1_offset": "70",
        "level2_offset": "10",
        "level3_offset": "20",
        "base_offset": "DC7918",
        "character_base": "368",
        "attack_speed": "4fa",
        "move_speed": "6cc",
        "air_lock": "8d8",
        "height_base": "180",
        "attack_range": "1210AE0",
        "show_hp": "546759",
        "x_coord": "98",
        "y_coord": "9c",
        "z_coord": "a0",
        "qsk_offset": "DC8F98",
        "skill_speed_adjust": "1210BB8",
        "attack_speed_data": "284e60",
        "wing": "1210BC4",
        "view_offset": "0120A4E0",
        "view_offset1": "4BC",
        "map_offset": "D98DDC",
        "stealth_offset": "546759",  # 添加反隐地址
        "teleport_speed": "37B31BA0"  # 添加瞬移加速基值地址
    }
}

# 当前选择的版本
current_version = "4.5核心8880版[64位]"

# 配置数据
offsets = {
    "base_offset": 0,
    "level1_offset": 0,
    "level2_offset": 0,
    "level3_offset": 0,
    "character_base": 0,
    "move_speed": 0,
    "attack_speed": 0,
    "air_lock": 0,
    "height_base": 0,
    "x_coord": 0,
    "y_coord": 0,
    "z_coord": 0
}

# 功能状态
features_active = {
    "attack_speed": False,
    "move_speed": False,
    "air_lock": False,
    "fly": False,
    "stealth": False,
    "teleport_speed": False,  # 添加瞬移加速功能状态
    "view_range": False,      # 添加视野扩展功能状态
    "show_hp": False,         # 添加显血功能状态
    "map_extend": False,      # 添加地图扩展功能状态
    "wing_nocd": False,       # 添加翅膀无CD功能状态
    "skill_speed_adjust": False, # 添加攻击速度微调功能状态
    "qsk": False,             # 添加QSK秒起功能状态
    "attack_range": False     # 添加攻击距离功能状态
}

# 定时器
timers = {}

# 内存辅助类
class MemoryHelper:
    """内存读写辅助类"""
    def __init__(self):
        self.process_handle = None
        self.process_id = None
        self.module_base = 0
    
    def open_process(self, process_id):
        """打开进程以允许内存操作"""
        self.process_handle = OpenProcess(PROCESS_REQUIRED_ACCESS, False, process_id)
        if self.process_handle:
            self.process_id = process_id
            return True
        else:
            error_code = ctypes.get_last_error()
            logger.error(f"打开进程失败，错误码: {error_code}")
            return False
    
    def close_process(self):
        """关闭进程句柄"""
        if self.process_handle:
            CloseHandle(self.process_handle)
            self.process_handle = None
            self.process_id = None
            self.module_base = 0
    
    def read_bytes(self, address, size):
        """从指定地址读取字节"""
        if not self.process_handle:
            return None
        
        buffer = ctypes.create_string_buffer(size)
        bytes_read = ctypes.c_size_t(0)
        
        result = ReadProcessMemory(
            self.process_handle,
            ctypes.c_void_p(address),
            buffer,
            size,
            ctypes.byref(bytes_read)
        )
        
        if result:
            return buffer.raw
        else:
            error_code = ctypes.get_last_error()
            logger.error(f"读取内存失败，地址: 0x{address:X}, 错误码: {error_code}")
            return None
    
    def write_bytes(self, address, data):
        """向指定地址写入字节"""
        if not self.process_handle:
            return False
        
        buffer = bytes(data)
        bytes_written = ctypes.c_size_t(0)
        
        result = WriteProcessMemory(
            self.process_handle,
            ctypes.c_void_p(address),
            buffer,
            len(buffer),
            ctypes.byref(bytes_written)
        )
        
        if result:
            return True
        else:
            error_code = ctypes.get_last_error()
            logger.error(f"写入内存失败，地址: 0x{address:X}, 错误码: {error_code}")
            return False
    
    def read_int(self, address):
        """读取整数"""
        data = self.read_bytes(address, 4)
        if data:
            return struct.unpack("<i", data)[0]
        return None
    
    def read_uint(self, address):
        """读取无符号整数"""
        data = self.read_bytes(address, 4)
        if data:
            return struct.unpack("<I", data)[0]
        return None
    
    def read_long(self, address):
        """读取长整数"""
        data = self.read_bytes(address, 8)
        if data:
            return struct.unpack("<q", data)[0]
        return None
    
    def read_ulong(self, address):
        """读取无符号长整数"""
        data = self.read_bytes(address, 8)
        if data:
            return struct.unpack("<Q", data)[0]
        return None
    
    def read_float(self, address):
        """读取浮点数"""
        data = self.read_bytes(address, 4)
        if data:
            return struct.unpack("<f", data)[0]
        return None
    
    def read_double(self, address):
        """读取双精度浮点数"""
        data = self.read_bytes(address, 8)
        if data:
            return struct.unpack("<d", data)[0]
        return None
    
    def read_string(self, address, max_length=256):
        """读取字符串"""
        data = self.read_bytes(address, max_length)
        if data:
            try:
                null_pos = data.index(b'\x00')
                return data[:null_pos].decode('utf-8', errors='ignore')
            except ValueError:
                return data.decode('utf-8', errors='ignore')
        return None
    
    def write_int(self, address, value):
        """写入整数"""
        data = struct.pack("<i", value)
        return self.write_bytes(address, data)
    
    def write_uint(self, address, value):
        """写入无符号整数"""
        data = struct.pack("<I", value)
        return self.write_bytes(address, data)
    
    def write_long(self, address, value):
        """写入长整数"""
        data = struct.pack("<q", value)
        return self.write_bytes(address, data)
    
    def write_ulong(self, address, value):
        """写入无符号长整数"""
        data = struct.pack("<Q", value)
        return self.write_bytes(address, data)
    
    def write_float(self, address, value):
        """写入浮点数"""
        data = struct.pack("<f", value)
        return self.write_bytes(address, data)
    
    def write_double(self, address, value):
        """写入双精度浮点数"""
        data = struct.pack("<d", value)
        return self.write_bytes(address, data)
    
    def write_string(self, address, value):
        """写入字符串"""
        data = value.encode('utf-8') + b'\x00'
        return self.write_bytes(address, data)

# 锁空功能
def toggle_air_lock(memory_helper, checked):
    """切换锁空状态"""
    if not memory_helper or not memory_helper.process_handle:
        return False
    
    try:
        # 获取锁空地址
        base_offset = int(game_versions[current_version]["base_offset"], 16)
        level1_offset = int(game_versions[current_version]["level1_offset"], 16)
        level2_offset = int(game_versions[current_version]["level2_offset"], 16)
        level3_offset = int(game_versions[current_version]["level3_offset"], 16)
        character_base = int(game_versions[current_version]["character_base"], 16)
        air_lock_offset = int(game_versions[current_version]["air_lock"], 16)
        
        # 读取多级指针
        base_addr = memory_helper.read_long(memory_helper.module_base + base_offset)
        if not base_addr:
            return False
            
        level1_addr = memory_helper.read_long(base_addr + level1_offset)
        if not level1_addr:
            return False
            
        level2_addr = memory_helper.read_long(level1_addr + level2_offset)
        if not level2_addr:
            return False
            
        level3_addr = memory_helper.read_long(level2_addr + level3_offset)
        if not level3_addr:
            return False
            
        character_addr = memory_helper.read_long(level3_addr + character_base)
        if not character_addr:
            return False
            
        air_lock_addr = character_addr + air_lock_offset
        
        if checked:
            # 写入锁空值
            result = memory_helper.write_int(air_lock_addr, 5)
        else:
            # 写入解除锁空值
            result = memory_helper.write_int(air_lock_addr, 0)
        
        return result
    except Exception as e:
        logger.error(f"切换锁空状态时出错: {str(e)}")
        return False

# 飞天功能
def toggle_fly(memory_helper, checked, height_value=5.0, direction="up"):
    """飞天功能"""
    if not memory_helper or not memory_helper.process_handle:
        return False
    
    try:
        # 获取高度地址
        base_offset = int(game_versions[current_version]["base_offset"], 16)
        level1_offset = int(game_versions[current_version]["level1_offset"], 16)
        level2_offset = int(game_versions[current_version]["level2_offset"], 16)
        level3_offset = int(game_versions[current_version]["level3_offset"], 16)
        height_offset = int(game_versions[current_version]["height_base"], 16)
        z_coord_offset = int(game_versions[current_version]["z_coord"], 16)
        
        # 读取多级指针
        base_addr = memory_helper.read_long(memory_helper.module_base + base_offset)
        if not base_addr:
            return False
            
        level1_addr = memory_helper.read_long(base_addr + level1_offset)
        if not level1_addr:
            return False
            
        level2_addr = memory_helper.read_long(level1_addr + level2_offset)
        if not level2_addr:
            return False
            
        level3_addr = memory_helper.read_long(level2_addr + level3_offset)
        if not level3_addr:
            return False
            
        height_addr = memory_helper.read_long(level3_addr + height_offset)
        if not height_addr:
            return False
            
        z_addr = height_addr + z_coord_offset
        
        # 读取当前高度
        current_height = memory_helper.read_float(z_addr)
        if current_height is None:
            return False
            
        # 计算新高度
        if direction == "up":
            new_height = current_height + height_value
        else:
            new_height = current_height - height_value
        
        # 写入新高度
        result = memory_helper.write_float(z_addr, new_height)
        return result
    except Exception as e:
        logger.error(f"飞天功能出错: {str(e)}")
        return False

# 反隐功能
def toggle_stealth(memory_helper, checked):
    """切换反隐状态"""
    if not memory_helper or not memory_helper.process_handle:
        return False
    
    try:
        # 获取反隐地址
        stealth_offset = int(game_versions[current_version]["stealth_offset"], 16)
        stealth_addr = memory_helper.module_base + stealth_offset
        
        if checked:
            # 写入反隐值
            result = memory_helper.write_bytes(stealth_addr, bytearray([0x90, 0x90]))
        else:
            # 恢复原始值
            result = memory_helper.write_bytes(stealth_addr, bytearray([0x74, 0x09]))
        
        return result
    except Exception as e:
        logger.error(f"切换反隐状态时出错: {str(e)}")
        return False

# 视野扩展功能
def toggle_view_range(memory_helper, checked, view_value=100.0):
    """切换视野扩展状态"""
    if not memory_helper or not memory_helper.process_handle:
        return False
    
    global view_range_timer
    
    try:
        if checked:
            # 创建定时器，持续更新视野范围
            if view_range_timer is None:
                view_range_timer = QTimer()
                view_range_timer.timeout.connect(lambda: update_view_range(memory_helper, view_value))
            
            view_range_timer.start(100)  # 每100ms更新一次
            
            # 立即更新一次视野范围
            update_view_range(memory_helper, view_value)
        else:
            # 关闭视野扩展
            if view_range_timer:
                view_range_timer.stop()
            
            # 恢复默认视野
            view_offset = int(game_versions[current_version]["view_offset"], 16)
            view_offset1 = int(game_versions[current_version]["view_offset1"], 16)
            
            view_base1 = memory_helper.module_base + view_offset
            view_base2 = memory_helper.read_long(view_base1)
            if view_base2:
                view_base3 = view_base2 + view_offset1
                memory_helper.write_float(view_base3, 35.0)  # 恢复默认视野值
        
        return True
    except Exception as e:
        logger.error(f"切换视野扩展状态时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def update_view_range(memory_helper, view_value):
    """定时更新视野范围"""
    if not memory_helper or not memory_helper.process_handle:
        return
        
    try:
        # 获取视野地址
        view_offset = int(game_versions[current_version]["view_offset"], 16)
        view_offset1 = int(game_versions[current_version]["view_offset1"], 16)
        
        view_base1 = memory_helper.module_base + view_offset
        view_base2 = memory_helper.read_long(view_base1)
        if not view_base2:
            logger.warning("无法读取视野基址")
            return
            
        view_base3 = view_base2 + view_offset1
        
        # 写入视野值
        result = memory_helper.write_float(view_base3, view_value)
        if not result:
            logger.warning(f"写入视野值失败: {view_value}")
        
    except Exception as e:
        logger.error(f"更新视野范围时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        # 出错时停止计时器
        global view_range_timer
        if view_range_timer:
            view_range_timer.stop()

# 显血功能
def toggle_show_hp(memory_helper, checked):
    """切换显血状态"""
    if not memory_helper or not memory_helper.process_handle:
        return False
    
    try:
        # 获取显血地址
        show_hp_offset = int(game_versions[current_version]["show_hp"], 16)
        show_hp_addr = memory_helper.module_base + show_hp_offset
        
        if checked:
            # 写入NOP指令
            patch_bytes = bytearray([0x90, 0x90])
            result = memory_helper.write_bytes(show_hp_addr, patch_bytes)
        else:
            # 恢复原始指令
            original_bytes = bytearray([0x74, 0x1B])  # 原始跳转指令
            result = memory_helper.write_bytes(show_hp_addr, original_bytes)
        
        return result
    except Exception as e:
        logger.error(f"切换显血功能时出错: {str(e)}")
        return False

# 地图扩展功能
def toggle_map_extend(memory_helper, checked, map_value=200.0):
    """切换地图扩展状态"""
    if not memory_helper or not memory_helper.process_handle:
        return False
    
    global map_extend_timer
    
    try:
        if checked:
            # 创建定时器，持续更新地图范围
            if map_extend_timer is None:
                map_extend_timer = QTimer()
                map_extend_timer.timeout.connect(lambda: update_map_extend(memory_helper, map_value))
            
            map_extend_timer.start(100)  # 每100ms更新一次
            
            # 立即更新一次地图范围
            update_map_extend(memory_helper, map_value)
        else:
            # 关闭地图扩展
            if map_extend_timer:
                map_extend_timer.stop()
            
            # 恢复默认地图范围
            map_offset = int(game_versions[current_version]["map_offset"], 16)
            map_addr = memory_helper.module_base + map_offset
            memory_helper.write_float(map_addr, 90.0)  # 恢复默认地图值
        
        return True
    except Exception as e:
        logger.error(f"切换地图扩展状态时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def update_map_extend(memory_helper, map_value):
    """定时更新地图范围"""
    if not memory_helper or not memory_helper.process_handle:
        return
        
    try:
        # 获取地图地址
        map_offset = int(game_versions[current_version]["map_offset"], 16)
        map_addr = memory_helper.module_base + map_offset
        
        # 写入地图值
        result = memory_helper.write_float(map_addr, map_value)
        if not result:
            logger.warning(f"写入地图范围值失败: {map_value}")
        
    except Exception as e:
        logger.error(f"更新地图范围时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        # 出错时停止计时器
        global map_extend_timer
        if map_extend_timer:
            map_extend_timer.stop()

# 技能加速功能
def toggle_attack_speed(memory_helper, checked, speed_value=200):
    """切换技能加速状态"""
    global features_active, current_version, game_versions
    
    logger.info(f"尝试切换技能加速状态: checked={checked}, speed_value={speed_value}")
    logger.info(f"memory_helper状态: process_id={memory_helper.process_id}, module_base={memory_helper.module_base}")
    
    if not memory_helper.process_id or not memory_helper.module_base:
        logger.error("请先加载游戏进程")
        return False
    
    features_active["attack_speed"] = checked
    
    if checked:
        try:
            # 获取攻速地址
            if current_version not in game_versions:
                logger.error(f"未知游戏版本: {current_version}")
                return False
                
            logger.info(f"当前游戏版本: {current_version}")
            
            attack_speed_offset = int(game_versions[current_version]["attack_speed_data"], 16)
            attack_speed_addr = memory_helper.module_base + attack_speed_offset
            
            logger.info(f"攻速地址: 0x{attack_speed_addr:X}, 偏移量: 0x{attack_speed_offset:X}")
            
            # 构造攻速修改指令
            speed_bytes = speed_value.to_bytes(2, byteorder='little')
            patch_bytes = bytearray([0x66, 0xB8]) + speed_bytes + bytearray([0x90, 0x90, 0x90])
            
            logger.info(f"修改指令: {patch_bytes.hex()}")
            
            # 写入内存
            result = memory_helper.write_bytes(attack_speed_addr, patch_bytes)
            
            if result:
                logger.info(f"技能加速已启用，值: {speed_value}")
                return True
            else:
                features_active["attack_speed"] = False
                logger.error("无法修改内存，可能是权限不足")
                return False
                
        except Exception as e:
            features_active["attack_speed"] = False
            logger.error(f"技能加速启用失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    else:
        # 关闭技能加速，恢复原始值
        logger.info("技能加速已关闭")
        # 这里应该添加恢复原始指令的代码
        return True

# 移动加速功能
def toggle_move_speed(memory_helper, checked, speed_value=2.0):
    """切换移动加速状态"""
    global features_active, timers
    
    logger.info(f"尝试切换移动加速状态: checked={checked}, speed_value={speed_value}")
    logger.info(f"memory_helper状态: process_id={memory_helper.process_id}, module_base={memory_helper.module_base}")
    
    if not memory_helper.process_id or not memory_helper.module_base:
        logger.error("请先加载游戏进程")
        return False
    
    features_active["move_speed"] = checked
    
    if checked:
        try:
            # 创建定时器，持续更新移动速度
            if "move_speed" not in timers:
                timers["move_speed"] = QTimer()
                timers["move_speed"].timeout.connect(lambda: update_move_speed(memory_helper, speed_value))
            
            timers["move_speed"].start(100)  # 每100ms更新一次
            logger.info(f"移动加速已启用，值: {speed_value}")
            return True
            
        except Exception as e:
            features_active["move_speed"] = False
            logger.error(f"移动加速启用失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    else:
        # 关闭移动加速
        if "move_speed" in timers:
            timers["move_speed"].stop()
        
        logger.info("移动加速已关闭")
        return True

def update_move_speed(memory_helper, speed_value):
    """定时更新移动速度"""
    global features_active, current_version, game_versions, offsets
    
    if not memory_helper.process_id or not features_active["move_speed"]:
        return
        
    try:
        # 获取人物基址
        if current_version not in game_versions:
            logger.error(f"未知游戏版本: {current_version}")
            return
            
        base_offset = int(game_versions[current_version]["base_offset"], 16)
        level1_offset = int(game_versions[current_version]["level1_offset"], 16)
        level2_offset = int(game_versions[current_version]["level2_offset"], 16)
        level3_offset = int(game_versions[current_version]["level3_offset"], 16)
        character_offset = int(game_versions[current_version]["character_base"], 16)
        move_speed_offset = int(game_versions[current_version]["move_speed"], 16)
        
        logger.debug(f"基址偏移: 0x{base_offset:X}, 一级偏移: 0x{level1_offset:X}, 二级偏移: 0x{level2_offset:X}, 三级偏移: 0x{level3_offset:X}")
        logger.debug(f"角色偏移: 0x{character_offset:X}, 移速偏移: 0x{move_speed_offset:X}")
        
        # 读取多级指针
        base_addr = memory_helper.read_long(memory_helper.module_base + base_offset)
        if not base_addr:
            logger.warning("无法读取基址")
            return
            
        logger.debug(f"基址: 0x{base_addr:X}")
            
        level1_addr = memory_helper.read_long(base_addr + level1_offset)
        if not level1_addr:
            logger.warning("无法读取一级地址")
            return
            
        logger.debug(f"一级地址: 0x{level1_addr:X}")
            
        level2_addr = memory_helper.read_long(level1_addr + level2_offset)
        if not level2_addr:
            logger.warning("无法读取二级地址")
            return
            
        logger.debug(f"二级地址: 0x{level2_addr:X}")
            
        level3_addr = memory_helper.read_long(level2_addr + level3_offset)
        if not level3_addr:
            logger.warning("无法读取三级地址")
            return
            
        logger.debug(f"三级地址: 0x{level3_addr:X}")
            
        character_addr = memory_helper.read_long(level3_addr + character_offset)
        if not character_addr:
            logger.warning("无法读取角色地址")
            return
            
        logger.debug(f"角色地址: 0x{character_addr:X}")
            
        move_speed_addr = character_addr + move_speed_offset
        
        logger.debug(f"移速地址: 0x{move_speed_addr:X}")
        
        # 写入移动速度
        result = memory_helper.write_float(move_speed_addr, speed_value)
        if result:
            logger.debug(f"成功写入移动速度: {speed_value}")
        else:
            logger.warning(f"写入移动速度失败: {speed_value}")
        
    except Exception as e:
        logger.error(f"更新移动速度时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

# 攻击距离修改功能
def toggle_attack_range(memory_helper, checked, range_value=10.0):
    """切换攻击距离状态"""
    global features_active, timers
    
    logger.info(f"尝试切换攻击距离状态: checked={checked}, range_value={range_value}")
    logger.info(f"memory_helper状态: process_id={memory_helper.process_id}, module_base={memory_helper.module_base}")
    
    if not memory_helper.process_id or not memory_helper.module_base:
        logger.error("请先加载游戏进程")
        return False
    
    features_active["attack_range"] = checked
    
    if checked:
        try:
            # 创建定时器，持续更新攻击距离
            if "attack_range" not in timers:
                timers["attack_range"] = QTimer()
                timers["attack_range"].timeout.connect(lambda: update_attack_range(memory_helper, range_value))
            
            timers["attack_range"].start(100)  # 每100毫秒更新一次
            
            # 立即更新一次攻击距离
            update_attack_range(memory_helper, range_value)
            
            logger.info(f"攻击距离已增加: {range_value}")
            return True
            
        except Exception as e:
            features_active["attack_range"] = False
            logger.error(f"攻击距离修改启用失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    else:
        # 关闭攻击距离增加
        if "attack_range" in timers:
            timers["attack_range"].stop()
        
        logger.info("攻击距离修改已关闭")
        return True

def update_attack_range(memory_helper, range_value):
    """定时更新攻击距离"""
    global features_active, current_version, game_versions
    
    if not memory_helper.process_id or not features_active["attack_range"]:
        return
        
    try:
        # 获取攻击距离地址
        if current_version not in game_versions:
            logger.error(f"未知游戏版本: {current_version}")
            return
            
        attack_range_offset = int(game_versions[current_version]["attack_range"], 16)
        attack_range_addr = memory_helper.module_base + attack_range_offset
        
        logger.debug(f"攻击距离地址: 0x{attack_range_addr:X}, 偏移量: 0x{attack_range_offset:X}")
        
        # 写入攻击距离值
        result = memory_helper.write_float(attack_range_addr, range_value)
        
        if result:
            logger.debug(f"成功写入攻击距离: {range_value}")
        else:
            logger.warning("写入攻击距离值失败")
            features_active["attack_range"] = False
            if "attack_range" in timers:
                timers["attack_range"].stop()
        
    except Exception as e:
        logger.error(f"更新攻击距离时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        # 出错时停止计时器
        if "attack_range" in timers:
            timers["attack_range"].stop()
        features_active["attack_range"] = False

# 全局变量，用于跟踪自动战斗状态
auto_battle_active = False
battle_manager = None
memory_helper = MemoryHelper()  # 提前初始化memory_helper

# 初始化全局定时器变量
view_range_timer = None
map_extend_timer = None

# 热键回调函数
def hotkey_callback():
    global auto_battle_active, battle_manager
    if battle_manager:
        auto_battle_active = not auto_battle_active
        if auto_battle_active:
            battle_manager.start()
            print("自动战斗已启动")
        else:
            battle_manager.stop()
            print("自动战斗已停止")

# 注册全局热键 - 使用Windows API
def register_global_hotkey(window_handle):
    """注册全局热键 - 使用Windows API"""
    try:
        # 注册Home键为全局热键
        result = user32.RegisterHotKey(
            window_handle,  # 窗口句柄
            1,              # 热键ID
            0,              # 修饰键 (0表示无修饰键)
            VK_HOME         # Home键的虚拟键码
        )
        
        if result:
            logger.info("全局热键注册成功: Home键")
            return True
        else:
            error_code = ctypes.get_last_error()
            logger.error(f"全局热键注册失败，错误码: {error_code}")
            return False
    except Exception as e:
        logger.error(f"注册全局热键时发生异常: {str(e)}")
        return False

# 使用keyboard库注册热键（备用方法）
def register_keyboard_hotkey():
    """使用keyboard库注册热键（备用方法）"""
    try:
        keyboard.add_hotkey('home', hotkey_callback)
        logger.info("使用keyboard库注册热键成功: Home键")
        return True
    except Exception as e:
        logger.error(f"使用keyboard库注册热键失败: {str(e)}")
        return False

# 注册所有功能热键
def register_all_hotkeys():
    """注册所有功能热键"""
    try:
        # 注册Home键为技能加速开关
        keyboard.add_hotkey('home', lambda: toggle_hotkey_function(toggle_attack_speed))
        logger.info("全局热键注册成功: Home键 (技能加速)")
        
        # 注册F7键为移动加速开关
        keyboard.add_hotkey('f7', lambda: toggle_hotkey_function(toggle_move_speed))
        logger.info("全局热键注册成功: F7键 (移动加速)")
        
        # 注册Insert键为锁空
        keyboard.add_hotkey('insert', lambda: toggle_air_lock_hotkey(True))
        logger.info("全局热键注册成功: Insert键 (锁空)")
        
        # 注册Delete键为解除锁空
        keyboard.add_hotkey('delete', lambda: toggle_air_lock_hotkey(False))
        logger.info("全局热键注册成功: Delete键 (解除锁空)")
        
        # 注册Page Up键为上升
        keyboard.add_hotkey('page up', lambda: toggle_fly_hotkey("up"))
        logger.info("全局热键注册成功: Page Up键 (上升)")
        
        # 注册Page Down键为下降
        keyboard.add_hotkey('page down', lambda: toggle_fly_hotkey("down"))
        logger.info("全局热键注册成功: Page Down键 (下降)")
        
        # 确保空格键不会被监听
        keyboard.unhook_key('space')
        logger.info("已移除空格键监听")
        
        # 确保Tab键不会被监听
        keyboard.unhook_key('tab')
        logger.info("已移除Tab键监听")
        
        return True
    except Exception as e:
        logger.error(f"注册全局热键失败: {e}")
        return False

# 热键功能切换
def toggle_hotkey_function(function):
    """热键功能切换"""
    if memory_helper and memory_helper.process_handle:
        function_name = function.__name__.replace("toggle_", "")
        current_state = features_active.get(function_name, False)
        new_state = not current_state
        
        # 调用对应的功能切换函数
        if function == toggle_attack_speed:
            function(memory_helper, new_state, 200)  # 默认速度值200
        elif function == toggle_move_speed:
            function(memory_helper, new_state, 2.0)  # 默认速度值2.0
        else:
            function(memory_helper, new_state)
            
        logger.info(f"通过热键切换{function_name}状态: {new_state}")
    else:
        logger.warning("热键触发失败: 未连接到游戏进程")

# 锁空热键功能
def toggle_air_lock_hotkey(lock_state):
    """锁空热键功能"""
    if memory_helper and memory_helper.process_handle and features_active.get("air_lock", False):
        if lock_state:
            # 锁定当前高度
            toggle_air_lock(memory_helper, True, lock=True)
            logger.info("通过热键锁定当前高度")
        else:
            # 解除锁定
            toggle_air_lock(memory_helper, True, lock=False)
            logger.info("通过热键解除高度锁定")
    else:
        logger.warning("锁空热键触发失败: 未连接到游戏进程或锁空功能未启用")

# 飞天热键功能
def toggle_fly_hotkey(direction):
    """飞天热键功能"""
    if memory_helper and memory_helper.process_handle and features_active.get("fly", False):
        # 默认高度变化值
        height_value = 5.0
        
        # 调用飞天功能
        toggle_fly(memory_helper, True, height_value, direction)
        logger.info(f"通过热键{direction}升/降: {height_value}米")
    else:
        logger.warning("飞天热键触发失败: 未连接到游戏进程或飞天功能未启用")

# Home键按下时切换窗口显示/隐藏状态
def toggle_window_visibility(window):
    """Home键按下时切换窗口显示/隐藏状态"""
    if window.isVisible():
        window.hide()
    else:
        window.show()
        window.setWindowState(window.windowState() & ~Qt.WindowState.WindowMinimized | Qt.WindowState.WindowActive)
        window.activateWindow()

# 更新游戏偏移量
def update_offsets(version):
    """更新游戏偏移量"""
    global offsets, game_versions, current_version
    
    if version not in game_versions:
        logger.error(f"未知游戏版本: {version}")
        return False
    
    current_version = version
    
    # 更新偏移量
    for key, value in game_versions[version].items():
        if key in offsets:
            offsets[key] = int(value, 16)
    
    logger.info(f"已更新偏移量: {offsets}")
    return True

def main():
    global battle_manager, memory_helper, current_version
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('aion_bot.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger('main')
    
    app = QApplication(sys.argv)
    
    # 设置默认游戏版本
    current_version = "4.5核心8880版[64位]"
    update_offsets(current_version)
    logger.info(f"设置游戏版本: {current_version}")
    
    try:
        # 创建组件
        logger.info("初始化系统组件...")
        memory = MemoryReader()
        
        # 尝试使用修复版输入模拟器
        try:
            from input_simulator_fixed import InputSimulatorFixed
            input_sim = InputSimulatorFixed()
            logger.info("✓ 使用修复版输入模拟器")
            
            # 尝试查找游戏窗口
            if input_sim.find_game_window():
                logger.info("✓ 找到游戏窗口，将使用窗口特定按键发送")
            else:
                logger.warning("⚠ 未找到游戏窗口，将使用全局按键发送")
        except ImportError:
            # 如果修复版不可用，使用原版
            from input_simulator import InputSimulator
            input_sim = InputSimulator()
            logger.warning("⚠ 使用原版输入模拟器")
        
        monster_selector = MonsterSelector(memory)
        battle_manager = BattleManager(memory, input_sim, monster_selector)
        
        # 设置全局memory_helper变量（用于热键功能）
        global memory_helper
        memory_helper = memory
        
        # 创建增强的自动战斗系统
        logger.info("创建自动战斗系统...")
        auto_combat = AutoCombatSystem(memory, input_sim, monster_selector)
        
        # 启用调试模式（当没有真实内存读取时）
        auto_combat.enable_debug_mode(True)
        logger.info("自动战斗系统调试模式已启用")
        
        # 创建主窗口
        logger.info("创建主界面...")
        window = MainWindow(memory, battle_manager, auto_combat)
        window.show()
        
        # 设置游戏动作日志回调
        battle_manager.set_log_callback(window.add_game_log)
        
        # 设置更新定时器 (每100ms更新一次)
        update_timer = QTimer()
        update_timer.timeout.connect(lambda: battle_manager.update() if battle_manager.is_active else None)
        update_timer.start(100)
        
        # 注册全局热键 - 首先尝试使用Windows API
        logger.info("注册全局热键...")
        if not register_global_hotkey(int(window.winId())):
            # 如果Windows API注册失败，使用keyboard库作为备用
            register_keyboard_hotkey()
        
        # 注册所有功能热键
        register_all_hotkeys()
        
        logger.info("系统启动完成!")
        logger.info("=" * 50)
        logger.info("使用说明:")
        logger.info("1. 点击'连接游戏'按钮连接到游戏进程")
        logger.info("2. 在'战斗配置'选项卡中调整参数")
        logger.info("3. 点击'开始副本辅助'启动自动战斗")
        logger.info("4. 使用F8键可以快速开启/关闭自动战斗")
        logger.info("=" * 50)
        
        # 启动应用
        sys.exit(app.exec())
        
    except Exception as e:
        logger.error(f"系统启动失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 显示错误对话框
        if 'app' in locals():
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Icon.Critical)
            msg.setWindowTitle("启动错误")
            msg.setText(f"系统启动失败:\n{str(e)}")
            msg.exec()
        
        sys.exit(1)

if __name__ == "__main__":
    main()