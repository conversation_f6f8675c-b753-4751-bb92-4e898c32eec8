#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
专注击杀功能测试脚本 - 测试专注击杀模式
"""

import sys
import os
import time
import logging

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_focus_kill_mode():
    """测试专注击杀模式"""
    print("=" * 60)
    print("专注击杀模式测试")
    print("=" * 60)
    
    try:
        from battle_logic import BattleManager
        from memory_reader import MemoryReader
        from input_simulator import InputSimulator
        from monster_select import MonsterSelector
        from tactical_ai import Target
        
        # 创建组件
        memory = MemoryReader()
        input_sim = InputSimulator()
        monster_selector = MonsterSelector(memory)
        battle_manager = BattleManager(memory, input_sim, monster_selector)
        
        print("✓ 战斗管理器初始化成功")
        print(f"  - 专注击杀模式: {battle_manager.focus_kill_mode}")
        print(f"  - 最大专注时间: {battle_manager.max_focus_duration}秒")
        print(f"  - 无伤害超时: {battle_manager.no_damage_timeout}秒")
        print(f"  - 击杀确认时间: {battle_manager.kill_confirmation_time}秒")
        
        # 启用AI功能
        battle_manager.toggle_ai_mode(True)
        battle_manager.toggle_auto_attack(True)
        battle_manager.is_active = True
        
        print("\n✓ AI功能已启用")
        
        # 创建测试目标
        test_targets = [
            Target(
                id=1001,
                name="普通怪物1",
                level=48,
                hp_percent=0.8,
                distance=12.0,
                position=(900, 500),
                last_seen=time.time(),
                threat_level=1
            ),
            Target(
                id=1002,
                name="精英怪物2",
                level=50,
                hp_percent=0.9,
                distance=15.0,
                position=(1000, 550),
                last_seen=time.time(),
                threat_level=3,
                is_elite=True
            ),
            Target(
                id=1003,
                name="普通怪物3",
                level=49,
                hp_percent=0.7,
                distance=18.0,
                position=(1100, 600),
                last_seen=time.time(),
                threat_level=2
            )
        ]
        
        print(f"\n创建了 {len(test_targets)} 个测试目标:")
        for target in test_targets:
            print(f"  - {target.name}: 等级{target.level}, 血量{target.hp_percent*100:.0f}%, 距离{target.distance:.1f}米")
        
        # 测试专注击杀逻辑
        print("\n开始专注击杀测试:")
        
        # 模拟选择第一个目标
        print(f"\n--- 选择专注目标: {test_targets[0].name} ---")
        result = battle_manager._set_focus_target(test_targets[0])
        print(f"设置专注目标: {'成功' if result else '失败'}")
        
        if result:
            print(f"当前专注目标: {battle_manager.current_focus_target.name}")
            print(f"专注开始时间: {battle_manager.focus_start_time}")
            
            # 模拟专注攻击过程
            print("\n模拟专注攻击过程:")
            for i in range(10):
                print(f"\n--- 攻击轮次 {i+1} ---")
                
                # 检查专注目标有效性
                current_time = time.time()
                is_valid = battle_manager._is_focus_target_valid(current_time)
                print(f"目标有效性: {'有效' if is_valid else '无效'}")
                
                if not is_valid:
                    print("目标无效，应该切换目标")
                    break
                
                # 检查是否击杀
                is_killed = battle_manager._check_target_killed(current_time)
                print(f"击杀检查: {'已击杀' if is_killed else '未击杀'}")
                
                if is_killed:
                    print("目标已击杀，应该寻找新目标")
                    battle_manager._clear_focus_target()
                    break
                
                # 继续攻击
                attack_result = battle_manager._continue_focus_attack()
                print(f"继续攻击: {'成功' if attack_result else '失败'}")
                
                # 模拟伤害检测
                if i % 3 == 0:  # 每3轮模拟一次命中
                    battle_manager.target_hit_confirmed = True
                    battle_manager.last_damage_time = current_time
                    print("模拟命中，更新伤害时间")
                
                time.sleep(0.2)
        
        return True
        
    except Exception as e:
        print(f"✗ 专注击杀模式测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_target_switching():
    """测试目标切换逻辑"""
    print("\n" + "=" * 60)
    print("目标切换逻辑测试")
    print("=" * 60)
    
    try:
        from battle_logic import BattleManager
        from memory_reader import MemoryReader
        from input_simulator import InputSimulator
        from monster_select import MonsterSelector
        from tactical_ai import Target
        
        # 创建组件
        memory = MemoryReader()
        input_sim = InputSimulator()
        monster_selector = MonsterSelector(memory)
        battle_manager = BattleManager(memory, input_sim, monster_selector)
        
        # 启用功能
        battle_manager.toggle_ai_mode(True)
        battle_manager.focus_kill_mode = True
        battle_manager.is_active = True
        
        print("✓ 战斗管理器初始化成功")
        
        # 测试目标切换场景
        scenarios = [
            {
                'name': '正常击杀切换',
                'description': '目标被击杀后自动寻找新目标',
                'simulate_kill': True,
                'simulate_timeout': False
            },
            {
                'name': '超时切换',
                'description': '专注时间超时后切换目标',
                'simulate_kill': False,
                'simulate_timeout': True
            },
            {
                'name': '无伤害切换',
                'description': '长时间无伤害后切换目标',
                'simulate_kill': False,
                'simulate_timeout': False
            }
        ]
        
        for scenario in scenarios:
            print(f"\n--- {scenario['name']} ---")
            print(f"场景描述: {scenario['description']}")
            
            # 创建测试目标
            test_target = Target(
                id=int(time.time()),
                name=f"测试目标_{scenario['name']}",
                level=50,
                hp_percent=0.8,
                distance=15.0,
                position=(960, 540),
                last_seen=time.time(),
                threat_level=2
            )
            
            # 设置专注目标
            battle_manager._set_focus_target(test_target)
            print(f"设置专注目标: {test_target.name}")
            
            # 模拟不同场景
            if scenario['simulate_kill']:
                # 模拟击杀
                battle_manager.target_hit_confirmed = True
                battle_manager.target_hit_time = time.time() - 5.0  # 5秒前命中
                print("模拟目标被击杀")
                
            elif scenario['simulate_timeout']:
                # 模拟超时
                battle_manager.focus_start_time = time.time() - 20.0  # 20秒前开始专注
                print("模拟专注时间超时")
                
            else:
                # 模拟无伤害
                battle_manager.last_damage_time = time.time() - 10.0  # 10秒前最后伤害
                print("模拟长时间无伤害")
            
            # 执行专注击杀逻辑
            result = battle_manager._focus_kill_logic(time.time())
            print(f"专注击杀逻辑结果: {'继续' if result else '切换'}")
            
            # 检查是否正确切换
            if battle_manager.current_focus_target is None:
                print("✓ 目标已正确切换")
            else:
                print(f"○ 仍在专注: {battle_manager.current_focus_target.name}")
            
            time.sleep(0.5)
        
        return True
        
    except Exception as e:
        print(f"✗ 目标切换测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_focus_kill_features():
    """显示专注击杀功能特性"""
    print("\n" + "=" * 60)
    print("专注击杀功能特性")
    print("=" * 60)
    
    features = [
        "🎯 专注击杀 - 选定目标后专注攻击直到死亡",
        "🔒 目标锁定 - 避免频繁切换目标",
        "⏱️ 智能超时 - 防止卡在无效目标上",
        "💥 击杀确认 - 多种方式确认目标死亡",
        "🔄 自动切换 - 目标死亡后自动寻找新目标",
        "📊 伤害监控 - 实时监控伤害输出",
        "🛡️ 异常处理 - 自动处理各种异常情况",
        "⚙️ 参数可调 - 专注时间和超时时间可配置",
        "🎮 无缝集成 - 与现有AI系统完美结合",
        "📈 效率提升 - 显著提高副本清理效率"
    ]
    
    print("专注击杀模式特性:")
    for feature in features:
        print(f"  {feature}")
    
    print("\n工作流程:")
    print("1. 🔍 扫描周围怪物")
    print("2. 🎯 AI选择最佳目标")
    print("3. 🔒 锁定目标开始专注攻击")
    print("4. ⚔️ 持续攻击直到目标死亡")
    print("5. ✅ 确认击杀完成")
    print("6. 🔄 自动寻找下一个目标")
    print("7. 🔁 重复流程")
    
    print("\n优势对比:")
    print("┌─────────────────┬──────────────┬──────────────┐")
    print("│ 功能            │ 频繁切换模式 │ 专注击杀模式 │")
    print("├─────────────────┼──────────────┼──────────────┤")
    print("│ 目标选择        │ 频繁变化     │ 专注固定     │")
    print("│ 击杀效率        │ 较低         │ 很高         │")
    print("│ 资源浪费        │ 较多         │ 很少         │")
    print("│ 战斗稳定性      │ 一般         │ 很好         │")
    print("│ 副本清理速度    │ 中等         │ 很快         │")
    print("└─────────────────┴──────────────┴──────────────┘")

def main():
    """主测试函数"""
    print("专注击杀功能测试")
    print("=" * 60)
    
    # 执行测试
    test_results = []
    
    test_results.append(("专注击杀模式", test_focus_kill_mode()))
    test_results.append(("目标切换逻辑", test_target_switching()))
    
    # 显示功能特性
    show_focus_kill_features()
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 专注击杀功能实现成功！")
        print("\n现在您的副本辅助具有:")
        print("🎯 专注击杀模式 - 一个目标一个目标地清理")
        print("🔒 目标锁定 - 不会频繁切换目标")
        print("⚔️ 高效击杀 - 专注攻击直到目标死亡")
        print("🔄 智能切换 - 目标死亡后自动寻找新目标")
        print("📊 实时监控 - 监控攻击效果和目标状态")
        
        print("\n使用方法:")
        print("1. 重新启动程序")
        print("2. 在'AI增强'选项卡中启用'专注击杀模式'")
        print("3. 调整专注时间和超时参数")
        print("4. 开始副本辅助")
        print("5. 观察AI专注击杀每个目标")
    else:
        print("⚠ 部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
