#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
直接战斗测试 - 最简单直接的方法
"""

import time
import ctypes
import win32api
import win32con
import win32gui
import psutil
import logging

# 设置简单日志
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger()

class DirectCombatTest:
    """直接战斗测试 - 最简单的实现"""
    
    def __init__(self):
        self.running = False
        
        # 虚拟键码
        self.keys = {
            '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34, '5': 0x35,
            '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39, '0': 0x30,
            'tab': 0x09, 'alt': 0x12, 'space': 0x20
        }
        
        # 找游戏窗口
        self.game_window = self.find_game_window()
        
    def find_game_window(self):
        """查找游戏窗口"""
        def callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd).lower()
                if 'aion' in title or '永恒之塔' in title:
                    windows.append(hwnd)
            return True
        
        windows = []
        win32gui.EnumWindows(callback, windows)
        
        if windows:
            hwnd = windows[0]
            title = win32gui.GetWindowText(hwnd)
            logger.info(f"找到游戏窗口: {title}")
            return hwnd
        else:
            logger.warning("未找到游戏窗口")
            return None
    
    def send_key_direct(self, key):
        """直接发送按键 - 多种方法同时使用"""
        vk = self.keys.get(key)
        if not vk:
            return False
        
        success_count = 0
        
        # 方法1: 全局按键
        try:
            win32api.keybd_event(vk, 0, 0, 0)
            time.sleep(0.02)
            win32api.keybd_event(vk, 0, win32con.KEYEVENTF_KEYUP, 0)
            success_count += 1
        except:
            pass
        
        # 方法2: 发送到游戏窗口
        if self.game_window:
            try:
                win32gui.PostMessage(self.game_window, win32con.WM_KEYDOWN, vk, 0)
                time.sleep(0.02)
                win32gui.PostMessage(self.game_window, win32con.WM_KEYUP, vk, 0)
                success_count += 1
            except:
                pass
        
        # 方法3: SendInput
        try:
            ctypes.windll.user32.keybd_event(vk, 0, 0, 0)
            time.sleep(0.02)
            ctypes.windll.user32.keybd_event(vk, 0, 2, 0)
            success_count += 1
        except:
            pass
        
        return success_count > 0
    
    def send_alt_combo(self, key):
        """发送Alt组合键"""
        alt_vk = self.keys['alt']
        key_vk = self.keys.get(key)
        
        if not key_vk:
            return False
        
        try:
            # 按下Alt
            win32api.keybd_event(alt_vk, 0, 0, 0)
            time.sleep(0.01)
            
            # 按下目标键
            win32api.keybd_event(key_vk, 0, 0, 0)
            time.sleep(0.02)
            
            # 释放目标键
            win32api.keybd_event(key_vk, 0, win32con.KEYEVENTF_KEYUP, 0)
            time.sleep(0.01)
            
            # 释放Alt
            win32api.keybd_event(alt_vk, 0, win32con.KEYEVENTF_KEYUP, 0)
            
            return True
        except Exception as e:
            logger.error(f"Alt组合键失败: {e}")
            return False
    
    def test_single_key(self, key):
        """测试单个按键"""
        logger.info(f"测试按键: {key}")
        
        if key.startswith('alt+'):
            target_key = key.split('+')[1]
            success = self.send_alt_combo(target_key)
        else:
            success = self.send_key_direct(key)
        
        if success:
            logger.info(f"  ✓ {key} 发送成功")
        else:
            logger.error(f"  ✗ {key} 发送失败")
        
        return success
    
    def manual_test(self):
        """手动测试模式"""
        logger.info("=" * 50)
        logger.info("手动测试模式")
        logger.info("=" * 50)
        
        test_keys = ['1', '2', '3', 'alt+1', 'alt+2', 'alt+3', 'tab']
        
        for key in test_keys:
            input(f"按回车测试 {key} (观察游戏反应)...")
            self.test_single_key(key)
            time.sleep(1)
    
    def auto_combat_simple(self):
        """简单自动战斗"""
        logger.info("=" * 50)
        logger.info("简单自动战斗开始")
        logger.info("按Ctrl+C停止")
        logger.info("=" * 50)
        
        skills = ['1', '2', '3', '4', '5', '6', '7', '8', '9', 
                 'alt+1', 'alt+2', 'alt+3', 'alt+4', 'alt+5']
        
        skill_index = 0
        target_counter = 0
        
        self.running = True
        
        try:
            while self.running:
                # 每10个技能选择一次目标
                if target_counter % 10 == 0:
                    logger.info("🎯 选择目标...")
                    self.send_key_direct('tab')
                    time.sleep(0.5)
                
                # 释放技能
                skill = skills[skill_index % len(skills)]
                logger.info(f"⚔️ 释放技能: {skill}")
                
                if skill.startswith('alt+'):
                    target_key = skill.split('+')[1]
                    self.send_alt_combo(target_key)
                else:
                    self.send_key_direct(skill)
                
                skill_index += 1
                target_counter += 1
                
                time.sleep(0.3)  # 技能间隔
                
        except KeyboardInterrupt:
            logger.info("\\n战斗停止")
            self.running = False
    
    def focus_kill_test(self):
        """专注击杀测试"""
        logger.info("=" * 50)
        logger.info("专注击杀测试")
        logger.info("=" * 50)
        
        skills = ['1', '2', '3', '4', '5']  # 只用基础技能测试
        skill_index = 0
        
        self.running = True
        
        try:
            # 先选择一个目标
            logger.info("🎯 选择初始目标...")
            self.send_key_direct('tab')
            time.sleep(1)
            
            attack_count = 0
            while self.running:
                # 释放技能
                skill = skills[skill_index % len(skills)]
                logger.info(f"⚔️ 攻击 #{attack_count+1}: {skill}")
                
                self.send_key_direct(skill)
                
                skill_index += 1
                attack_count += 1
                
                # 每20次攻击后选择新目标（假设目标已死）
                if attack_count % 20 == 0:
                    logger.info("✅ 假设目标已死，选择新目标...")
                    self.send_key_direct('tab')
                    time.sleep(0.5)
                    attack_count = 0
                
                time.sleep(0.2)  # 快速攻击
                
        except KeyboardInterrupt:
            logger.info("\\n专注击杀测试停止")
            self.running = False

def check_admin():
    """检查管理员权限"""
    try:
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if is_admin:
            logger.info("✓ 程序以管理员权限运行")
        else:
            logger.warning("⚠ 程序未以管理员权限运行")
            logger.warning("建议: 右键程序 -> '以管理员身份运行'")
        return is_admin
    except:
        return False

def check_game():
    """检查游戏进程"""
    for proc in psutil.process_iter(['name']):
        try:
            if 'aion' in proc.info['name'].lower():
                logger.info(f"✓ 找到游戏进程: {proc.info['name']}")
                return True
        except:
            continue
    
    logger.warning("⚠ 未找到游戏进程")
    logger.warning("请确保永恒之塔游戏正在运行")
    return False

def main():
    print("🎮 永恒之塔直接战斗测试")
    print("=" * 50)
    
    # 检查环境
    admin_ok = check_admin()
    game_ok = check_game()
    
    if not admin_ok:
        print("\\n❌ 需要管理员权限才能正常工作")
        input("按回车退出...")
        return
    
    if not game_ok:
        print("\\n❌ 请先启动游戏")
        input("按回车退出...")
        return
    
    # 创建测试器
    tester = DirectCombatTest()
    
    while True:
        print("\\n🎯 选择测试模式:")
        print("1. 手动测试按键 (逐个测试)")
        print("2. 简单自动战斗 (循环释放技能)")
        print("3. 专注击杀测试 (专注单个目标)")
        print("4. 退出")
        
        try:
            choice = input("\\n请选择 (1-4): ").strip()
            
            if choice == '1':
                tester.manual_test()
            elif choice == '2':
                tester.auto_combat_simple()
            elif choice == '3':
                tester.focus_kill_test()
            elif choice == '4':
                print("👋 退出程序")
                break
            else:
                print("❌ 无效选择")
                
        except KeyboardInterrupt:
            print("\\n👋 退出程序")
            break
        except Exception as e:
            logger.error(f"错误: {e}")

if __name__ == "__main__":
    main()