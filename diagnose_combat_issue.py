#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
诊断自动战斗问题的完整工具
"""

import sys
import os
import time
import logging
import ctypes
import psutil
import win32gui
import win32api
import win32con

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_admin_rights():
    """检查管理员权限"""
    try:
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        print(f"管理员权限: {'✓ 是' if is_admin else '✗ 否'}")
        if not is_admin:
            print("  ⚠ 建议: 右键程序 -> '以管理员身份运行'")
        return is_admin
    except:
        print("管理员权限: ❌ 检查失败")
        return False

def check_game_process():
    """检查游戏进程"""
    print("\\n🔍 检查游戏进程...")
    
    aion_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'exe']):
        try:
            name = proc.info['name'].lower()
            if 'aion' in name or 'bin32' in name or 'bin64' in name:
                aion_processes.append(proc.info)
        except:
            continue
    
    if aion_processes:
        print("✓ 找到游戏进程:")
        for proc in aion_processes:
            print(f"  - {proc['name']} (PID: {proc['pid']})")
        return True
    else:
        print("✗ 未找到游戏进程")
        print("  ⚠ 请确保永恒之塔游戏正在运行")
        return False

def check_game_window():
    """检查游戏窗口"""
    print("\\n🪟 检查游戏窗口...")
    
    def enum_callback(hwnd, windows):
        if win32gui.IsWindowVisible(hwnd):
            title = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            
            if title and (any(keyword in title.lower() for keyword in ['aion', '永恒之塔']) or 
                         'aion' in class_name.lower()):
                try:
                    rect = win32gui.GetWindowRect(hwnd)
                    _, pid = win32gui.GetWindowThreadProcessId(hwnd)
                    windows.append({
                        'handle': hwnd,
                        'title': title,
                        'class': class_name,
                        'rect': rect,
                        'pid': pid
                    })
                except:
                    pass
        return True
    
    windows = []
    win32gui.EnumWindows(enum_callback, windows)
    
    if windows:
        print("✓ 找到游戏窗口:")
        for win in windows:
            print(f"  - 标题: {win['title']}")
            print(f"    类名: {win['class']}")
            print(f"    句柄: {win['handle']}")
            print(f"    进程ID: {win['pid']}")
            print(f"    位置: {win['rect']}")
        return windows[0]  # 返回第一个窗口
    else:
        print("✗ 未找到游戏窗口")
        print("  ⚠ 请确保游戏在窗口模式下运行")
        return None

def test_basic_input():
    """测试基本输入功能"""
    print("\\n⌨️ 测试基本输入功能...")
    
    # 测试Win32 API
    try:
        print("测试Win32 keybd_event...")
        print("将在3秒后发送数字键1，请切换到记事本观察")
        
        for i in range(3, 0, -1):
            print(f"  {i}...")
            time.sleep(1)
        
        # 发送数字键1
        win32api.keybd_event(0x31, 0, 0, 0)  # 按下
        time.sleep(0.05)
        win32api.keybd_event(0x31, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放
        
        print("✓ Win32 API测试完成")
        return True
        
    except Exception as e:
        print(f"✗ Win32 API测试失败: {e}")
        return False

def test_game_input(game_window):
    """测试游戏输入"""
    if not game_window:
        print("\\n❌ 无法测试游戏输入 - 未找到游戏窗口")
        return False
    
    print("\\n🎮 测试游戏输入...")
    print("将向游戏窗口发送按键，请观察游戏中的反应")
    
    try:
        hwnd = game_window['handle']
        
        # 确保窗口在前台
        win32gui.SetForegroundWindow(hwnd)
        time.sleep(0.5)
        
        # 测试发送数字键2到游戏窗口
        print("发送数字键2到游戏窗口...")
        
        vk_code = 0x32  # 数字键2
        scan_code = win32api.MapVirtualKey(vk_code, 0)
        
        # WM_KEYDOWN
        lParam_down = (scan_code << 16) | 1
        result1 = win32gui.PostMessage(hwnd, win32con.WM_KEYDOWN, vk_code, lParam_down)
        
        time.sleep(0.05)
        
        # WM_KEYUP
        lParam_up = (scan_code << 16) | 0xC0000001
        result2 = win32gui.PostMessage(hwnd, win32con.WM_KEYUP, vk_code, lParam_up)
        
        if result1 and result2:
            print("✓ 游戏窗口按键发送成功")
            return True
        else:
            print("✗ 游戏窗口按键发送失败")
            return False
            
    except Exception as e:
        print(f"✗ 游戏输入测试失败: {e}")
        return False

def test_working_input_simulator():
    """测试工作版输入模拟器"""
    print("\\n🛠️ 测试工作版输入模拟器...")
    
    try:
        from working_input_simulator import WorkingInputSimulator
        
        input_sim = WorkingInputSimulator()
        print("✓ 工作版输入模拟器创建成功")
        
        # 测试按键发送
        print("测试按键发送功能...")
        success = input_sim.test_input()
        
        if success:
            print("✓ 工作版输入模拟器测试通过")
        else:
            print("✗ 工作版输入模拟器测试失败")
        
        return success
        
    except ImportError:
        print("✗ 工作版输入模拟器不存在")
        return False
    except Exception as e:
        print(f"✗ 工作版输入模拟器测试失败: {e}")
        return False

def check_dependencies():
    """检查依赖库"""
    print("\\n📦 检查依赖库...")
    
    required_libs = [
        'win32api', 'win32con', 'win32gui', 'win32process',
        'psutil', 'ctypes', 'logging', 'threading', 'time'
    ]
    
    missing_libs = []
    
    for lib in required_libs:
        try:
            __import__(lib)
            print(f"✓ {lib}")
        except ImportError:
            print(f"✗ {lib}")
            missing_libs.append(lib)
    
    if missing_libs:
        print(f"\\n❌ 缺少依赖库: {', '.join(missing_libs)}")
        print("安装命令: pip install pywin32 psutil")
        return False
    else:
        print("\\n✓ 所有依赖库都可用")
        return True

def provide_solutions(issues):
    """提供解决方案"""
    print("\\n" + "=" * 60)
    print("💡 解决方案")
    print("=" * 60)
    
    if 'no_admin' in issues:
        print("🔧 管理员权限问题:")
        print("  1. 右键点击程序")
        print("  2. 选择'以管理员身份运行'")
        print("  3. 重新启动程序")
        print()
    
    if 'no_game_process' in issues:
        print("🎮 游戏进程问题:")
        print("  1. 启动永恒之塔游戏")
        print("  2. 确保游戏完全加载")
        print("  3. 重新运行诊断")
        print()
    
    if 'no_game_window' in issues:
        print("🪟 游戏窗口问题:")
        print("  1. 将游戏设置为窗口模式")
        print("  2. 确保游戏窗口可见")
        print("  3. 不要最小化游戏窗口")
        print()
    
    if 'input_failed' in issues:
        print("⌨️ 输入问题:")
        print("  1. 检查游戏是否有反外挂保护")
        print("  2. 尝试关闭杀毒软件")
        print("  3. 确保游戏窗口有焦点")
        print("  4. 检查键盘布局设置")
        print()
    
    if 'missing_deps' in issues:
        print("📦 依赖库问题:")
        print("  1. 运行: pip install pywin32 psutil")
        print("  2. 重启命令提示符")
        print("  3. 重新运行程序")
        print()

def main():
    """主诊断函数"""
    print("🔍 永恒之塔自动战斗问题诊断工具")
    print("=" * 60)
    
    issues = []
    
    # 1. 检查管理员权限
    if not check_admin_rights():
        issues.append('no_admin')
    
    # 2. 检查依赖库
    if not check_dependencies():
        issues.append('missing_deps')
    
    # 3. 检查游戏进程
    if not check_game_process():
        issues.append('no_game_process')
    
    # 4. 检查游戏窗口
    game_window = check_game_window()
    if not game_window:
        issues.append('no_game_window')
    
    # 5. 测试基本输入
    if not test_basic_input():
        issues.append('input_failed')
    
    # 6. 测试游戏输入
    if game_window and not test_game_input(game_window):
        issues.append('game_input_failed')
    
    # 7. 测试工作版输入模拟器
    if not test_working_input_simulator():
        issues.append('working_sim_failed')
    
    # 8. 提供解决方案
    if issues:
        provide_solutions(issues)
    else:
        print("\\n🎉 所有检查都通过了！")
        print("如果自动战斗仍然不工作，可能是游戏特定的问题")
    
    print("\\n" + "=" * 60)
    print("诊断完成")
    print("=" * 60)
    
    return len(issues) == 0

if __name__ == "__main__":
    try:
        success = main()
        input("\\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\\n用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\\n诊断工具错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)