#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
强制UI刷新和按键注入 - 最激进的方法
"""

import time
import ctypes
import ctypes.wintypes
import win32api
import win32con
import win32gui
import win32process
import psutil
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger()

# Windows API 常量
PROCESS_ALL_ACCESS = 0x1F0FFF
MEM_COMMIT = 0x1000
MEM_RESERVE = 0x2000
PAGE_READWRITE = 0x04

class ForceInputInjector:
    """强制输入注入器"""
    
    def __init__(self):
        self.game_hwnd = None
        self.game_pid = None
        self.process_handle = None
        
        # 查找游戏
        self.find_game()
        
        # 虚拟键码
        self.vk_codes = {
            '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34, '5': 0x35,
            '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39, '0': 0x30,
            'tab': 0x09, 'alt': 0x12, 'space': 0x20, 'f1': 0x70,
            'f2': 0x71, 'f3': 0x72, 'f4': 0x73, 'f5': 0x74
        }
    
    def find_game(self):
        """查找游戏进程和窗口"""
        # 查找进程
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'aion' in proc.info['name'].lower():
                    self.game_pid = proc.info['pid']
                    logger.info(f"找到游戏进程: {proc.info['name']} (PID: {self.game_pid})")
                    break
            except:
                continue
        
        if not self.game_pid:
            logger.error("未找到游戏进程")
            return False
        
        # 查找窗口
        def enum_callback(hwnd, windows):
            try:
                _, pid = win32gui.GetWindowThreadProcessId(hwnd)
                if pid == self.game_pid and win32gui.IsWindowVisible(hwnd):
                    title = win32gui.GetWindowText(hwnd)
                    if title:  # 有标题的窗口
                        windows.append((hwnd, title))
            except:
                pass
            return True
        
        windows = []
        win32gui.EnumWindows(enum_callback, windows)
        
        if windows:
            self.game_hwnd, title = windows[0]
            logger.info(f"找到游戏窗口: {title}")
            
            # 打开进程句柄
            try:
                self.process_handle = win32api.OpenProcess(PROCESS_ALL_ACCESS, False, self.game_pid)
                logger.info("获得进程访问权限")
            except Exception as e:
                logger.error(f"无法打开进程: {e}")
            
            return True
        else:
            logger.error("未找到游戏窗口")
            return False
    
    def force_window_focus(self):
        """强制窗口获得焦点"""
        if not self.game_hwnd:
            return False
        
        try:
            # 多种方法强制获得焦点
            win32gui.ShowWindow(self.game_hwnd, win32con.SW_RESTORE)
            win32gui.SetForegroundWindow(self.game_hwnd)
            win32gui.BringWindowToTop(self.game_hwnd)
            win32gui.SetActiveWindow(self.game_hwnd)
            
            # 发送激活消息
            win32gui.PostMessage(self.game_hwnd, win32con.WM_ACTIVATE, win32con.WA_ACTIVE, 0)
            
            logger.info("强制窗口获得焦点")
            return True
        except Exception as e:
            logger.error(f"强制焦点失败: {e}")
            return False
    
    def inject_key_hardcore(self, key):
        """硬核按键注入"""
        vk = self.vk_codes.get(key)
        if not vk:
            return False
        
        success_methods = 0
        
        # 确保窗口焦点
        self.force_window_focus()
        time.sleep(0.01)
        
        # 方法1: 直接内存注入（如果有权限）
        try:
            if self.process_handle:
                # 这里可以添加更底层的内存操作
                pass
        except:
            pass
        
        # 方法2: 多重消息发送
        try:
            if self.game_hwnd:
                # WM_KEYDOWN
                win32gui.SendMessage(self.game_hwnd, win32con.WM_KEYDOWN, vk, 0)
                win32gui.PostMessage(self.game_hwnd, win32con.WM_KEYDOWN, vk, 0)
                
                time.sleep(0.02)
                
                # WM_KEYUP
                win32gui.SendMessage(self.game_hwnd, win32con.WM_KEYUP, vk, 0)
                win32gui.PostMessage(self.game_hwnd, win32con.WM_KEYUP, vk, 0)
                
                success_methods += 1
        except:
            pass
        
        # 方法3: 全局按键事件
        try:
            win32api.keybd_event(vk, 0, 0, 0)
            time.sleep(0.02)
            win32api.keybd_event(vk, 0, win32con.KEYEVENTF_KEYUP, 0)
            success_methods += 1
        except:
            pass
        
        # 方法4: SendInput API
        try:
            # 定义输入结构
            class KEYBDINPUT(ctypes.Structure):
                _fields_ = [
                    ("wVk", ctypes.wintypes.WORD),
                    ("wScan", ctypes.wintypes.WORD),
                    ("dwFlags", ctypes.wintypes.DWORD),
                    ("time", ctypes.wintypes.DWORD),
                    ("dwExtraInfo", ctypes.POINTER(ctypes.wintypes.ULONG))
                ]
            
            class INPUT(ctypes.Structure):
                class _INPUT(ctypes.Union):
                    _fields_ = [("ki", KEYBDINPUT)]
                _anonymous_ = ("_input",)
                _fields_ = [
                    ("type", ctypes.wintypes.DWORD),
                    ("_input", _INPUT)
                ]
            
            # 按下
            inp = INPUT()
            inp.type = 1  # INPUT_KEYBOARD
            inp.ki.wVk = vk
            inp.ki.wScan = 0
            inp.ki.dwFlags = 0
            inp.ki.time = 0
            inp.ki.dwExtraInfo = None
            
            ctypes.windll.user32.SendInput(1, ctypes.byref(inp), ctypes.sizeof(INPUT))
            
            time.sleep(0.02)
            
            # 释放
            inp.ki.dwFlags = 2  # KEYEVENTF_KEYUP
            ctypes.windll.user32.SendInput(1, ctypes.byref(inp), ctypes.sizeof(INPUT))
            
            success_methods += 1
        except:
            pass
        
        logger.info(f"按键 {key}: {success_methods}/3 方法成功")
        return success_methods > 0
    
    def inject_alt_combo_hardcore(self, key):
        """硬核Alt组合键注入"""
        alt_vk = self.vk_codes['alt']
        key_vk = self.vk_codes.get(key)
        
        if not key_vk:
            return False
        
        try:
            self.force_window_focus()
            time.sleep(0.01)
            
            # 按下Alt
            self.inject_key_hardcore('alt')
            time.sleep(0.01)
            
            # 按下目标键
            win32api.keybd_event(key_vk, 0, 0, 0)
            time.sleep(0.02)
            win32api.keybd_event(key_vk, 0, win32con.KEYEVENTF_KEYUP, 0)
            
            # 释放Alt
            time.sleep(0.01)
            win32api.keybd_event(alt_vk, 0, win32con.KEYEVENTF_KEYUP, 0)
            
            logger.info(f"Alt组合键 Alt+{key} 注入完成")
            return True
            
        except Exception as e:
            logger.error(f"Alt组合键注入失败: {e}")
            return False
    
    def rapid_fire_test(self):
        """快速连发测试"""
        logger.info("开始快速连发测试...")
        
        skills = ['1', '2', '3', '4', '5']
        
        try:
            for i in range(20):  # 连发20次
                skill = skills[i % len(skills)]
                logger.info(f"快速连发 #{i+1}: {skill}")
                
                self.inject_key_hardcore(skill)
                time.sleep(0.1)  # 极短间隔
                
        except KeyboardInterrupt:
            logger.info("快速连发测试停止")
    
    def ultimate_combat_test(self):
        """终极战斗测试"""
        logger.info("=" * 50)
        logger.info("终极战斗测试开始")
        logger.info("=" * 50)
        
        basic_skills = ['1', '2', '3', '4', '5', '6', '7', '8', '9']
        alt_skills = ['1', '2', '3', '4', '5']  # Alt+1 到 Alt+5
        
        skill_index = 0
        alt_index = 0
        cycle_count = 0
        
        try:
            while True:
                cycle_count += 1
                logger.info(f"\\n=== 战斗循环 #{cycle_count} ===")
                
                # 选择目标
                if cycle_count % 3 == 1:  # 每3个循环选择一次目标
                    logger.info("🎯 选择目标...")
                    self.inject_key_hardcore('tab')
                    time.sleep(0.3)
                
                # 释放基础技能
                for _ in range(3):
                    skill = basic_skills[skill_index % len(basic_skills)]
                    logger.info(f"⚔️ 基础技能: {skill}")
                    self.inject_key_hardcore(skill)
                    skill_index += 1
                    time.sleep(0.15)
                
                # 释放Alt技能
                alt_skill = alt_skills[alt_index % len(alt_skills)]
                logger.info(f"⚡ Alt技能: Alt+{alt_skill}")
                self.inject_alt_combo_hardcore(alt_skill)
                alt_index += 1
                time.sleep(0.2)
                
        except KeyboardInterrupt:
            logger.info("\\n终极战斗测试停止")

def main():
    print("💥 永恒之塔强制输入注入器")
    print("=" * 50)
    
    # 检查管理员权限
    try:
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if not is_admin:
            print("❌ 需要管理员权限!")
            print("请右键程序 -> '以管理员身份运行'")
            input("按回车退出...")
            return
    except:
        print("❌ 无法检查管理员权限")
        return
    
    # 创建注入器
    injector = ForceInputInjector()
    
    if not injector.game_hwnd:
        print("❌ 无法找到游戏，请确保游戏正在运行")
        input("按回车退出...")
        return
    
    while True:
        print("\\n💥 选择测试模式:")
        print("1. 单键测试")
        print("2. Alt组合键测试")
        print("3. 快速连发测试")
        print("4. 终极战斗测试")
        print("5. 退出")
        
        try:
            choice = input("\\n请选择 (1-5): ").strip()
            
            if choice == '1':
                key = input("输入要测试的键 (1-9): ").strip()
                injector.inject_key_hardcore(key)
                
            elif choice == '2':
                key = input("输入Alt组合键的目标键 (1-9): ").strip()
                injector.inject_alt_combo_hardcore(key)
                
            elif choice == '3':
                injector.rapid_fire_test()
                
            elif choice == '4':
                print("\\n💥 启动终极战斗测试...")
                print("这将使用最激进的方法强制发送按键")
                print("按Ctrl+C停止")
                injector.ultimate_combat_test()
                
            elif choice == '5':
                print("👋 退出程序")
                break
                
            else:
                print("❌ 无效选择")
                
        except KeyboardInterrupt:
            print("\\n👋 退出程序")
            break
        except Exception as e:
            logger.error(f"错误: {e}")

if __name__ == "__main__":
    main()