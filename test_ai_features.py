#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI功能测试脚本 - 测试新增的AI增强功能
"""

import sys
import os
import time
import logging

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('ai_test')

def test_screen_detector():
    """测试屏幕检测器的新功能"""
    print("=" * 50)
    print("测试屏幕检测器功能")
    print("=" * 50)
    
    try:
        from screen_detector import ScreenDetector
        
        detector = ScreenDetector()
        print(f"✓ 屏幕检测器初始化成功")
        print(f"  - OCR启用: {detector.ocr_enabled}")
        print(f"  - 模板匹配启用: {detector.template_matching_enabled}")
        print(f"  - 图像预处理启用: {detector.image_preprocessing_enabled}")
        
        # 测试图像预处理
        import numpy as np
        test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        processed = detector.preprocess_image(test_image)
        print(f"✓ 图像预处理测试成功，输出形状: {processed.shape}")
        
        # 测试OCR功能（如果Tesseract可用）
        try:
            text = detector.ocr_text_recognition()
            print(f"✓ OCR功能可用")
        except Exception as e:
            print(f"⚠ OCR功能不可用: {str(e)}")
        
        # 测试UI元素检测
        ui_elements = detector.detect_ui_elements(['button', 'health_bar'])
        print(f"✓ UI元素检测功能可用，检测到 {len(ui_elements)} 个元素")
        
        return True
        
    except Exception as e:
        print(f"✗ 屏幕检测器测试失败: {str(e)}")
        return False

def test_tactical_ai():
    """测试战术AI系统"""
    print("=" * 50)
    print("测试战术AI系统")
    print("=" * 50)
    
    try:
        from tactical_ai import TacticalAI, Target, PlayerState, TargetPriority
        from screen_detector import ScreenDetector
        
        # 创建模拟的内存读取器
        class MockMemoryReader:
            def __init__(self):
                self.process_id = 1234
                self.module_base = 0x400000
        
        memory_reader = MockMemoryReader()
        screen_detector = ScreenDetector()
        
        ai = TacticalAI(memory_reader, screen_detector)
        print(f"✓ 战术AI初始化成功")
        
        # 测试玩家状态更新
        player_state = ai.update_player_state()
        print(f"✓ 玩家状态更新成功: HP={player_state.hp_percent:.1f}, MP={player_state.mp_percent:.1f}")
        
        # 测试目标创建和优先级计算
        test_target = Target(
            id=1001,
            name="测试怪物",
            level=50,
            hp_percent=0.8,
            distance=15.0,
            threat_level=2
        )
        
        priority = ai._calculate_target_priority(test_target)
        print(f"✓ 目标优先级计算成功: {priority.name}")
        
        # 测试战斗决策
        action = ai.decide_combat_action(player_state, test_target)
        print(f"✓ 战斗决策成功: {action['type']} - {action.get('skill', 'N/A')}")
        
        # 测试AI状态获取
        status = ai.get_ai_status()
        print(f"✓ AI状态获取成功: 当前状态={status['current_state']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 战术AI测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_battle_manager_integration():
    """测试战斗管理器集成"""
    print("=" * 50)
    print("测试战斗管理器AI集成")
    print("=" * 50)
    
    try:
        from battle_logic import BattleManager
        from memory_reader import MemoryReader
        from input_simulator import InputSimulator
        from monster_select import MonsterSelector
        
        # 创建组件
        memory = MemoryReader()
        input_sim = InputSimulator()
        monster_selector = MonsterSelector(memory)
        battle_manager = BattleManager(memory, input_sim, monster_selector)
        
        print(f"✓ 战斗管理器初始化成功")
        print(f"  - AI启用: {battle_manager.ai_enabled}")
        print(f"  - 智能目标选择: {battle_manager.smart_target_selection}")
        print(f"  - 自适应技能: {battle_manager.adaptive_skill_usage}")
        
        # 测试AI模式切换
        battle_manager.toggle_ai_mode(True)
        print(f"✓ AI模式切换成功")
        
        # 测试智能目标选择切换
        battle_manager.toggle_smart_targeting(True)
        print(f"✓ 智能目标选择切换成功")
        
        # 测试自适应技能切换
        battle_manager.toggle_adaptive_skills(True)
        print(f"✓ 自适应技能切换成功")
        
        # 测试AI状态获取
        ai_status = battle_manager.get_ai_status()
        print(f"✓ AI状态获取成功: {len(ai_status)} 个状态项")
        
        # 测试AI参数设置
        battle_manager.set_ai_parameters(max_distance=25.0, hp_threshold=0.3)
        print(f"✓ AI参数设置成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 战斗管理器集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_dependencies():
    """测试依赖包"""
    print("=" * 50)
    print("测试依赖包")
    print("=" * 50)
    
    dependencies = [
        ('numpy', 'numpy'),
        ('cv2', 'cv2'),
        ('PIL', 'PIL'),
        ('pytesseract', 'pytesseract'),
        ('skimage', 'skimage'),
        ('scipy', 'scipy')
    ]
    
    success_count = 0
    
    for dep_name, import_name in dependencies:
        try:
            __import__(import_name)
            print(f"✓ {dep_name} 可用")
            success_count += 1
        except ImportError as e:
            print(f"✗ {dep_name} 不可用: {str(e)}")
    
    print(f"\n依赖包检查完成: {success_count}/{len(dependencies)} 可用")
    return success_count == len(dependencies)

def main():
    """主测试函数"""
    print("AI功能增强测试")
    print("=" * 50)
    
    test_results = []
    
    # 测试依赖包
    test_results.append(("依赖包", test_dependencies()))
    
    # 测试屏幕检测器
    test_results.append(("屏幕检测器", test_screen_detector()))
    
    # 测试战术AI
    test_results.append(("战术AI", test_tactical_ai()))
    
    # 测试战斗管理器集成
    test_results.append(("战斗管理器集成", test_battle_manager_integration()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！AI功能增强已成功实现。")
    else:
        print("⚠ 部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
