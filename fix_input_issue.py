#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复输入问题的完整解决方案
"""

import sys
import os
import time
import logging

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def diagnose_input_issue():
    """诊断输入问题"""
    print("=" * 60)
    print("🔍 诊断输入模拟器问题")
    print("=" * 60)
    
    issues_found = []
    solutions = []
    
    # 1. 检查输入模拟器导入
    try:
        from input_simulator import InputSimulator
        print("✓ 原版输入模拟器导入成功")
    except ImportError as e:
        issues_found.append("原版输入模拟器导入失败")
        print(f"❌ 原版输入模拟器导入失败: {e}")
    
    try:
        from input_simulator_fixed import InputSimulatorFixed
        print("✓ 修复版输入模拟器导入成功")
    except ImportError as e:
        issues_found.append("修复版输入模拟器不存在")
        print(f"❌ 修复版输入模拟器导入失败: {e}")
    
    # 2. 检查依赖库
    required_libs = ['keyboard', 'win32api', 'win32con', 'win32gui']
    for lib in required_libs:
        try:
            __import__(lib)
            print(f"✓ {lib} 库可用")
        except ImportError:
            issues_found.append(f"缺少 {lib} 库")
            print(f"❌ {lib} 库不可用")
            solutions.append(f"安装 {lib}: pip install {lib}")
    
    # 3. 检查权限
    import ctypes
    is_admin = ctypes.windll.shell32.IsUserAnAdmin()
    if is_admin:
        print("✓ 程序以管理员权限运行")
    else:
        issues_found.append("程序未以管理员权限运行")
        print("⚠ 程序未以管理员权限运行")
        solutions.append("以管理员权限运行程序")
    
    return issues_found, solutions

def test_input_methods():
    """测试不同的输入方法"""
    print("\\n" + "=" * 60)
    print("🧪 测试不同输入方法")
    print("=" * 60)
    
    print("请在5秒内切换到一个文本编辑器（如记事本）...")
    for i in range(5, 0, -1):
        print(f"倒计时: {i}秒")
        time.sleep(1)
    
    print("\\n开始测试...")
    
    # 方法1: keyboard库
    try:
        import keyboard
        print("\\n方法1: keyboard库")
        keyboard.press_and_release('1')
        time.sleep(0.5)
        keyboard.press_and_release('2')
        time.sleep(0.5)
        keyboard.press_and_release('3')
        print("✓ keyboard库测试完成")
    except Exception as e:
        print(f"❌ keyboard库测试失败: {e}")
    
    # 方法2: Win32 API
    try:
        import win32api
        import win32con
        print("\\n方法2: Win32 API")
        
        # 发送数字键4
        win32api.keybd_event(0x34, 0, 0, 0)  # 按下
        time.sleep(0.05)
        win32api.keybd_event(0x34, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放
        time.sleep(0.5)
        
        # 发送数字键5
        win32api.keybd_event(0x35, 0, 0, 0)  # 按下
        time.sleep(0.05)
        win32api.keybd_event(0x35, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放
        time.sleep(0.5)
        
        # 发送数字键6
        win32api.keybd_event(0x36, 0, 0, 0)  # 按下
        time.sleep(0.05)
        win32api.keybd_event(0x36, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放
        
        print("✓ Win32 API测试完成")
    except Exception as e:
        print(f"❌ Win32 API测试失败: {e}")
    
    # 方法3: 修复版输入模拟器
    try:
        from input_simulator_fixed import InputSimulatorFixed
        print("\\n方法3: 修复版输入模拟器")
        
        input_sim = InputSimulatorFixed()
        input_sim.press_key('7')
        time.sleep(0.5)
        input_sim.press_key('8')
        time.sleep(0.5)
        input_sim.press_key('9')
        
        print("✓ 修复版输入模拟器测试完成")
    except Exception as e:
        print(f"❌ 修复版输入模拟器测试失败: {e}")

def create_ultimate_input_simulator():
    """创建终极输入模拟器"""
    print("\\n" + "=" * 60)
    print("🛠️ 创建终极输入模拟器")
    print("=" * 60)
    
    ultimate_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
终极输入模拟器 - 多种方法确保按键发送成功
"""

import time
import logging
import ctypes
from ctypes import wintypes
import win32api
import win32con
import win32gui

logger = logging.getLogger('ultimate_input_simulator')

class UltimateInputSimulator:
    """终极输入模拟器 - 使用多种方法确保按键发送"""
    
    def __init__(self):
        self.vk_codes = {
            '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34, '5': 0x35,
            '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39, '0': 0x30,
            'f1': 0x70, 'f2': 0x71, 'f3': 0x72, 'f4': 0x73, 'f5': 0x74,
            'space': 0x20, 'alt': 0x12, 'ctrl': 0x11, 'shift': 0x10
        }
        self.game_window = None
        logger.info("终极输入模拟器已初始化")
    
    def find_game_window(self):
        """查找游戏窗口"""
        def enum_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if any(keyword in title.lower() for keyword in ['aion', '永恒之塔']):
                    windows.append((hwnd, title))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_callback, windows)
        
        if windows:
            self.game_window = windows[0][0]
            logger.info(f"找到游戏窗口: {windows[0][1]}")
            return True
        return False
    
    def press_key(self, key):
        """发送按键 - 使用多种方法确保成功"""
        vk_code = self.vk_codes.get(key.lower())
        if not vk_code:
            logger.error(f"不支持的按键: {key}")
            return False
        
        success_count = 0
        methods_tried = 0
        
        # 方法1: Win32 keybd_event (全局)
        try:
            win32api.keybd_event(vk_code, 0, 0, 0)  # 按下
            time.sleep(0.05)
            win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放
            success_count += 1
            logger.debug(f"方法1成功: {key}")
        except Exception as e:
            logger.debug(f"方法1失败: {e}")
        methods_tried += 1
        
        # 方法2: SendInput API
        try:
            # 定义INPUT结构
            PUL = ctypes.POINTER(ctypes.c_ulong)
            
            class KeyBdInput(ctypes.Structure):
                _fields_ = [("wVk", ctypes.c_ushort),
                           ("wScan", ctypes.c_ushort),
                           ("dwFlags", ctypes.c_ulong),
                           ("time", ctypes.c_ulong),
                           ("dwExtraInfo", PUL)]
            
            class HardwareInput(ctypes.Structure):
                _fields_ = [("uMsg", ctypes.c_ulong),
                           ("wParamL", ctypes.c_short),
                           ("wParamH", ctypes.c_ushort)]
            
            class MouseInput(ctypes.Structure):
                _fields_ = [("dx", ctypes.c_long),
                           ("dy", ctypes.c_long),
                           ("mouseData", ctypes.c_ulong),
                           ("dwFlags", ctypes.c_ulong),
                           ("time", ctypes.c_ulong),
                           ("dwExtraInfo", PUL)]
            
            class Input_I(ctypes.Union):
                _fields_ = [("ki", KeyBdInput),
                           ("mi", MouseInput),
                           ("hi", HardwareInput)]
            
            class Input(ctypes.Structure):
                _fields_ = [("type", ctypes.c_ulong),
                           ("ii", Input_I)]
            
            # 按下
            extra = ctypes.c_ulong(0)
            ii_ = Input_I()
            ii_.ki = KeyBdInput(vk_code, 0, 0, 0, ctypes.pointer(extra))
            x = Input(ctypes.c_ulong(1), ii_)
            ctypes.windll.user32.SendInput(1, ctypes.pointer(x), ctypes.sizeof(x))
            
            time.sleep(0.05)
            
            # 释放
            ii_.ki = KeyBdInput(vk_code, 0, 2, 0, ctypes.pointer(extra))
            x = Input(ctypes.c_ulong(1), ii_)
            ctypes.windll.user32.SendInput(1, ctypes.pointer(x), ctypes.sizeof(x))
            
            success_count += 1
            logger.debug(f"方法2成功: {key}")
        except Exception as e:
            logger.debug(f"方法2失败: {e}")
        methods_tried += 1
        
        # 方法3: PostMessage到游戏窗口
        if self.game_window:
            try:
                scan_code = win32api.MapVirtualKey(vk_code, 0)
                lParam = (scan_code << 16) | 1
                
                win32gui.PostMessage(self.game_window, win32con.WM_KEYDOWN, vk_code, lParam)
                time.sleep(0.05)
                win32gui.PostMessage(self.game_window, win32con.WM_KEYUP, vk_code, lParam | 0xC0000000)
                
                success_count += 1
                logger.debug(f"方法3成功: {key}")
            except Exception as e:
                logger.debug(f"方法3失败: {e}")
            methods_tried += 1
        
        # 方法4: keyboard库 (如果可用)
        try:
            import keyboard
            keyboard.press_and_release(key)
            success_count += 1
            logger.debug(f"方法4成功: {key}")
        except Exception as e:
            logger.debug(f"方法4失败: {e}")
        methods_tried += 1
        
        success_rate = success_count / methods_tried
        logger.info(f"按键 {key}: {success_count}/{methods_tried} 方法成功 ({success_rate*100:.1f}%)")
        
        return success_count > 0
    
    def press_key_combo(self, keys):
        """发送组合键"""
        try:
            # 按下所有键
            for key in keys:
                vk_code = self.vk_codes.get(key.lower())
                if vk_code:
                    win32api.keybd_event(vk_code, 0, 0, 0)
            
            time.sleep(0.05)
            
            # 释放所有键
            for key in reversed(keys):
                vk_code = self.vk_codes.get(key.lower())
                if vk_code:
                    win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)
            
            logger.info(f"组合键发送: {'+'.join(keys)}")
            return True
        except Exception as e:
            logger.error(f"组合键发送失败: {e}")
            return False
    
    def test_all_methods(self):
        """测试所有输入方法"""
        logger.info("开始测试所有输入方法...")
        
        if not self.find_game_window():
            logger.warning("未找到游戏窗口，将使用全局方法")
        
        test_keys = ['1', '2', '3']
        for key in test_keys:
            self.press_key(key)
            time.sleep(0.5)
        
        logger.info("输入方法测试完成")
'''
    
    # 保存终极输入模拟器
    with open('私服垃圾/ultimate_input_simulator.py', 'w', encoding='utf-8') as f:
        f.write(ultimate_code)
    
    print("✓ 终极输入模拟器已创建: ultimate_input_simulator.py")

def provide_solutions():
    """提供解决方案"""
    print("\\n" + "=" * 60)
    print("💡 解决方案")
    print("=" * 60)
    
    solutions = [
        "1. 以管理员权限运行程序",
        "2. 确保游戏窗口处于前台并有焦点",
        "3. 检查游戏是否有反外挂保护",
        "4. 尝试使用不同的输入方法",
        "5. 在游戏中测试手动按键是否正常",
        "6. 检查键盘布局和输入法设置",
        "7. 暂时关闭杀毒软件和防火墙",
        "8. 使用窗口模式而不是全屏模式"
    ]
    
    for solution in solutions:
        print(f"  {solution}")
    
    print("\\n" + "=" * 60)
    print("🔧 立即修复步骤")
    print("=" * 60)
    
    steps = [
        "1. 关闭当前程序",
        "2. 右键点击程序 -> '以管理员身份运行'",
        "3. 确保游戏在窗口模式下运行",
        "4. 启动程序并连接到游戏",
        "5. 在游戏中手动测试按键1-9是否正常",
        "6. 如果手动按键正常，再启动自动战斗",
        "7. 观察日志中的按键发送信息"
    ]
    
    for step in steps:
        print(f"  {step}")

def main():
    """主函数"""
    print("🚀 输入问题修复工具")
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 1. 诊断问题
    issues, solutions = diagnose_input_issue()
    
    # 2. 测试输入方法
    test_input_methods()
    
    # 3. 创建终极输入模拟器
    create_ultimate_input_simulator()
    
    # 4. 提供解决方案
    provide_solutions()
    
    print("\\n" + "=" * 60)
    print("✅ 修复工具运行完成")
    print("=" * 60)
    
    if issues:
        print("\\n发现的问题:")
        for issue in issues:
            print(f"  ❌ {issue}")
    
    print("\\n建议:")
    print("1. 使用创建的 ultimate_input_simulator.py")
    print("2. 以管理员权限运行程序")
    print("3. 确保游戏窗口有焦点")
    print("4. 检查游戏的反外挂设置")

if __name__ == "__main__":
    main()