#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自动战斗系统测试脚本
用于验证增强的副本辅助功能
"""

import sys
import time
import logging
from auto_combat_system import AutoCombatSystem, BattleState, AttackIntention, LootPriority

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_auto_combat')

class MockMemoryReader:
    """模拟内存读取器"""
    def __init__(self):
        self.player_hp = 100.0
        self.player_mp = 100.0
    
    def get_player_info(self):
        return {
            "hp_percent": self.player_hp,
            "mp_percent": self.player_mp
        }

class MockInputSimulator:
    """模拟输入模拟器"""
    def __init__(self):
        self.pressed_keys = []
    
    def press_key(self, key):
        self.pressed_keys.append(key)
        logger.info(f"模拟按键: {key}")

class MockMonsterSelector:
    """模拟怪物选择器"""
    def __init__(self, memory):
        self.memory = memory

def test_auto_combat_system():
    """测试自动战斗系统"""
    logger.info("开始测试自动战斗系统...")
    
    # 创建模拟组件
    memory = MockMemoryReader()
    input_sim = MockInputSimulator()
    monster_selector = MockMonsterSelector(memory)
    
    # 创建自动战斗系统
    combat_system = AutoCombatSystem(memory, input_sim, monster_selector)
    
    # 测试配置设置
    logger.info("测试配置设置...")
    config = {
        "attack_range": 20.0,
        "search_range": 30.0,
        "retreat_hp_threshold": 25.0,
        "auto_loot_enabled": True,
        "loot_range": 15.0,
        "use_potions": True,
        "auto_buff": True,
        "loot_filter": {
            "LEGENDARY": True,
            "EPIC": True,
            "RARE": True,
            "UNCOMMON": False,
            "COMMON": False
        }
    }
    
    combat_system.set_config(config)
    logger.info("配置设置完成")
    
    # 测试技能系统
    logger.info("测试技能系统...")
    skills = combat_system.skills
    logger.info(f"已加载技能数量: {len(skills)}")
    
    for skill_id, skill in skills.items():
        logger.info(f"技能: {skill_id} - 类型: {skill.skill_type} - 优先级: {skill.priority}")
    
    # 测试攻击意图选择
    logger.info("测试攻击意图选择...")
    intention = combat_system._choose_attack_intention()
    logger.info(f"当前攻击意图: {intention}")
    
    # 测试战斗状态
    logger.info("测试战斗状态...")
    logger.info(f"当前战斗状态: {combat_system.battle_state}")
    
    # 测试启动和停止
    logger.info("测试系统启动...")
    combat_system.start()
    
    # 运行一段时间
    logger.info("系统运行中...")
    time.sleep(3)
    
    # 测试配置更新
    logger.info("测试配置更新...")
    new_config = {
        "attack_range": 25.0,
        "auto_loot_enabled": False
    }
    combat_system.set_config(new_config)
    
    time.sleep(2)
    
    # 停止系统
    logger.info("停止系统...")
    combat_system.stop()
    
    # 获取统计信息
    stats = combat_system.get_stats()
    logger.info(f"统计信息: {stats}")
    
    # 检查按键记录
    logger.info(f"按键记录: {input_sim.pressed_keys}")
    
    logger.info("自动战斗系统测试完成!")

def test_battle_states():
    """测试战斗状态枚举"""
    logger.info("测试战斗状态枚举...")
    
    states = [
        BattleState.IDLE,
        BattleState.SEARCHING,
        BattleState.ENGAGING,
        BattleState.FIGHTING,
        BattleState.LOOTING,
        BattleState.RETREATING,
        BattleState.DEAD
    ]
    
    for state in states:
        logger.info(f"战斗状态: {state.value}")

def test_attack_intentions():
    """测试攻击意图枚举"""
    logger.info("测试攻击意图枚举...")
    
    intentions = [
        AttackIntention.FINISH_ATTACK,
        AttackIntention.SWITCH_TARGET,
        AttackIntention.SKILL_ATTACK,
        AttackIntention.SIMPLE_ATTACK,
        AttackIntention.DEFENSIVE_ACTION,
        AttackIntention.BUFF_SELF,
        AttackIntention.HEAL_SELF,
        AttackIntention.LOOT_ITEMS
    ]
    
    for intention in intentions:
        logger.info(f"攻击意图: {intention.value}")

def test_loot_priorities():
    """测试拾取优先级枚举"""
    logger.info("测试拾取优先级枚举...")
    
    priorities = [
        LootPriority.LEGENDARY,
        LootPriority.EPIC,
        LootPriority.RARE,
        LootPriority.UNCOMMON,
        LootPriority.COMMON
    ]
    
    for priority in priorities:
        logger.info(f"拾取优先级: {priority.name} - 值: {priority.value}")

def main():
    """主测试函数"""
    logger.info("=" * 50)
    logger.info("自动战斗系统测试开始")
    logger.info("=" * 50)
    
    try:
        # 测试枚举
        test_battle_states()
        print()
        
        test_attack_intentions()
        print()
        
        test_loot_priorities()
        print()
        
        # 测试主系统
        test_auto_combat_system()
        
        logger.info("=" * 50)
        logger.info("所有测试完成!")
        logger.info("=" * 50)
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()