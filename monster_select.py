#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
怪物选择模块 - 负责过滤和排序可攻击的怪物目标
"""

import logging
import numpy as np

# 设置日志
logger = logging.getLogger('aion_monster_selector')

class MonsterSelector:
    """怪物选择器，用于过滤和选择最佳攻击目标"""
    
    def __init__(self, memory_reader):
        """初始化怪物选择器"""
        self.memory = memory_reader
        
        # 默认筛选配置
        self.min_level = 1
        self.max_level = 99
        self.max_distance = 30.0
        self.target_types = [0, 1]  # 0=普通怪, 1=精英怪, 2=BOSS
        self.hp_threshold = 0.05  # 忽略血量低于此值的怪物
        self.avoid_crowded = True  # 避免拥挤区域的怪物
        self.prefer_isolated = True  # 优先选择独立的怪物
        self.crowded_threshold = 5.0  # 判断拥挤的距离阈值
        
        logger.info("怪物选择器已初始化")
        
    def filter_monsters(self, monsters):
        """过滤怪物列表，返回符合条件的怪物"""
        if not monsters:
            return []
            
        # 获取角色等级作为参考
        try:
            player_level = self.memory.get_character_level()
        except Exception as e:
            logger.debug(f"获取角色等级失败，使用默认等级: {str(e)}")
            player_level = 50
            
        # 根据角色等级调整筛选范围
        self.min_level = max(1, player_level - 5)
        self.max_level = player_level + 3
        
        filtered = []
        for monster in monsters:
            # 基本过滤条件
            if 'level' in monster and (monster['level'] < self.min_level or monster['level'] > self.max_level):
                continue
                
            if 'hp' in monster and 'hp_max' in monster and monster['hp_max'] > 0:
                # 新格式：hp和hp_max是绝对值
                hp_percent = monster['hp'] / monster['hp_max']
                if hp_percent < self.hp_threshold:
                    continue
            elif 'hp' in monster:
                # 旧格式：hp是0-1的百分比
                if monster['hp'] < self.hp_threshold:
                    continue
                
            if 'distance' in monster and monster['distance'] > self.max_distance:
                continue
                
            if 'type' in monster and monster['type'] not in self.target_types:
                continue
                
            # 加入过滤后的列表
            filtered.append(monster)
            
        # 如果没有找到怪物，则放宽条件
        if not filtered and monsters:
            logger.debug("使用标准条件没有找到怪物，正在放宽条件...")
            
            # 放宽等级限制
            for monster in monsters:
                if 'distance' in monster and monster['distance'] > self.max_distance:
                    continue
                    
                if 'hp' in monster and monster['hp'] < self.hp_threshold:
                    continue
                    
                filtered.append(monster)
        
        # 对过滤后的怪物排序
        return self.sort_monsters(filtered)
    
    def sort_monsters(self, monsters):
        """根据优先级对怪物进行排序"""
        if not monsters:
            return []
            
        # 计算每个怪物的拥挤度
        if self.avoid_crowded or self.prefer_isolated:
            for i, monster in enumerate(monsters):
                crowded_factor = 0
                
                # 检查附近的其他怪物
                for j, other in enumerate(monsters):
                    if i == j:
                        continue
                        
                    # 计算两个怪物之间的距离
                    distance = self.calculate_distance(
                        monster['position'],
                        other['position']
                    )
                    
                    # 如果太近，增加拥挤度
                    if distance < self.crowded_threshold:
                        crowded_factor += 1
                
                # 保存拥挤度
                monster['crowded_factor'] = crowded_factor
        
        # 创建怪物排序函数
        def monster_sort_key(monster):
            # 基础得分，距离越近得分越高
            base_score = 1000 - monster['distance'] * 10
            
            # 调整等级因素
            level_diff = monster['level'] - self.memory.get_character_level()
            if level_diff > 0:
                # 高于玩家等级的怪物降低优先级
                level_factor = -level_diff * 50
            else:
                # 等于或低于玩家等级的怪物优先级适当提高
                level_factor = -level_diff * 10
                
            base_score += level_factor
            
            # 拥挤度调整
            if self.avoid_crowded and monster.get('crowded_factor', 0) > 2:
                base_score -= monster['crowded_factor'] * 100
                
            if self.prefer_isolated and monster.get('crowded_factor', 0) == 0:
                base_score += 200
                
            # 血量因素，优先选择血量高的怪物
            hp_factor = monster['hp'] * 100
            base_score += hp_factor
            
            # 返回最终得分
            return base_score
            
        # 按照计算的得分排序
        sorted_monsters = sorted(monsters, key=monster_sort_key, reverse=True)
        
        # 记录日志
        if sorted_monsters:
            top_monster = sorted_monsters[0]
            logger.debug(f"已选择最佳目标: ID={top_monster['id']}, 等级={top_monster['level']}, "
                        f"距离={top_monster['distance']:.1f}, 血量={top_monster['hp']:.2f}")
            
        return sorted_monsters
    
    def calculate_distance(self, pos1, pos2):
        """计算两点间的欧几里得距离"""
        return np.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2 + (pos1[2] - pos2[2])**2)
    
    def set_level_range(self, min_level, max_level):
        """设置目标等级范围"""
        try:
            min_lvl = int(min_level)
            max_lvl = int(max_level)
            
            if 1 <= min_lvl <= max_lvl <= 99:
                self.min_level = min_lvl
                self.max_level = max_lvl
                logger.info(f"设置目标等级范围: {min_lvl} - {max_lvl}")
                return True
            else:
                logger.warning("等级范围必须在1-99之间，且最小等级不能大于最大等级")
        except ValueError:
            logger.error(f"无效的等级范围: {min_level} - {max_level}")
            
        return False
    
    def set_max_distance(self, distance):
        """设置最大目标距离"""
        try:
            dist = float(distance)
            if 5.0 <= dist <= 100.0:
                self.max_distance = dist
                logger.info(f"设置最大目标距离: {dist}")
                return True
            else:
                logger.warning("距离必须在5-100之间")
        except ValueError:
            logger.error(f"无效的距离值: {distance}")
            
        return False
    
    def set_target_types(self, types):
        """设置目标类型列表
        
        参数:
            types: 类型列表, 例如 [0, 1] 表示普通怪和精英怪
        """
        if isinstance(types, list) and all(isinstance(t, int) for t in types):
            self.target_types = [t for t in types if 0 <= t <= 2]
            type_names = {0: "普通", 1: "精英", 2: "BOSS"}
            type_str = ", ".join(type_names.get(t, str(t)) for t in self.target_types)
            logger.info(f"设置目标类型: {type_str}")
            return True
        return False