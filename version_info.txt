
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'080404b0',
          [StringStruct(u'CompanyName', u'Aion增强工具'),
          StringStruct(u'FileDescription', u'Aion 4.5 4.6 5.8 7.7通用'),
          StringStruct(u'FileVersion', u'1.0.0'),
          StringStruct(u'InternalName', u'aion_enhancer'),
          StringStruct(u'LegalCopyright', u'Copyright 2025'),
          StringStruct(u'OriginalFilename', u'aion_enhancer.exe'),
          StringStruct(u'ProductName', u'Aion 4.5 4.6 5.8 7.7通用'),
          StringStruct(u'ProductVersion', u'1.0.0')])
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
