#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI主窗口模块 - 创建并管理自动战斗工具的图形界面
"""

import os
import sys
import logging
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QLineEdit, QSlider, QGroupBox, QCheckBox, QTableWidget,
    QTableWidgetItem, QTabWidget, QSpinBox, QTextEdit, QMessageBox,
    QGridLayout, QSplitter, QFrame, QDialog, QDialogButtonBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, pyqtSlot
from PyQt6.QtGui import QFont, QColor, QPixmap, QIcon

# 设置日志
logger = logging.getLogger('aion_ui')

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self, memory_reader, battle_manager):
        """初始化主窗口"""
        super().__init__()
        
        self.memory = memory_reader
        self.battle_manager = battle_manager
        
        # 设置窗口标题和大小
        self.setWindowTitle("Aion自动战斗工具")
        self.setMinimumSize(800, 600)
        
        # 创建UI组件
        self.init_ui()

        # AI控制变量
        self.ai_enabled = False
        self.smart_targeting = False
        self.adaptive_skills = False
        
        # 创建状态更新定时器
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self.update_status_display)
        self.status_timer.start(1000)  # 每1秒更新一次
        
        # 创建进程列表更新定时器
        self.process_timer = QTimer(self)
        self.process_timer.timeout.connect(self.refresh_process_list)
        self.process_timer.start(5000)  # 每5秒更新一次
        
        # 初始化状态
        self.refresh_process_list()
        
        logger.info("主窗口已初始化")
        
        # 应用现代化高科技风格的UI样式
        self.apply_dark_theme()
    
    def apply_dark_theme(self):
        """应用暗色主题和高科技风格"""
        # 设置应用程序样式表
        self.setStyleSheet("""
            QMainWindow, QDialog {
                background-color: #1e1e2e;
                color: #cdd6f4;
            }
            QWidget {
                background-color: #1e1e2e;
                color: #cdd6f4;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QTabWidget::pane {
                border: 1px solid #313244;
                background-color: #1e1e2e;
                border-radius: 4px;
            }
            QTabBar::tab {
                background-color: #313244;
                color: #cdd6f4;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #45475a;
                color: #f5e0dc;
                border-bottom: 2px solid #89b4fa;
            }
            QTabBar::tab:hover:!selected {
                background-color: #45475a;
            }
            QPushButton {
                background-color: #45475a;
                color: #cdd6f4;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #585b70;
            }
            QPushButton:pressed {
                background-color: #313244;
            }
            QPushButton:disabled {
                background-color: #313244;
                color: #6c7086;
            }
            QLineEdit, QComboBox {
                background-color: #313244;
                color: #cdd6f4;
                border: 1px solid #45475a;
                border-radius: 4px;
                padding: 5px;
            }
            QLineEdit:focus, QComboBox:focus {
                border: 1px solid #89b4fa;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(down-arrow.png);
                width: 12px;
                height: 12px;
            }
            QCheckBox {
                color: #cdd6f4;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 1px solid #45475a;
            }
            QCheckBox::indicator:unchecked {
                background-color: #313244;
            }
            QCheckBox::indicator:checked {
                background-color: #89b4fa;
                image: url(check.png);
            }
            QGroupBox {
                border: 1px solid #313244;
                border-radius: 4px;
                margin-top: 1.5ex;
                padding-top: 1ex;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                color: #89b4fa;
            }
            QScrollBar:vertical {
                border: none;
                background-color: #313244;
                width: 10px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #45475a;
                min-height: 20px;
                border-radius: 5px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar:horizontal {
                border: none;
                background-color: #313244;
                height: 10px;
                margin: 0px;
            }
            QScrollBar::handle:horizontal {
                background-color: #45475a;
                min-width: 20px;
                border-radius: 5px;
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }
            QListWidget, QTextEdit {
                background-color: #313244;
                color: #cdd6f4;
                border: 1px solid #45475a;
                border-radius: 4px;
            }
            QStatusBar {
                background-color: #313244;
                color: #cdd6f4;
            }
            QLabel {
                color: #cdd6f4;
            }
            QProgressBar {
                border: 1px solid #45475a;
                border-radius: 4px;
                background-color: #313244;
                text-align: center;
                color: #cdd6f4;
            }
            QProgressBar::chunk {
                background-color: #89b4fa;
                border-radius: 3px;
            }
        """)
        
        # 设置窗口标题和图标
        self.setWindowTitle("Aion 4.5 4.6 5.8 7.7通用")
        
        # 设置应用图标
        icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "icon.ico")
        if os.path.exists(icon_path):
            from PyQt6.QtGui import QIcon
            self.setWindowIcon(QIcon(icon_path))
        else:
            # 如果没有找到图标文件，尝试创建一个
            try:
                # 导入图标创建模块
                sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                from create_icon import create_app_icon
                create_app_icon()
                
                # 再次检查图标是否已创建
                if os.path.exists(icon_path):
                    from PyQt6.QtGui import QIcon
                    self.setWindowIcon(QIcon(icon_path))
                else:
                    print("无法找到或创建图标文件")
            except Exception as e:
                print(f"设置图标时出错: {e}")
        
    def init_ui(self):
        """初始化UI组件"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建顶部控制区域
        top_control_layout = QHBoxLayout()
        
        # 创建进程选择组
        process_group = QGroupBox("游戏进程")
        process_layout = QVBoxLayout(process_group)
        
        # 进程下拉框和刷新按钮
        process_row = QHBoxLayout()
        self.process_combo = QComboBox()
        self.process_combo.setMinimumWidth(200)
        process_row.addWidget(self.process_combo)
        
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.clicked.connect(self.refresh_process_list)
        process_row.addWidget(self.refresh_button)
        
        # 添加自动搜索按钮
        auto_search_button = QPushButton("自动搜索")
        auto_search_button.setToolTip("自动搜索并连接到Aion游戏进程")
        auto_search_button.clicked.connect(self.auto_search_game)
        process_row.addWidget(auto_search_button)
        
        process_layout.addLayout(process_row)
        
        # 连接按钮
        self.connect_button = QPushButton("连接到游戏")
        self.connect_button.clicked.connect(self.connect_to_game)
        process_layout.addWidget(self.connect_button)
        
        top_control_layout.addWidget(process_group)
        
        # 创建副本专用组
        battle_group = QGroupBox("副本专用")
        battle_layout = QVBoxLayout(battle_group)
        
        # 开始/停止按钮
        self.start_button = QPushButton("开始副本辅助")
        self.start_button.setEnabled(False)  # 初始时禁用
        self.start_button.clicked.connect(self.toggle_auto_battle)
        self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
        battle_layout.addWidget(self.start_button)
        
        top_control_layout.addWidget(battle_group)
        
        # 添加顶部控制区域到主布局
        main_layout.addLayout(top_control_layout)
        
        # 创建功能选项卡
        self.tabs = QTabWidget()
        
        # 创建基础功能选项卡
        basic_tab = QWidget()
        basic_layout = QVBoxLayout(basic_tab)
        
        # 创建技能加速控制组
        attack_speed_group = QGroupBox("技能加速")
        attack_speed_layout = QHBoxLayout(attack_speed_group)
        
        self.attack_speed_check = QCheckBox("启用技能加速")
        self.attack_speed_check.setEnabled(False)
        self.attack_speed_check.stateChanged.connect(self.toggle_attack_speed)
        attack_speed_layout.addWidget(self.attack_speed_check)
        
        self.attack_speed_value = QLineEdit("200")
        self.attack_speed_value.setEnabled(False)
        self.attack_speed_value.setMaximumWidth(60)
        attack_speed_layout.addWidget(self.attack_speed_value)
        
        basic_layout.addWidget(attack_speed_group)
        
        # 创建移动加速控制组
        move_speed_group = QGroupBox("移动加速")
        move_speed_layout = QHBoxLayout(move_speed_group)
        
        self.move_speed_check = QCheckBox("启用移动加速")
        self.move_speed_check.setEnabled(False)
        self.move_speed_check.stateChanged.connect(self.toggle_move_speed)
        move_speed_layout.addWidget(self.move_speed_check)
        
        self.move_speed_value = QLineEdit("2.0")
        self.move_speed_value.setEnabled(False)
        self.move_speed_value.setMaximumWidth(60)
        move_speed_layout.addWidget(self.move_speed_value)
        
        basic_layout.addWidget(move_speed_group)
        
        # 添加基础功能选项卡
        self.tabs.addTab(basic_tab, "基础功能")
        
        # 创建高级功能选项卡
        advanced_tab = QWidget()
        advanced_layout = QVBoxLayout(advanced_tab)
        
        # 创建攻击相关功能选项卡
        attack_tab = QWidget()
        attack_layout = QVBoxLayout(attack_tab)
        
        # 创建攻击距离控制组
        attack_range_group = QGroupBox("攻击距离")
        attack_range_layout = QHBoxLayout(attack_range_group)
        
        self.attack_range_check = QCheckBox("启用攻击距离修改")
        self.attack_range_check.setEnabled(False)
        self.attack_range_check.stateChanged.connect(self.toggle_attack_range)
        attack_range_layout.addWidget(self.attack_range_check)
        
        self.attack_range_value = QLineEdit("10.0")
        self.attack_range_value.setEnabled(False)
        self.attack_range_value.setMaximumWidth(60)
        attack_range_layout.addWidget(self.attack_range_value)
        
        attack_layout.addWidget(attack_range_group)
        
        # 创建反隐控制组
        stealth_group = QGroupBox("反隐功能")
        stealth_layout = QHBoxLayout(stealth_group)
        
        self.stealth_check = QCheckBox("启用反隐")
        self.stealth_check.setEnabled(False)
        self.stealth_check.stateChanged.connect(self.toggle_stealth)
        stealth_layout.addWidget(self.stealth_check)
        
        attack_layout.addWidget(stealth_group)
        
        # 添加攻击相关功能选项卡
        self.tabs.addTab(attack_tab, "攻击功能")

        # 创建AI增强功能选项卡
        ai_tab = QWidget()
        ai_layout = QVBoxLayout(ai_tab)

        # AI总开关组
        ai_main_group = QGroupBox("AI增强系统")
        ai_main_layout = QVBoxLayout(ai_main_group)

        self.ai_enabled_check = QCheckBox("启用AI增强模式")
        self.ai_enabled_check.setEnabled(False)
        self.ai_enabled_check.stateChanged.connect(self.toggle_ai_mode)
        self.ai_enabled_check.setToolTip("启用后将使用AI进行智能战斗决策")
        ai_main_layout.addWidget(self.ai_enabled_check)

        ai_layout.addWidget(ai_main_group)

        # 智能目标选择组
        smart_target_group = QGroupBox("智能目标选择")
        smart_target_layout = QVBoxLayout(smart_target_group)

        self.smart_targeting_check = QCheckBox("启用智能目标选择")
        self.smart_targeting_check.setEnabled(False)
        self.smart_targeting_check.stateChanged.connect(self.toggle_smart_targeting)
        self.smart_targeting_check.setToolTip("AI将根据目标威胁等级、距离等因素智能选择最佳目标")
        smart_target_layout.addWidget(self.smart_targeting_check)

        # 目标选择参数
        target_params_layout = QHBoxLayout()
        target_params_layout.addWidget(QLabel("最大距离:"))
        self.max_distance_spin = QSpinBox()
        self.max_distance_spin.setRange(10, 100)
        self.max_distance_spin.setValue(30)
        self.max_distance_spin.setSuffix(" 米")
        self.max_distance_spin.setEnabled(False)
        target_params_layout.addWidget(self.max_distance_spin)

        target_params_layout.addWidget(QLabel("血量阈值:"))
        self.hp_threshold_spin = QSpinBox()
        self.hp_threshold_spin.setRange(10, 90)
        self.hp_threshold_spin.setValue(20)
        self.hp_threshold_spin.setSuffix(" %")
        self.hp_threshold_spin.setEnabled(False)
        target_params_layout.addWidget(self.hp_threshold_spin)

        smart_target_layout.addLayout(target_params_layout)
        ai_layout.addWidget(smart_target_group)

        # 自适应技能使用组
        adaptive_skill_group = QGroupBox("自适应技能使用")
        adaptive_skill_layout = QVBoxLayout(adaptive_skill_group)

        self.adaptive_skills_check = QCheckBox("启用自适应技能使用")
        self.adaptive_skills_check.setEnabled(False)
        self.adaptive_skills_check.stateChanged.connect(self.toggle_adaptive_skills)
        self.adaptive_skills_check.setToolTip("AI将根据战斗情况智能选择最合适的技能")
        adaptive_skill_layout.addWidget(self.adaptive_skills_check)

        ai_layout.addWidget(adaptive_skill_group)

        # OCR文字识别组
        ocr_group = QGroupBox("OCR文字识别")
        ocr_layout = QVBoxLayout(ocr_group)

        self.ocr_enabled_check = QCheckBox("启用OCR文字识别")
        self.ocr_enabled_check.setEnabled(False)
        self.ocr_enabled_check.stateChanged.connect(self.toggle_ocr)
        self.ocr_enabled_check.setToolTip("启用后可以识别屏幕上的文字信息")
        ocr_layout.addWidget(self.ocr_enabled_check)

        # OCR测试按钮
        ocr_test_layout = QHBoxLayout()
        self.ocr_test_button = QPushButton("测试OCR")
        self.ocr_test_button.setEnabled(False)
        self.ocr_test_button.clicked.connect(self.test_ocr)
        ocr_test_layout.addWidget(self.ocr_test_button)

        self.ocr_result_label = QLabel("OCR结果将显示在这里")
        self.ocr_result_label.setWordWrap(True)
        ocr_test_layout.addWidget(self.ocr_result_label)

        ocr_layout.addLayout(ocr_test_layout)
        ai_layout.addWidget(ocr_group)

        # 模板匹配组
        template_group = QGroupBox("高级模板匹配")
        template_layout = QVBoxLayout(template_group)

        self.template_matching_check = QCheckBox("启用高级模板匹配")
        self.template_matching_check.setEnabled(False)
        self.template_matching_check.stateChanged.connect(self.toggle_template_matching)
        self.template_matching_check.setToolTip("启用后可以进行多尺度、多角度的图像模板匹配")
        template_layout.addWidget(self.template_matching_check)

        # 模板匹配参数
        template_params_layout = QHBoxLayout()
        template_params_layout.addWidget(QLabel("匹配阈值:"))
        self.template_threshold_spin = QSpinBox()
        self.template_threshold_spin.setRange(50, 99)
        self.template_threshold_spin.setValue(80)
        self.template_threshold_spin.setSuffix(" %")
        self.template_threshold_spin.setEnabled(False)
        template_params_layout.addWidget(self.template_threshold_spin)

        template_layout.addLayout(template_params_layout)
        ai_layout.addWidget(template_group)

        # AI状态显示组
        ai_status_group = QGroupBox("AI状态信息")
        ai_status_layout = QVBoxLayout(ai_status_group)

        self.ai_status_text = QTextEdit()
        self.ai_status_text.setMaximumHeight(100)
        self.ai_status_text.setReadOnly(True)
        ai_status_layout.addWidget(self.ai_status_text)

        ai_layout.addWidget(ai_status_group)

        # 自动攻击组
        auto_attack_group = QGroupBox("自动攻击周围怪物")
        auto_attack_layout = QVBoxLayout(auto_attack_group)

        self.auto_attack_check = QCheckBox("启用自动攻击周围怪物")
        self.auto_attack_check.setEnabled(False)
        self.auto_attack_check.stateChanged.connect(self.toggle_auto_attack)
        self.auto_attack_check.setToolTip("自动扫描并攻击指定范围内的怪物")
        auto_attack_layout.addWidget(self.auto_attack_check)

        # 攻击范围设置
        range_layout = QHBoxLayout()
        range_layout.addWidget(QLabel("攻击范围:"))
        self.attack_range_spin = QSpinBox()
        self.attack_range_spin.setRange(5, 50)
        self.attack_range_spin.setValue(30)
        self.attack_range_spin.setSuffix(" 米")
        self.attack_range_spin.setEnabled(False)
        self.attack_range_spin.valueChanged.connect(self.update_attack_range)
        range_layout.addWidget(self.attack_range_spin)

        range_layout.addWidget(QLabel("扫描间隔:"))
        self.scan_interval_spin = QSpinBox()
        self.scan_interval_spin.setRange(50, 1000)
        self.scan_interval_spin.setValue(100)
        self.scan_interval_spin.setSuffix(" 毫秒")
        self.scan_interval_spin.setEnabled(False)
        self.scan_interval_spin.valueChanged.connect(self.update_scan_interval)
        range_layout.addWidget(self.scan_interval_spin)

        auto_attack_layout.addLayout(range_layout)

        # 专注击杀模式
        self.focus_kill_check = QCheckBox("启用专注击杀模式")
        self.focus_kill_check.setEnabled(False)
        self.focus_kill_check.setChecked(True)  # 默认启用
        self.focus_kill_check.stateChanged.connect(self.toggle_focus_kill_mode)
        self.focus_kill_check.setToolTip("专注击杀当前目标直到死亡，避免频繁切换目标")
        auto_attack_layout.addWidget(self.focus_kill_check)

        # 专注击杀参数
        focus_params_layout = QHBoxLayout()
        focus_params_layout.addWidget(QLabel("最大专注时间:"))
        self.max_focus_time_spin = QSpinBox()
        self.max_focus_time_spin.setRange(5, 60)
        self.max_focus_time_spin.setValue(15)
        self.max_focus_time_spin.setSuffix(" 秒")
        self.max_focus_time_spin.setEnabled(False)
        self.max_focus_time_spin.valueChanged.connect(self.update_focus_parameters)
        focus_params_layout.addWidget(self.max_focus_time_spin)

        focus_params_layout.addWidget(QLabel("无伤害超时:"))
        self.no_damage_timeout_spin = QSpinBox()
        self.no_damage_timeout_spin.setRange(2, 20)
        self.no_damage_timeout_spin.setValue(5)
        self.no_damage_timeout_spin.setSuffix(" 秒")
        self.no_damage_timeout_spin.setEnabled(False)
        self.no_damage_timeout_spin.valueChanged.connect(self.update_focus_parameters)
        focus_params_layout.addWidget(self.no_damage_timeout_spin)

        auto_attack_layout.addLayout(focus_params_layout)
        ai_layout.addWidget(auto_attack_group)

        # 自动范围打怪组
        auto_aoe_group = QGroupBox("自动范围打怪")
        auto_aoe_layout = QVBoxLayout(auto_aoe_group)

        self.auto_aoe_check = QCheckBox("启用自动范围打怪")
        self.auto_aoe_check.setEnabled(False)
        self.auto_aoe_check.stateChanged.connect(self.toggle_auto_aoe)
        self.auto_aoe_check.setToolTip("自动使用范围技能攻击多个目标")
        auto_aoe_layout.addWidget(self.auto_aoe_check)

        # 范围技能设置
        aoe_settings_layout = QHBoxLayout()
        aoe_settings_layout.addWidget(QLabel("范围技能:"))
        self.aoe_skills_edit = QLineEdit()
        self.aoe_skills_edit.setText("3,6,9")
        self.aoe_skills_edit.setEnabled(False)
        self.aoe_skills_edit.textChanged.connect(self.update_aoe_skills)
        self.aoe_skills_edit.setToolTip("输入范围技能按键，用逗号分隔，如: 3,6,9")
        aoe_settings_layout.addWidget(self.aoe_skills_edit)

        aoe_settings_layout.addWidget(QLabel("最少目标:"))
        self.aoe_min_targets_spin = QSpinBox()
        self.aoe_min_targets_spin.setRange(1, 10)
        self.aoe_min_targets_spin.setValue(2)
        self.aoe_min_targets_spin.setEnabled(False)
        self.aoe_min_targets_spin.valueChanged.connect(self.update_aoe_min_targets)
        self.aoe_min_targets_spin.setToolTip("使用范围技能需要的最少目标数量")
        aoe_settings_layout.addWidget(self.aoe_min_targets_spin)

        auto_aoe_layout.addLayout(aoe_settings_layout)
        ai_layout.addWidget(auto_aoe_group)

        # 手动点怪自动技能组
        manual_target_group = QGroupBox("手动点怪自动技能")
        manual_target_layout = QVBoxLayout(manual_target_group)

        self.manual_target_auto_skill_check = QCheckBox("启用手动点怪自动技能")
        self.manual_target_auto_skill_check.setEnabled(False)
        self.manual_target_auto_skill_check.stateChanged.connect(self.toggle_manual_target_auto_skill)
        self.manual_target_auto_skill_check.setToolTip("手动选择目标后，AI自动释放最佳技能")
        manual_target_layout.addWidget(self.manual_target_auto_skill_check)

        # 手动目标控制
        manual_control_layout = QHBoxLayout()
        self.force_manual_target_button = QPushButton("设置当前目标为手动")
        self.force_manual_target_button.setEnabled(False)
        self.force_manual_target_button.clicked.connect(self.force_manual_target)
        self.force_manual_target_button.setToolTip("将当前选中的目标设置为手动目标")
        manual_control_layout.addWidget(self.force_manual_target_button)

        self.clear_manual_target_button = QPushButton("清除手动目标")
        self.clear_manual_target_button.setEnabled(False)
        self.clear_manual_target_button.clicked.connect(self.clear_manual_target)
        self.clear_manual_target_button.setToolTip("清除当前的手动目标")
        manual_control_layout.addWidget(self.clear_manual_target_button)

        manual_target_layout.addLayout(manual_control_layout)
        ai_layout.addWidget(manual_target_group)

        # 添加AI功能选项卡
        self.tabs.addTab(ai_tab, "AI增强")
        
        # 创建移动相关功能选项卡
        movement_tab = QWidget()
        movement_layout = QVBoxLayout(movement_tab)
        
        # 创建锁空控制组
        air_lock_group = QGroupBox("锁空功能")
        air_lock_layout = QHBoxLayout(air_lock_group)
        
        self.air_lock_check = QCheckBox("启用锁空")
        self.air_lock_check.setEnabled(False)
        self.air_lock_check.stateChanged.connect(self.toggle_air_lock)
        air_lock_layout.addWidget(self.air_lock_check)
        
        movement_layout.addWidget(air_lock_group)
        
        # 创建飞天控制组
        fly_group = QGroupBox("飞天功能")
        fly_layout = QVBoxLayout(fly_group)
        
        self.fly_check = QCheckBox("启用飞天")
        self.fly_check.setEnabled(False)
        self.fly_check.stateChanged.connect(self.toggle_fly)
        fly_layout.addWidget(self.fly_check)
        
        fly_controls = QHBoxLayout()
        
        self.fly_value = QLineEdit("10.0")
        self.fly_value.setEnabled(False)
        self.fly_value.setMaximumWidth(60)
        fly_controls.addWidget(self.fly_value)
        
        self.fly_up_btn = QPushButton("上升")
        self.fly_up_btn.setEnabled(False)
        self.fly_up_btn.clicked.connect(self.fly_up)
        fly_controls.addWidget(self.fly_up_btn)
        
        self.fly_down_btn = QPushButton("下降")
        self.fly_down_btn.setEnabled(False)
        self.fly_down_btn.clicked.connect(self.fly_down)
        fly_controls.addWidget(self.fly_down_btn)
        
        fly_layout.addLayout(fly_controls)
        
        movement_layout.addWidget(fly_group)
        
        # 添加移动相关功能选项卡
        self.tabs.addTab(movement_tab, "移动功能")
        
        # 创建视觉相关功能选项卡
        visual_tab = QWidget()
        visual_layout = QVBoxLayout(visual_tab)
        
        # 创建视野扩展控制组
        view_range_group = QGroupBox("视野扩展")
        view_range_layout = QHBoxLayout(view_range_group)
        
        self.view_range_check = QCheckBox("启用视野扩展")
        self.view_range_check.setEnabled(False)
        self.view_range_check.stateChanged.connect(self.toggle_view_range)
        view_range_layout.addWidget(self.view_range_check)
        
        self.view_range_value = QLineEdit("100.0")
        self.view_range_value.setEnabled(False)
        self.view_range_value.setMaximumWidth(60)
        view_range_layout.addWidget(self.view_range_value)
        
        visual_layout.addWidget(view_range_group)
        
        # 创建显血控制组
        show_hp_group = QGroupBox("显血功能")
        show_hp_layout = QHBoxLayout(show_hp_group)
        
        self.show_hp_check = QCheckBox("启用显血")
        self.show_hp_check.setEnabled(False)
        self.show_hp_check.stateChanged.connect(self.toggle_show_hp)
        show_hp_layout.addWidget(self.show_hp_check)
        
        visual_layout.addWidget(show_hp_group)
        
        # 创建地图扩展控制组
        map_extend_group = QGroupBox("地图扩展")
        map_extend_layout = QHBoxLayout(map_extend_group)
        
        self.map_extend_check = QCheckBox("启用地图扩展")
        self.map_extend_check.setEnabled(False)
        self.map_extend_check.stateChanged.connect(self.toggle_map_extend)
        map_extend_layout.addWidget(self.map_extend_check)
        
        self.map_extend_value = QLineEdit("200.0")
        self.map_extend_value.setEnabled(False)
        self.map_extend_value.setMaximumWidth(60)
        map_extend_layout.addWidget(self.map_extend_value)
        
        visual_layout.addWidget(map_extend_group)
        
        # 添加视觉相关功能选项卡
        self.tabs.addTab(visual_tab, "视觉功能")
        
        # 添加高级功能选项卡（保留，但内容已移至其他选项卡）
        advanced_layout.addWidget(QLabel("高级功能已分类至其他选项卡"))
        self.tabs.addTab(advanced_tab, "高级功能")
        
        # 添加选项卡到主布局
        main_layout.addWidget(self.tabs)
        
        # 创建日志区域
        log_group = QGroupBox("游戏动作日志")
        log_layout = QVBoxLayout(log_group)
        
        # 创建游戏动作日志文本框
        self.game_action_text = QTextEdit()
        self.game_action_text.setReadOnly(True)
        self.game_action_text.setMinimumHeight(200)
        self.game_action_text.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #FFFFFF;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 10pt;
            }
        """)
        log_layout.addWidget(self.game_action_text)
        
        # 日志控制按钮
        log_buttons_layout = QHBoxLayout()
        
        # 清空日志按钮
        clear_game_log_button = QPushButton("清空日志")
        clear_game_log_button.clicked.connect(self.clear_game_log)
        log_buttons_layout.addWidget(clear_game_log_button)
        
        # 添加日志控制按钮到日志布局
        log_layout.addLayout(log_buttons_layout)
        
        # 添加日志区域到主布局
        main_layout.addWidget(log_group)
        
        # 创建状态栏
        self.statusBar().showMessage("准备就绪")
        
        logger.info("UI组件已初始化")
    
    def refresh_process_list(self):
        """刷新进程列表"""
        # 记住当前选择
        current_text = self.process_combo.currentText()
        current_pid = self.process_combo.currentData() if self.process_combo.currentIndex() >= 0 else None
        
        # 清空下拉框
        self.process_combo.clear()
        
        # 获取进程列表
        processes = self.memory.get_processes_list()
        
        if not processes:
            self.process_combo.addItem("未找到游戏进程")
            return
            
        # 添加进程到下拉框
        aion_index = -1  # 记录第一个Aion进程的索引
        
        for i, proc in enumerate(processes):
            text = f"{proc['name']} (PID: {proc['pid']})"
            self.process_combo.addItem(text, proc['pid'])
            
            # 记录第一个Aion进程
            if aion_index == -1 and proc['is_aion']:
                aion_index = i
        
        # 自动选择逻辑：
        selected_index = -1
        
        # 1. 如果之前有选择的进程仍然存在，保持选择
        if current_pid:
            for i in range(self.process_combo.count()):
                if self.process_combo.itemData(i) == current_pid:
                    selected_index = i
                    break
        
        # 2. 如果没有之前的选择或进程已不存在，优先选择第一个Aion进程
        if selected_index == -1 and aion_index != -1:
            selected_index = aion_index
            
        # 3. 如果没有Aion进程，不改变默认选择（即第一个）
        if selected_index != -1:
            self.process_combo.setCurrentIndex(selected_index)
    
    def auto_search_game(self):
        """自动搜索并连接到Aion游戏进程"""
        # 获取进程列表
        processes = self.memory.get_processes_list()
        
        # 查找第一个Aion进程
        aion_index = -1
        
        for i, proc in enumerate(processes):
            if proc['is_aion']:
                aion_index = i
                break
        
        # 如果找到Aion进程，连接到它
        if aion_index != -1:
            process_id = processes[aion_index]['pid']
            self.memory.open_process(process_id)
            self.connect_button.setText("断开连接")
            self.connect_button.clicked.disconnect()
            self.connect_button.clicked.connect(self.disconnect_from_game)
            self.statusBar().showMessage(f"已连接到进程 {process_id}")
            self.add_game_log("连接", f"已连接到进程 {process_id}")
            self.start_button.setEnabled(True)
        else:
            QMessageBox.warning(self, "错误", "未找到Aion游戏进程")
    
    def connect_to_game(self):
        """连接到选定的游戏进程"""
        # 获取选定的进程ID
        index = self.process_combo.currentIndex()
        if index < 0:
            QMessageBox.warning(self, "错误", "请先选择一个游戏进程")
            return
            
        process_id = self.process_combo.itemData(index)
        
        if not process_id:
            QMessageBox.warning(self, "错误", "无效的进程ID")
            return
            
        # 尝试连接
        if self.memory.open_process(process_id):
            # 导入游戏版本选择功能
            import sys
            sys.path.append('..')
            from main import update_offsets, game_versions
            
            # 显示游戏版本选择对话框
            version_dialog = QDialog(self)
            version_dialog.setWindowTitle("选择游戏版本")
            version_dialog.setMinimumWidth(300)
            
            version_layout = QVBoxLayout(version_dialog)
            version_label = QLabel("请选择游戏版本:")
            version_layout.addWidget(version_label)
            
            version_combo = QComboBox()
            for version in game_versions.keys():
                version_combo.addItem(version)
            version_layout.addWidget(version_combo)
            
            button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
            button_box.accepted.connect(version_dialog.accept)
            button_box.rejected.connect(version_dialog.reject)
            version_layout.addWidget(button_box)
            
            if version_dialog.exec() == QDialog.DialogCode.Accepted:
                selected_version = version_combo.currentText()
                update_offsets(selected_version)
                
                QMessageBox.information(self, "成功", f"已成功连接到进程 {process_id}，游戏版本: {selected_version}")
                
                # 启用开始按钮和功能控件
                self.start_button.setEnabled(True)
                self.enable_feature_controls(True)
                self.enable_ai_controls(True)
                
                # 更新UI
                self.connect_button.setText("断开连接")
                self.connect_button.clicked.disconnect()
                self.connect_button.clicked.connect(self.disconnect_from_game)
                
                # 更新状态栏
                self.statusBar().showMessage(f"已连接到进程 {process_id}，游戏版本: {selected_version}")
                
                # 添加日志
                self.add_game_log("连接", f"已连接到进程 {process_id}，游戏版本: {selected_version}")
                
                # 立即更新状态显示
                self.update_status_display()
            else:
                # 用户取消了版本选择，断开连接
                self.memory.close_process()
                QMessageBox.warning(self, "取消", "已取消连接")
        else:
            QMessageBox.critical(self, "错误", f"无法连接到进程 {process_id}")
    
    def disconnect_from_game(self):
        """断开与游戏的连接"""
        # 停止自动战斗
        if self.battle_manager.is_active:
            self.toggle_auto_battle()
            
        # 关闭进程
        self.memory.close_process()
        
        # 更新UI
        self.start_button.setEnabled(False)
        self.enable_feature_controls(False)
        self.connect_button.setText("连接到游戏")
        self.connect_button.clicked.disconnect()
        self.connect_button.clicked.connect(self.connect_to_game)
        
        # 更新状态栏
        self.statusBar().showMessage("已断开连接")
        
        # 添加日志
        self.add_game_log("断开", "已断开与游戏的连接")

    def toggle_auto_battle(self):
        """切换自动战斗状态"""
        if not self.battle_manager:
            return
            
        if self.battle_manager.is_active:
            # 停止自动战斗
            self.battle_manager.stop()
            self.start_button.setText("开始副本辅助")
            self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
            self.add_game_log("战斗", "已停止自动战斗")
        else:
            # 开始自动战斗
            self.battle_manager.start()
            self.start_button.setText("停止副本辅助")
            self.start_button.setStyleSheet("QPushButton { background-color: #F44336; color: white; }")
            self.add_game_log("战斗", "已开始自动战斗")
    
    def update_status_display(self):
        """更新状态显示"""
        # 简化状态更新，不再更新状态页面的内容
        pass
    
    def add_game_log(self, action_type, message):
        """添加游戏动作日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.game_action_text.append(f"[{timestamp}][{action_type}] {message}")
    
    def clear_game_log(self):
        """清空游戏动作日志区域"""
        self.game_action_text.clear()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止自动战斗
        if self.battle_manager.is_active:
            self.battle_manager.stop()
            
        # 断开与游戏的连接
        if self.memory.process_handle:
            self.memory.close_process()
            
        # 接受关闭事件
        event.accept()
        
    def enable_feature_controls(self, enabled):
        """启用或禁用功能控件"""
        # 基础功能
        self.attack_speed_check.setEnabled(enabled)
        self.attack_speed_value.setEnabled(enabled)
        self.move_speed_check.setEnabled(enabled)
        self.move_speed_value.setEnabled(enabled)
        
        # 高级功能
        self.attack_range_check.setEnabled(enabled)
        self.stealth_check.setEnabled(enabled)
        self.air_lock_check.setEnabled(enabled)
        self.fly_check.setEnabled(enabled)
        self.fly_value.setEnabled(enabled)
        self.fly_up_btn.setEnabled(enabled)
        self.fly_down_btn.setEnabled(enabled)
        self.view_range_check.setEnabled(enabled)
        self.view_range_value.setEnabled(enabled)
        self.show_hp_check.setEnabled(enabled)
        self.map_extend_check.setEnabled(enabled)
        self.map_extend_value.setEnabled(enabled)
        
        # 重置复选框状态
        if not enabled:
            self.attack_speed_check.setChecked(False)
            self.move_speed_check.setChecked(False)
            self.attack_range_check.setChecked(False)
            self.stealth_check.setChecked(False)
            self.air_lock_check.setChecked(False)
            self.fly_check.setChecked(False)
            self.view_range_check.setChecked(False)
            self.show_hp_check.setChecked(False)
            self.map_extend_check.setChecked(False)

    def enable_ai_controls(self, enabled):
        """启用或禁用AI控件"""
        # AI主控件
        self.ai_enabled_check.setEnabled(enabled)
        self.ocr_enabled_check.setEnabled(enabled)
        self.template_matching_check.setEnabled(enabled)
        self.auto_attack_check.setEnabled(enabled)
        self.auto_aoe_check.setEnabled(enabled)
        self.manual_target_auto_skill_check.setEnabled(enabled)

        # 如果禁用，重置所有AI相关状态
        if not enabled:
            self.ai_enabled_check.setChecked(False)
            self.smart_targeting_check.setChecked(False)
            self.adaptive_skills_check.setChecked(False)
            self.ocr_enabled_check.setChecked(False)
            self.template_matching_check.setChecked(False)
            self.auto_attack_check.setChecked(False)
            self.focus_kill_check.setChecked(False)
            self.auto_aoe_check.setChecked(False)
            self.manual_target_auto_skill_check.setChecked(False)

            # 禁用子控件
            self.smart_targeting_check.setEnabled(False)
            self.adaptive_skills_check.setEnabled(False)
            self.max_distance_spin.setEnabled(False)
            self.hp_threshold_spin.setEnabled(False)
            self.ocr_test_button.setEnabled(False)
            self.template_threshold_spin.setEnabled(False)
            self.attack_range_spin.setEnabled(False)
            self.scan_interval_spin.setEnabled(False)
            self.focus_kill_check.setEnabled(False)
            self.max_focus_time_spin.setEnabled(False)
            self.no_damage_timeout_spin.setEnabled(False)
            self.aoe_skills_edit.setEnabled(False)
            self.aoe_min_targets_spin.setEnabled(False)
            self.force_manual_target_button.setEnabled(False)
            self.clear_manual_target_button.setEnabled(False)

            # 清空状态显示
            self.ai_status_text.clear()
            self.ocr_result_label.setText("OCR结果将显示在这里")

    def toggle_attack_speed(self, checked):
        """切换技能加速状态"""
        import sys
        sys.path.append('..')
        from main import toggle_attack_speed, memory_helper
        
        try:
            # 使用内存读取器的进程句柄和模块基址
            memory_helper.process_handle = self.memory.process_handle
            memory_helper.process_id = self.memory.process_id
            memory_helper.module_base = self.memory.module_base_addr
            
            speed_value = int(self.attack_speed_value.text())
            result = toggle_attack_speed(memory_helper, checked, speed_value)
            
            if result:
                if checked:
                    self.add_game_log("技能加速", f"已启用技能加速，值: {speed_value}")
                    self.statusBar().showMessage(f"技能加速已启动: {speed_value}")
                else:
                    self.add_game_log("技能加速", "已关闭技能加速")
                    self.statusBar().showMessage("技能加速已关闭")
            else:
                self.attack_speed_check.setChecked(False)
                QMessageBox.warning(self, "错误", "启用技能加速失败")
        except ValueError:
            self.attack_speed_check.setChecked(False)
            QMessageBox.warning(self, "输入错误", "请输入有效的速度值")
        except Exception as e:
            self.attack_speed_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"技能加速失败: {str(e)}")
    
    def toggle_move_speed(self, checked):
        """切换移动加速状态"""
        import sys
        sys.path.append('..')
        from main import toggle_move_speed, memory_helper
        
        try:
            # 使用内存读取器的进程句柄和模块基址
            memory_helper.process_handle = self.memory.process_handle
            memory_helper.process_id = self.memory.process_id
            memory_helper.module_base = self.memory.module_base_addr
            
            speed_value = float(self.move_speed_value.text())
            result = toggle_move_speed(memory_helper, checked, speed_value)
            
            if result:
                if checked:
                    self.add_game_log("移动加速", f"已启用移动加速，值: {speed_value}")
                    self.statusBar().showMessage(f"移动加速已启动: {speed_value}")
                else:
                    self.add_game_log("移动加速", "已关闭移动加速")
                    self.statusBar().showMessage("移动加速已关闭")
            else:
                self.move_speed_check.setChecked(False)
                QMessageBox.warning(self, "错误", "启用移动加速失败")
        except ValueError:
            self.move_speed_check.setChecked(False)
            QMessageBox.warning(self, "输入错误", "请输入有效的速度值")
        except Exception as e:
            self.move_speed_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"移动加速失败: {str(e)}")

    def toggle_air_lock(self, checked):
        """切换锁空状态"""
        import sys
        sys.path.append('..')
        from main import toggle_air_lock, memory_helper
        
        try:
            # 使用内存读取器的进程句柄和模块基址
            memory_helper.process_handle = self.memory.process_handle
            memory_helper.process_id = self.memory.process_id
            memory_helper.module_base = self.memory.module_base_addr
            
            result = toggle_air_lock(memory_helper, checked)
            
            if result:
                if checked:
                    self.add_game_log("锁空", "锁空功能已启用")
                    self.statusBar().showMessage("锁空功能已启用，按Insert锁定，Delete解除")
                else:
                    self.add_game_log("锁空", "锁空功能已关闭")
                    self.statusBar().showMessage("锁空功能已关闭")
            else:
                self.air_lock_check.setChecked(False)
                QMessageBox.warning(self, "错误", "启用锁空功能失败")
        except Exception as e:
            self.air_lock_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"锁空功能失败: {str(e)}")
    
    def toggle_fly(self, checked):
        """切换飞天状态"""
        import sys
        sys.path.append('..')
        from main import toggle_fly, memory_helper
        
        try:
            # 使用内存读取器的进程句柄和模块基址
            memory_helper.process_handle = self.memory.process_handle
            memory_helper.process_id = self.memory.process_id
            memory_helper.module_base = self.memory.module_base_addr
            
            # 启用飞天功能
            if checked:
                self.add_game_log("飞天", "飞天功能已启用")
                self.statusBar().showMessage("飞天功能已启用，使用PgUp上升，PgDown下降")
                self.fly_up_btn.setEnabled(True)
                self.fly_down_btn.setEnabled(True)
            else:
                self.add_game_log("飞天", "飞天功能已关闭")
                self.statusBar().showMessage("飞天功能已关闭")
                self.fly_up_btn.setEnabled(False)
                self.fly_down_btn.setEnabled(False)
        except Exception as e:
            self.fly_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"飞天功能失败: {str(e)}")
    
    def fly_up(self):
        """上升"""
        import sys
        sys.path.append('..')
        from main import toggle_fly, memory_helper
        
        try:
            # 使用内存读取器的进程句柄和模块基址
            memory_helper.process_handle = self.memory.process_handle
            memory_helper.process_id = self.memory.process_id
            memory_helper.module_base = self.memory.module_base_addr
            
            height_value = float(self.fly_value.text())
            result = toggle_fly(memory_helper, True, height_value, "up")
            
            if result:
                self.statusBar().showMessage(f"上升: {height_value}米")
            else:
                QMessageBox.warning(self, "错误", "上升失败")
        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的高度值")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"上升失败: {str(e)}")
    
    def fly_down(self):
        """下降"""
        import sys
        sys.path.append('..')
        from main import toggle_fly, memory_helper
        
        try:
            # 使用内存读取器的进程句柄和模块基址
            memory_helper.process_handle = self.memory.process_handle
            memory_helper.process_id = self.memory.process_id
            memory_helper.module_base = self.memory.module_base_addr
            
            height_value = float(self.fly_value.text())
            result = toggle_fly(memory_helper, True, height_value, "down")
            
            if result:
                self.statusBar().showMessage(f"下降: {height_value}米")
            else:
                QMessageBox.warning(self, "错误", "下降失败")
        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的高度值")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"下降失败: {str(e)}")
    
    def toggle_stealth(self, checked):
        """切换反隐状态"""
        import sys
        sys.path.append('..')
        from main import toggle_stealth, memory_helper
        
        try:
            # 使用内存读取器的进程句柄和模块基址
            memory_helper.process_handle = self.memory.process_handle
            memory_helper.process_id = self.memory.process_id
            memory_helper.module_base = self.memory.module_base_addr
            
            result = toggle_stealth(memory_helper, checked)
            
            if result:
                if checked:
                    self.add_game_log("反隐", "反隐功能已启用")
                    self.statusBar().showMessage("反隐功能已启用")
                else:
                    self.add_game_log("反隐", "反隐功能已关闭")
                    self.statusBar().showMessage("反隐功能已关闭")
            else:
                self.stealth_check.setChecked(False)
                QMessageBox.warning(self, "错误", "启用反隐功能失败")
        except Exception as e:
            self.stealth_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"反隐功能失败: {str(e)}")
    
    def toggle_view_range(self, checked):
        """切换视野扩展状态"""
        import sys
        sys.path.append('..')
        from main import toggle_view_range, memory_helper
        
        try:
            # 使用内存读取器的进程句柄和模块基址
            memory_helper.process_handle = self.memory.process_handle
            memory_helper.process_id = self.memory.process_id
            memory_helper.module_base = self.memory.module_base_addr
            
            view_value = float(self.view_range_value.text())
            result = toggle_view_range(memory_helper, checked, view_value)
            
            if result:
                if checked:
                    self.add_game_log("视野扩展", f"已启用视野扩展，值: {view_value}")
                    self.statusBar().showMessage(f"视野扩展已启用: {view_value}")
                else:
                    self.add_game_log("视野扩展", "已关闭视野扩展")
                    self.statusBar().showMessage("视野扩展已关闭")
            else:
                self.view_range_check.setChecked(False)
                QMessageBox.warning(self, "错误", "启用视野扩展失败")
        except ValueError:
            self.view_range_check.setChecked(False)
            QMessageBox.warning(self, "输入错误", "请输入有效的视野值")
        except Exception as e:
            self.view_range_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"视野扩展失败: {str(e)}")
    
    def toggle_show_hp(self, checked):
        """切换显血状态"""
        import sys
        sys.path.append('..')
        from main import toggle_show_hp, memory_helper
        
        try:
            # 使用内存读取器的进程句柄和模块基址
            memory_helper.process_handle = self.memory.process_handle
            memory_helper.process_id = self.memory.process_id
            memory_helper.module_base = self.memory.module_base_addr
            
            result = toggle_show_hp(memory_helper, checked)
            
            if result:
                if checked:
                    self.add_game_log("显血", "显血功能已启用")
                    self.statusBar().showMessage("显血功能已启用")
                else:
                    self.add_game_log("显血", "显血功能已关闭")
                    self.statusBar().showMessage("显血功能已关闭")
            else:
                self.show_hp_check.setChecked(False)
                QMessageBox.warning(self, "错误", "启用显血功能失败")
        except Exception as e:
            self.show_hp_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"显血功能失败: {str(e)}")
    
    def toggle_map_extend(self, checked):
        """切换地图扩展状态"""
        import sys
        sys.path.append('..')
        from main import toggle_map_extend, memory_helper
        
        try:
            # 使用内存读取器的进程句柄和模块基址
            memory_helper.process_handle = self.memory.process_handle
            memory_helper.process_id = self.memory.process_id
            memory_helper.module_base = self.memory.module_base_addr
            
            map_value = float(self.map_extend_value.text())
            result = toggle_map_extend(memory_helper, checked, map_value)
            
            if result:
                if checked:
                    self.add_game_log("地图扩展", f"已启用地图扩展，值: {map_value}")
                    self.statusBar().showMessage(f"地图扩展已启用: {map_value}")
                else:
                    self.add_game_log("地图扩展", "已关闭地图扩展")
                    self.statusBar().showMessage("地图扩展已关闭")
            else:
                self.map_extend_check.setChecked(False)
                QMessageBox.warning(self, "错误", "启用地图扩展失败")
        except ValueError:
            self.map_extend_check.setChecked(False)
            QMessageBox.warning(self, "输入错误", "请输入有效的地图范围值")
        except Exception as e:
            self.map_extend_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"地图扩展失败: {str(e)}")
    
    def toggle_attack_range(self, checked):
        """切换攻击距离状态"""
        import sys
        sys.path.append('..')
        from main import toggle_attack_range, memory_helper
        
        try:
            # 使用内存读取器的进程句柄和模块基址
            memory_helper.process_handle = self.memory.process_handle
            memory_helper.process_id = self.memory.process_id
            memory_helper.module_base = self.memory.module_base_addr
            
            range_value = float(self.attack_range_value.text())
            result = toggle_attack_range(memory_helper, checked, range_value)
            
            if result:
                if checked:
                    self.add_game_log("攻击距离", f"已启用攻击距离修改，值: {range_value}")
                    self.statusBar().showMessage(f"攻击距离已增加: {range_value}")
                else:
                    self.add_game_log("攻击距离", "已关闭攻击距离修改")
                    self.statusBar().showMessage("攻击距离修改已关闭")
            else:
                self.attack_range_check.setChecked(False)
                QMessageBox.warning(self, "错误", "启用攻击距离修改失败")
        except ValueError:
            self.attack_range_check.setChecked(False)
            QMessageBox.warning(self, "输入错误", "请输入有效的距离值")
        except Exception as e:
            self.attack_range_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"攻击距离修改失败: {str(e)}")

    def toggle_ai_mode(self, checked):
        """切换AI增强模式"""
        try:
            if self.battle_manager:
                self.battle_manager.toggle_ai_mode(checked)
                self.ai_enabled = checked

                # 启用/禁用相关控件
                self.smart_targeting_check.setEnabled(checked)
                self.adaptive_skills_check.setEnabled(checked)
                self.max_distance_spin.setEnabled(checked)
                self.hp_threshold_spin.setEnabled(checked)

                if checked:
                    self.add_game_log("AI", "AI增强模式已启用")
                    self.statusBar().showMessage("AI增强模式已启用")
                else:
                    self.add_game_log("AI", "AI增强模式已禁用")
                    self.statusBar().showMessage("AI增强模式已禁用")

                # 更新AI状态显示
                self.update_ai_status()
            else:
                self.ai_enabled_check.setChecked(False)
                QMessageBox.warning(self, "错误", "战斗管理器未初始化")

        except Exception as e:
            self.ai_enabled_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"切换AI模式失败: {str(e)}")

    def toggle_smart_targeting(self, checked):
        """切换智能目标选择"""
        try:
            if self.battle_manager:
                self.battle_manager.toggle_smart_targeting(checked)
                self.smart_targeting = checked

                if checked:
                    self.add_game_log("AI", "智能目标选择已启用")
                    self.statusBar().showMessage("智能目标选择已启用")
                else:
                    self.add_game_log("AI", "智能目标选择已禁用")
                    self.statusBar().showMessage("智能目标选择已禁用")

                # 更新AI参数
                self.update_ai_parameters()
                self.update_ai_status()
            else:
                self.smart_targeting_check.setChecked(False)
                QMessageBox.warning(self, "错误", "战斗管理器未初始化")

        except Exception as e:
            self.smart_targeting_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"切换智能目标选择失败: {str(e)}")

    def toggle_adaptive_skills(self, checked):
        """切换自适应技能使用"""
        try:
            if self.battle_manager:
                self.battle_manager.toggle_adaptive_skills(checked)
                self.adaptive_skills = checked

                if checked:
                    self.add_game_log("AI", "自适应技能使用已启用")
                    self.statusBar().showMessage("自适应技能使用已启用")
                else:
                    self.add_game_log("AI", "自适应技能使用已禁用")
                    self.statusBar().showMessage("自适应技能使用已禁用")

                self.update_ai_status()
            else:
                self.adaptive_skills_check.setChecked(False)
                QMessageBox.warning(self, "错误", "战斗管理器未初始化")

        except Exception as e:
            self.adaptive_skills_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"切换自适应技能使用失败: {str(e)}")

    def toggle_ocr(self, checked):
        """切换OCR文字识别"""
        try:
            if self.battle_manager and hasattr(self.battle_manager, 'screen'):
                self.battle_manager.screen.ocr_enabled = checked
                self.ocr_test_button.setEnabled(checked)

                if checked:
                    self.add_game_log("OCR", "OCR文字识别已启用")
                    self.statusBar().showMessage("OCR文字识别已启用")
                else:
                    self.add_game_log("OCR", "OCR文字识别已禁用")
                    self.statusBar().showMessage("OCR文字识别已禁用")
            else:
                self.ocr_enabled_check.setChecked(False)
                QMessageBox.warning(self, "错误", "屏幕检测器未初始化")

        except Exception as e:
            self.ocr_enabled_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"切换OCR功能失败: {str(e)}")

    def toggle_template_matching(self, checked):
        """切换高级模板匹配"""
        try:
            if self.battle_manager and hasattr(self.battle_manager, 'screen'):
                self.battle_manager.screen.template_matching_enabled = checked
                self.template_threshold_spin.setEnabled(checked)

                if checked:
                    threshold = self.template_threshold_spin.value() / 100.0
                    self.battle_manager.screen.template_threshold = threshold
                    self.add_game_log("模板匹配", f"高级模板匹配已启用，阈值: {threshold:.2f}")
                    self.statusBar().showMessage("高级模板匹配已启用")
                else:
                    self.add_game_log("模板匹配", "高级模板匹配已禁用")
                    self.statusBar().showMessage("高级模板匹配已禁用")
            else:
                self.template_matching_check.setChecked(False)
                QMessageBox.warning(self, "错误", "屏幕检测器未初始化")

        except Exception as e:
            self.template_matching_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"切换模板匹配失败: {str(e)}")

    def test_ocr(self):
        """测试OCR功能"""
        try:
            if self.battle_manager and hasattr(self.battle_manager, 'screen'):
                # 测试OCR识别
                text = self.battle_manager.screen.ocr_text_recognition()

                if text:
                    self.ocr_result_label.setText(f"识别结果: {text}")
                    self.add_game_log("OCR", f"识别到文字: {text}")
                else:
                    self.ocr_result_label.setText("未识别到文字")
                    self.add_game_log("OCR", "未识别到文字")
            else:
                QMessageBox.warning(self, "错误", "屏幕检测器未初始化")

        except Exception as e:
            self.ocr_result_label.setText(f"OCR测试失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"OCR测试失败: {str(e)}")

    def update_ai_parameters(self):
        """更新AI参数"""
        try:
            if self.battle_manager and hasattr(self.battle_manager, 'set_ai_parameters'):
                max_distance = self.max_distance_spin.value()
                hp_threshold = self.hp_threshold_spin.value() / 100.0

                self.battle_manager.set_ai_parameters(
                    max_distance=max_distance,
                    hp_threshold=hp_threshold
                )

                self.add_game_log("AI", f"AI参数已更新: 最大距离={max_distance}, 血量阈值={hp_threshold:.2f}")

        except Exception as e:
            logger.error(f"更新AI参数失败: {str(e)}")

    def update_ai_status(self):
        """更新AI状态显示"""
        try:
            if self.battle_manager and hasattr(self.battle_manager, 'get_ai_status'):
                status = self.battle_manager.get_ai_status()

                status_text = f"""AI状态: {'启用' if status.get('ai_enabled', False) else '禁用'}
智能目标选择: {'启用' if status.get('smart_targeting', False) else '禁用'}
自适应技能: {'启用' if status.get('adaptive_skills', False) else '禁用'}
当前状态: {status.get('current_state', 'unknown')}
当前目标ID: {status.get('current_target_id', 'None')}
总行动次数: {status.get('combat_statistics', {}).get('total_actions', 0)}
成功次数: {status.get('combat_statistics', {}).get('successful_actions', 0)}"""

                self.ai_status_text.setText(status_text)

        except Exception as e:
            logger.error(f"更新AI状态显示失败: {str(e)}")

    def toggle_auto_attack(self, checked):
        """切换自动攻击功能"""
        try:
            if self.battle_manager:
                self.battle_manager.toggle_auto_attack(checked)
                self.attack_range_spin.setEnabled(checked)
                self.scan_interval_spin.setEnabled(checked)

                if checked:
                    # 应用当前设置
                    self.update_attack_range()
                    self.update_scan_interval()
                    self.add_game_log("自动攻击", "自动攻击周围怪物已启用")
                    self.statusBar().showMessage("自动攻击已启用")
                else:
                    self.add_game_log("自动攻击", "自动攻击周围怪物已禁用")
                    self.statusBar().showMessage("自动攻击已禁用")

                self.update_ai_status()
            else:
                self.auto_attack_check.setChecked(False)
                QMessageBox.warning(self, "错误", "战斗管理器未初始化")

        except Exception as e:
            self.auto_attack_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"切换自动攻击失败: {str(e)}")

    def update_attack_range(self):
        """更新攻击范围"""
        try:
            if self.battle_manager:
                range_value = self.attack_range_spin.value()
                self.battle_manager.set_auto_attack_range(range_value)
                self.add_game_log("自动攻击", f"攻击范围设置为: {range_value}米")

        except Exception as e:
            logger.error(f"更新攻击范围失败: {str(e)}")

    def update_scan_interval(self):
        """更新扫描间隔"""
        try:
            if self.battle_manager:
                interval_ms = self.scan_interval_spin.value()
                interval_s = interval_ms / 1000.0
                self.battle_manager.auto_target_scan_interval = interval_s
                self.add_game_log("自动攻击", f"扫描间隔设置为: {interval_ms}毫秒")

        except Exception as e:
            logger.error(f"更新扫描间隔失败: {str(e)}")

    def toggle_focus_kill_mode(self, checked):
        """切换专注击杀模式"""
        try:
            if self.battle_manager:
                # 使用战斗管理器的方法来切换模式
                self.battle_manager.toggle_focus_kill_mode(checked)
                self.max_focus_time_spin.setEnabled(checked)
                self.no_damage_timeout_spin.setEnabled(checked)

                if checked:
                    # 应用当前设置
                    self.update_focus_parameters()
                    self.add_game_log("专注击杀", "专注击杀模式已启用 - 将专注击杀当前目标")
                    self.statusBar().showMessage("专注击杀模式已启用")
                else:
                    self.add_game_log("专注击杀", "专注击杀模式已禁用 - 将频繁切换目标")
                    self.statusBar().showMessage("专注击杀模式已禁用")

                self.update_ai_status()
            else:
                self.focus_kill_check.setChecked(False)
                QMessageBox.warning(self, "错误", "战斗管理器未初始化")

        except Exception as e:
            self.focus_kill_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"切换专注击杀模式失败: {str(e)}")

    def update_focus_parameters(self):
        """更新专注击杀参数"""
        try:
            if self.battle_manager:
                max_focus_time = self.max_focus_time_spin.value()
                no_damage_timeout = self.no_damage_timeout_spin.value()

                self.battle_manager.max_focus_duration = max_focus_time
                self.battle_manager.no_damage_timeout = no_damage_timeout

                self.add_game_log("专注击杀", f"参数已更新: 最大专注时间={max_focus_time}秒, 无伤害超时={no_damage_timeout}秒")

        except Exception as e:
            logger.error(f"更新专注击杀参数失败: {str(e)}")

    def toggle_auto_aoe(self, checked):
        """切换自动范围打怪功能"""
        try:
            if self.battle_manager:
                self.battle_manager.toggle_auto_aoe(checked)
                self.aoe_skills_edit.setEnabled(checked)
                self.aoe_min_targets_spin.setEnabled(checked)

                if checked:
                    # 应用当前设置
                    self.update_aoe_skills()
                    self.update_aoe_min_targets()
                    self.add_game_log("范围攻击", "自动范围打怪已启用")
                    self.statusBar().showMessage("自动范围打怪已启用")
                else:
                    self.add_game_log("范围攻击", "自动范围打怪已禁用")
                    self.statusBar().showMessage("自动范围打怪已禁用")

                self.update_ai_status()
            else:
                self.auto_aoe_check.setChecked(False)
                QMessageBox.warning(self, "错误", "战斗管理器未初始化")

        except Exception as e:
            self.auto_aoe_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"切换自动范围攻击失败: {str(e)}")

    def update_aoe_skills(self):
        """更新范围技能设置"""
        try:
            if self.battle_manager:
                skills_text = self.aoe_skills_edit.text().strip()
                self.battle_manager.set_aoe_skills(skills_text)
                self.add_game_log("范围攻击", f"范围技能已设置: {skills_text}")

        except Exception as e:
            logger.error(f"更新范围技能失败: {str(e)}")

    def update_aoe_min_targets(self):
        """更新最少目标数设置"""
        try:
            if self.battle_manager:
                min_targets = self.aoe_min_targets_spin.value()
                self.battle_manager.set_aoe_min_targets(min_targets)
                self.add_game_log("范围攻击", f"最少目标数设置为: {min_targets}")

        except Exception as e:
            logger.error(f"更新最少目标数失败: {str(e)}")

    def toggle_manual_target_auto_skill(self, checked):
        """切换手动点怪自动技能功能"""
        try:
            if self.battle_manager:
                self.battle_manager.toggle_manual_target_auto_skill(checked)
                self.force_manual_target_button.setEnabled(checked)
                self.clear_manual_target_button.setEnabled(checked)

                if checked:
                    self.add_game_log("手动目标", "手动点怪自动技能已启用")
                    self.statusBar().showMessage("手动点怪自动技能已启用")
                else:
                    self.add_game_log("手动目标", "手动点怪自动技能已禁用")
                    self.statusBar().showMessage("手动点怪自动技能已禁用")

                self.update_ai_status()
            else:
                self.manual_target_auto_skill_check.setChecked(False)
                QMessageBox.warning(self, "错误", "战斗管理器未初始化")

        except Exception as e:
            self.manual_target_auto_skill_check.setChecked(False)
            QMessageBox.warning(self, "错误", f"切换手动目标自动技能失败: {str(e)}")

    def force_manual_target(self):
        """强制设置手动目标"""
        try:
            if self.battle_manager:
                self.battle_manager.force_manual_target()
                self.add_game_log("手动目标", "已设置当前目标为手动目标")
                self.statusBar().showMessage("已设置手动目标")
            else:
                QMessageBox.warning(self, "错误", "战斗管理器未初始化")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"设置手动目标失败: {str(e)}")

    def clear_manual_target(self):
        """清除手动目标"""
        try:
            if self.battle_manager:
                self.battle_manager.manual_target_detected = False
                self.battle_manager.manual_target_id = 0
                self.add_game_log("手动目标", "已清除手动目标")
                self.statusBar().showMessage("已清除手动目标")
            else:
                QMessageBox.warning(self, "错误", "战斗管理器未初始化")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"清除手动目标失败: {str(e)}")