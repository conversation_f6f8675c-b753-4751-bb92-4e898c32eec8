#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
输入模拟模块 - 负责模拟键盘和鼠标输入
"""

import time
import random
import logging
import keyboard
import pyautogui
import win32api
import win32con
import numpy as np
from threading import Lock

# 设置日志
logger = logging.getLogger('aion_input_simulator')

# 防止PyAutoGUI失控
pyautogui.PAUSE = 0.1  # 每次操作之间的短暂暂停
pyautogui.FAILSAFE = True  # 将鼠标移动到屏幕左上角可以中断操作

class InputSimulator:
    """输入模拟器，模拟键盘和鼠标输入"""
    
    def __init__(self):
        """初始化输入模拟器"""
        self.input_lock = Lock()  # 防止并发输入冲突
        
        # 配置
        self.key_press_duration = 0.05  # 按键持续时间(秒)
        self.click_duration = 0.05  # 点击持续时间(秒)
        self.human_factor = True  # 是否添加人类因素(随机延迟和偏移)
        
        # 屏幕区域配置
        self.screen_width, self.screen_height = pyautogui.size()
        self.game_region = (0, 0, self.screen_width, self.screen_height)  # (x, y, width, height)
        self.minimap_region = (self.screen_width - 200, 0, 200, 200)  # 右上角小地图
        self.target_frame_region = (self.screen_width // 2 - 100, 100, 200, 100)  # 目标框架区域
        
        logger.info("输入模拟器已初始化")
    
    def press_key(self, key):
        """
        模拟按下并释放键盘按键
        
        参数:
            key: 要按下的键 (例如: '1', '2', 'f', 等)
        """
        try:
            with self.input_lock:
                if self.human_factor:
                    # 添加人类操作的随机延迟
                    time.sleep(random.uniform(0.01, 0.05))
                    
                # 按下并释放按键
                logger.debug(f"按下按键: {key}")
                keyboard.press(key)
                time.sleep(self.key_press_duration)
                keyboard.release(key)
                
                # 再添加一点随机延迟
                if self.human_factor:
                    time.sleep(random.uniform(0.01, 0.05))
                    
                return True
        except Exception as e:
            logger.error(f"按键操作失败: {key}, 错误: {str(e)}")
            return False
    
    def press_key_combo(self, keys):
        """
        模拟按下组合键
        
        参数:
            keys: 要同时按下的键列表 (例如: ['ctrl', 'c'])
        """
        try:
            with self.input_lock:
                if self.human_factor:
                    time.sleep(random.uniform(0.01, 0.05))
                
                # 按下所有按键
                for key in keys:
                    keyboard.press(key)
                
                time.sleep(self.key_press_duration)
                
                # 释放所有按键（按相反顺序）
                for key in reversed(keys):
                    keyboard.release(key)
                
                if self.human_factor:
                    time.sleep(random.uniform(0.01, 0.05))
                
                return True
        except Exception as e:
            logger.error(f"组合键操作失败: {keys}, 错误: {str(e)}")
            
            # 确保所有键都被释放
            for key in keys:
                try:
                    keyboard.release(key)
                except:
                    pass
                    
            return False
    
    def click(self, x=None, y=None, button='left'):
        """
        模拟鼠标点击
        
        参数:
            x, y: 点击坐标，如果为None则使用当前鼠标位置
            button: 'left', 'right', 或 'middle'
        """
        try:
            with self.input_lock:
                # 如果坐标未指定，使用当前位置
                if x is None or y is None:
                    current_pos = win32api.GetCursorPos()
                    x = x if x is not None else current_pos[0]
                    y = y if y is not None else current_pos[1]
                
                # 添加人类因素
                if self.human_factor:
                    # 添加微小的随机偏移
                    x += random.uniform(-2, 2)
                    y += random.uniform(-2, 2)
                    x = max(0, min(self.screen_width - 1, x))
                    y = max(0, min(self.screen_height - 1, y))
                
                # 移动鼠标并点击
                win32api.SetCursorPos((int(x), int(y)))
                
                # 确定按键代码
                if button == 'left':
                    down_event = win32con.MOUSEEVENTF_LEFTDOWN
                    up_event = win32con.MOUSEEVENTF_LEFTUP
                elif button == 'right':
                    down_event = win32con.MOUSEEVENTF_RIGHTDOWN
                    up_event = win32con.MOUSEEVENTF_RIGHTUP
                elif button == 'middle':
                    down_event = win32con.MOUSEEVENTF_MIDDLEDOWN
                    up_event = win32con.MOUSEEVENTF_MIDDLEUP
                else:
                    raise ValueError(f"不支持的按钮类型: {button}")
                
                # 模拟鼠标按下和释放
                win32api.mouse_event(down_event, 0, 0, 0, 0)
                time.sleep(self.click_duration)
                win32api.mouse_event(up_event, 0, 0, 0, 0)
                
                logger.debug(f"点击 {button} 按钮，位置: ({int(x)}, {int(y)})")
                return True
        except Exception as e:
            logger.error(f"鼠标点击操作失败，位置: ({x}, {y})，错误: {str(e)}")
            return False
    
    def move_mouse(self, x, y, duration=0.2):
        """
        平滑移动鼠标到指定位置
        
        参数:
            x, y: 目标坐标
            duration: 移动持续时间(秒)
        """
        try:
            with self.input_lock:
                # 获取当前位置
                current_pos = win32api.GetCursorPos()
                start_x, start_y = current_pos
                
                # 添加人类因素
                if self.human_factor:
                    # 添加略微的随机偏移
                    target_x = max(0, min(self.screen_width - 1, x + random.uniform(-2, 2)))
                    target_y = max(0, min(self.screen_height - 1, y + random.uniform(-2, 2)))
                else:
                    target_x, target_y = x, y
                
                # 计算步骤数
                steps = max(int(duration / 0.01), 10)  # 至少10步
                
                # 使用缓动函数使移动更加自然
                for step in range(1, steps + 1):
                    t = step / steps
                    # 缓动函数: 慢启动，快中间，慢结束
                    ease = self._ease_in_out_quad(t)
                    
                    # 计算当前步骤的位置
                    current_x = start_x + (target_x - start_x) * ease
                    current_y = start_y + (target_y - start_y) * ease
                    
                    # 移动鼠标
                    win32api.SetCursorPos((int(current_x), int(current_y)))
                    time.sleep(duration / steps)
                
                logger.debug(f"移动鼠标到位置: ({int(target_x)}, {int(target_y)})")
                return True
        except Exception as e:
            logger.error(f"鼠标移动操作失败，目标: ({x}, {y})，错误: {str(e)}")
            return False
    
    def _ease_in_out_quad(self, t):
        """缓动函数，使移动更加自然"""
        if t < 0.5:
            return 2 * t * t
        else:
            return 1 - ((-2 * t + 2) ** 2) / 2
    
    def select_target(self):
        """选择目标 (点击目标框架)"""
        try:
            # 我们之前的方法是点击目标框架区域，但这不能确保选中特定目标
            # 作为替代方案，我们现在使用Tab键循环选择目标，这在大多数MMO中都可用
            logger.info("使用Tab键选择目标")
            self.press_key('tab')
            time.sleep(0.1)
            
            # 也可以模拟额外的快捷键，如F1-F12选择附近的目标
            # 有些玩家可能使用宏或根据游戏机制设置了其他目标选择方式
            
            # 作为备选方案，也可以尝试点击目标框架区域
            logger.debug("也尝试点击目标框架区域")
            x = self.target_frame_region[0] + self.target_frame_region[2] // 2
            y = self.target_frame_region[1] + self.target_frame_region[3] // 2
            
            # 移动鼠标并点击
            self.move_mouse(x, y)
            self.click()
            return True
        except Exception as e:
            logger.error(f"选择目标失败: {str(e)}")
            return False
    
    def target_next_enemy(self):
        """选择下一个敌人
        
        使用Tab键选择下一个敌人
        """
        try:
            with self.input_lock:
                # 不再使用Tab键选择敌人
                logger.info("Tab键功能已禁用")
                return False
        except Exception as e:
            logger.error(f"选择下一个敌人失败: {str(e)}")
            return False
    
    def target_nearest_enemy(self):
        """选择最近的敌人
        
        使用Tab键多次选择，找到最近的敌人
        """
        try:
            with self.input_lock:
                # 不再使用Tab键选择敌人
                logger.info("Tab键功能已禁用")
                return False
        except Exception as e:
            logger.error(f"选择最近敌人失败: {str(e)}")
            return False
    
    def click_at_game_coordinates(self, game_x, game_y, game_z=None):
        """
        点击游戏世界特定坐标（如果游戏支持的话）
        
        这需要根据特定游戏进行调整，因为游戏世界坐标到屏幕坐标的转换因游戏而异
        这里提供一个简化实现，仅作示例
        """
        try:
            # 这部分代码需要根据特定游戏调整
            # 通常需要获取游戏摄像机位置、旋转角度等信息
            
            # 将游戏世界坐标转换为屏幕坐标（简化）
            # 在实际应用中，这会更复杂，涉及3D到2D的投影
            screen_width, screen_height = self.screen_width, self.screen_height
            
            # 简单估算的屏幕中心
            center_x = screen_width // 2
            center_y = screen_height // 2
            
            # 简单偏移（仅作演示）
            screen_x = center_x + int(game_x * 10)  # 将游戏坐标缩放到合适的屏幕范围
            screen_y = center_y + int(game_y * 10)
            
            # 确保坐标在屏幕范围内
            screen_x = max(0, min(screen_x, screen_width))
            screen_y = max(0, min(screen_y, screen_height))
            
            # 移动鼠标并点击
            logger.debug(f"点击游戏坐标 ({game_x}, {game_y}) -> 屏幕坐标 ({screen_x}, {screen_y})")
            self.move_mouse(screen_x, screen_y)
            self.click()
            
            return True
        except Exception as e:
            logger.error(f"点击游戏坐标失败: {str(e)}")
            return False
    
    def turn_character(self, direction, amount=0.5):
        """
        转动角色朝向
        
        参数:
            direction: 'left', 'right', 'up', 或 'down'
            amount: 转动程度 (0.0 到 1.0)
        """
        try:
            # 验证参数
            if direction not in ['left', 'right', 'up', 'down']:
                raise ValueError(f"不支持的方向: {direction}")
            
            amount = max(0.1, min(1.0, amount))  # 限制在0.1到1.0之间
            
            # 计算按键持续时间
            duration = 0.1 + amount * 0.3  # 0.1到0.4秒
            
            # 确定按键
            key = ''
            if direction == 'left':
                key = 'a'
            elif direction == 'right':
                key = 'd'
            elif direction == 'up':
                key = 'w'
            elif direction == 'down':
                key = 's'
            
            # 按下按键
            logger.debug(f"转向 {direction}，程度: {amount}")
            keyboard.press(key)
            time.sleep(duration)
            keyboard.release(key)
            
            return True
        except Exception as e:
            logger.error(f"角色转向失败: {str(e)}")
            
            # 确保按键被释放
            for key in ['a', 'd', 'w', 's']:
                try:
                    keyboard.release(key)
                except:
                    pass
                    
            return False
    
    def set_game_region(self, x, y, width, height):
        """设置游戏窗口区域"""
        self.game_region = (x, y, width, height)
        logger.info(f"设置游戏窗口区域: {self.game_region}")
    
    def set_minimap_region(self, x, y, width, height):
        """设置小地图区域"""
        self.minimap_region = (x, y, width, height)
        logger.info(f"设置小地图区域: {self.minimap_region}")
    
    def set_target_frame_region(self, x, y, width, height):
        """设置目标框架区域"""
        self.target_frame_region = (x, y, width, height)
        logger.info(f"设置目标框架区域: {self.target_frame_region}")
    
    def set_human_factor(self, enabled):
        """启用或禁用人类操作因素"""
        self.human_factor = bool(enabled)
        logger.info(f"{'启用' if self.human_factor else '禁用'}人类操作因素")
        
    def type_text(self, text):
        """
        输入文本
        
        参数:
            text: 要输入的文本
        """
        try:
            with self.input_lock:
                for char in text:
                    if self.human_factor:
                        time.sleep(random.uniform(0.05, 0.15))  # 人类打字速度
                    keyboard.write(char)
                
                logger.debug(f"输入文本: {text}")
                return True
        except Exception as e:
            logger.error(f"文本输入失败: {str(e)}")
            return False