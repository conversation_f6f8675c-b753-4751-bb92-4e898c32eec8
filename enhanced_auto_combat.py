#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强版自动战斗系统 - 高速技能释放 + Alt组合键支持
"""

import sys
import os
import time
import logging
import threading
from collections import deque

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('enhanced_combat.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('enhanced_auto_combat')

class EnhancedAutoCombat:
    """增强版自动战斗系统"""
    
    def __init__(self, input_simulator):
        self.input = input_simulator
        self.is_active = False
        self.combat_thread = None
        self.logger = logging.getLogger('enhanced_auto_combat')
        
        # 技能配置 - 分组管理
        self.basic_skills = ['1', '2', '3', '4', '5', '6', '7', '8', '9']
        self.alt_skills = ['a+1', 'a+2', 'a+3', 'a+4', 'a+5', 'a+6', 'a+7', 'a+8', 'a+9', 'a+0']
        self.all_skills = self.basic_skills + self.alt_skills
        
        # 技能队列系统
        self.skill_queue = deque()
        self.current_skill_index = 0
        
        # 战斗模式配置
        self.combat_modes = {
            'fast': {'skill_interval': 0.15, 'target_interval': 3.0},    # 快速模式
            'normal': {'skill_interval': 0.3, 'target_interval': 2.0},   # 正常模式
            'slow': {'skill_interval': 0.5, 'target_interval': 1.5}      # 慢速模式
        }
        self.current_mode = 'fast'
        
        # 当前配置
        self.skill_interval = self.combat_modes[self.current_mode]['skill_interval']
        self.target_interval = self.combat_modes[self.current_mode]['target_interval']
        self.target_key = 'tab'
        self.last_target_time = 0
        
        # 技能使用策略
        self.skill_strategy = 'mixed'  # 'basic_only', 'alt_only', 'mixed'
        self.use_skill_rotation = True  # 是否使用技能轮换
        
        # 统计信息
        self.stats = {
            'skills_cast': 0,
            'targets_selected': 0,
            'combat_time': 0,
            'start_time': 0
        }
        
        self.logger.info("增强版自动战斗系统已初始化")
    
    def set_combat_mode(self, mode):
        """设置战斗模式"""
        if mode in self.combat_modes:
            self.current_mode = mode
            config = self.combat_modes[mode]
            self.skill_interval = config['skill_interval']
            self.target_interval = config['target_interval']
            self.logger.info(f"战斗模式已设置为: {mode}")
            self.logger.info(f"技能间隔: {self.skill_interval}s, 目标间隔: {self.target_interval}s")
        else:
            self.logger.error(f"无效的战斗模式: {mode}")
    
    def set_skill_strategy(self, strategy):
        """设置技能使用策略"""
        valid_strategies = ['basic_only', 'alt_only', 'mixed']
        if strategy in valid_strategies:
            self.skill_strategy = strategy
            self.logger.info(f"技能策略已设置为: {strategy}")
        else:
            self.logger.error(f"无效的技能策略: {strategy}")
    
    def start(self):
        """启动自动战斗"""
        if self.is_active:
            self.logger.warning("自动战斗已经在运行中")
            return
        
        self.is_active = True
        self.stats['start_time'] = time.time()
        self.combat_thread = threading.Thread(target=self._combat_loop, daemon=True)
        self.combat_thread.start()
        self.logger.info(f"🚀 增强版自动战斗已启动 (模式: {self.current_mode})")
    
    def stop(self):
        """停止自动战斗"""
        if not self.is_active:
            return
        
        self.is_active = False
        if self.combat_thread and self.combat_thread.is_alive():
            self.combat_thread.join(timeout=2.0)
        
        # 计算统计信息
        if self.stats['start_time'] > 0:
            self.stats['combat_time'] = time.time() - self.stats['start_time']
        
        self.logger.info("⏹ 增强版自动战斗已停止")
        self._print_stats()
    
    def _combat_loop(self):
        """增强版战斗主循环"""
        self.logger.info("进入增强版战斗循环...")
        
        while self.is_active:
            try:
                current_time = time.time()
                
                # 1. 智能目标选择
                if current_time - self.last_target_time >= self.target_interval:
                    self._smart_target_selection()
                    self.last_target_time = current_time
                    time.sleep(0.05)  # 极短等待
                
                # 2. 高速技能释放
                self._cast_next_skill_enhanced()
                
                # 3. 动态间隔调整
                time.sleep(self.skill_interval)
                
            except Exception as e:
                self.logger.error(f"战斗循环错误: {e}")
                time.sleep(0.2)
    
    def _smart_target_selection(self):
        """智能目标选择"""
        try:
            # 可以扩展为更智能的目标选择逻辑
            success = self.input.press_key(self.target_key)
            if success:
                self.stats['targets_selected'] += 1
                self.logger.debug(f"🎯 选择目标: {self.target_key}")
            else:
                self.logger.warning(f"⚠ 选择目标失败: {self.target_key}")
        except Exception as e:
            self.logger.error(f"目标选择错误: {e}")
    
    def _cast_next_skill_enhanced(self):
        """增强版技能释放"""
        try:
            # 根据策略选择技能
            skill_key = self._get_next_skill()
            if not skill_key:
                return
            
            # 高速发送技能
            success = self._send_skill_fast(skill_key)
            
            if success:
                self.stats['skills_cast'] += 1
                self.logger.debug(f"⚔️ 技能释放: {skill_key}")
            else:
                self.logger.warning(f"⚠ 技能失败: {skill_key}")
            
        except Exception as e:
            self.logger.error(f"技能释放错误: {e}")
    
    def _get_next_skill(self):
        """根据策略获取下一个技能"""
        if self.skill_strategy == 'basic_only':
            skills = self.basic_skills
        elif self.skill_strategy == 'alt_only':
            skills = self.alt_skills
        else:  # mixed
            skills = self.all_skills
        
        if not skills:
            return None
        
        skill = skills[self.current_skill_index % len(skills)]
        self.current_skill_index += 1
        
        return skill
    
    def _send_skill_fast(self, skill_key):
        """高速发送技能"""
        try:
            if skill_key.startswith('a+'):
                # Alt组合键
                key_part = skill_key.split('+')[1]
                return self.input.press_alt_key(key_part)
            else:
                # 普通按键
                return self.input.press_key(skill_key)
        except Exception as e:
            self.logger.error(f"快速技能发送失败: {e}")
            return False
    
    def test_all_skills_fast(self):
        """快速测试所有技能"""
        self.logger.info("开始快速测试所有技能...")
        
        total_skills = len(self.all_skills)
        success_count = 0
        
        for i, skill_key in enumerate(self.all_skills):
            self.logger.info(f"测试 {i+1}/{total_skills}: {skill_key}")
            
            success = self._send_skill_fast(skill_key)
            
            if success:
                success_count += 1
                self.logger.info(f"  ✓ {skill_key}")
            else:
                self.logger.error(f"  ✗ {skill_key}")
            
            time.sleep(0.4)  # 快速测试间隔
        
        success_rate = (success_count / total_skills) * 100
        self.logger.info(f"技能测试完成: {success_count}/{total_skills} ({success_rate:.1f}%)")
        
        return success_count == total_skills
    
    def test_skill_sequence(self, count=20):
        """测试技能序列释放"""
        self.logger.info(f"开始测试技能序列释放 ({count}个技能)...")
        
        start_time = time.time()
        success_count = 0
        
        for i in range(count):
            skill_key = self._get_next_skill()
            success = self._send_skill_fast(skill_key)
            
            if success:
                success_count += 1
                self.logger.debug(f"序列 {i+1}: ✓ {skill_key}")
            else:
                self.logger.warning(f"序列 {i+1}: ✗ {skill_key}")
            
            time.sleep(self.skill_interval)
        
        elapsed_time = time.time() - start_time
        skills_per_second = count / elapsed_time
        
        self.logger.info(f"序列测试完成:")
        self.logger.info(f"  成功率: {success_count}/{count} ({(success_count/count)*100:.1f}%)")
        self.logger.info(f"  用时: {elapsed_time:.2f}秒")
        self.logger.info(f"  速度: {skills_per_second:.2f} 技能/秒")
        
        return success_count == count
    
    def _print_stats(self):
        """打印统计信息"""
        if self.stats['combat_time'] > 0:
            skills_per_second = self.stats['skills_cast'] / self.stats['combat_time']
            
            self.logger.info("📊 战斗统计:")
            self.logger.info(f"  释放技能: {self.stats['skills_cast']}")
            self.logger.info(f"  选择目标: {self.stats['targets_selected']}")
            self.logger.info(f"  战斗时间: {self.stats['combat_time']:.1f}秒")
            self.logger.info(f"  技能速度: {skills_per_second:.2f} 技能/秒")
    
    def get_status(self):
        """获取当前状态"""
        return {
            'is_active': self.is_active,
            'mode': self.current_mode,
            'strategy': self.skill_strategy,
            'skill_interval': self.skill_interval,
            'stats': self.stats.copy()
        }

def main():
    """主函数"""
    logger = setup_logging()
    
    print("=" * 60)
    print("⚡ 永恒之塔增强版自动战斗系统")
    print("=" * 60)
    
    try:
        # 1. 创建输入模拟器
        logger.info("创建输入模拟器...")
        from working_input_simulator import WorkingInputSimulator
        input_sim = WorkingInputSimulator()
        
        # 2. 创建增强版自动战斗系统
        logger.info("创建增强版自动战斗系统...")
        auto_combat = EnhancedAutoCombat(input_sim)
        
        # 3. 显示状态信息
        status = input_sim.get_status()
        print("\\n📊 系统状态:")
        print(f"  管理员权限: {'✓' if status['admin_rights'] else '✗'}")
        print(f"  找到游戏窗口: {'✓' if status['game_window_found'] else '✗'}")
        print(f"  游戏进程ID: {status['game_process_id'] or '未找到'}")
        
        # 4. 用户选择
        while True:
            print("\\n🎯 请选择操作:")
            print("1. 快速测试所有技能 (1-9 + Alt+1-9,0)")
            print("2. 测试技能序列释放 (20个技能)")
            print("3. 设置战斗模式 (fast/normal/slow)")
            print("4. 设置技能策略 (basic_only/alt_only/mixed)")
            print("5. 启动增强版自动战斗")
            print("6. 退出程序")
            
            try:
                choice = input("\\n请输入选择 (1-6): ").strip()
                
                if choice == '1':
                    print("\\n🧪 快速测试所有技能...")
                    auto_combat.test_all_skills_fast()
                    
                elif choice == '2':
                    print("\\n⚡ 测试技能序列释放...")
                    auto_combat.test_skill_sequence()
                    
                elif choice == '3':
                    mode = input("请输入战斗模式 (fast/normal/slow): ").strip()
                    auto_combat.set_combat_mode(mode)
                    
                elif choice == '4':
                    strategy = input("请输入技能策略 (basic_only/alt_only/mixed): ").strip()
                    auto_combat.set_skill_strategy(strategy)
                    
                elif choice == '5':
                    print("\\n🚀 启动增强版自动战斗...")
                    print(f"当前模式: {auto_combat.current_mode}")
                    print(f"技能策略: {auto_combat.skill_strategy}")
                    print("按 Ctrl+C 停止")
                    
                    auto_combat.start()
                    
                    try:
                        while auto_combat.is_active:
                            time.sleep(1)
                    except KeyboardInterrupt:
                        print("\\n⏹ 用户中断，停止自动战斗...")
                        auto_combat.stop()
                    
                elif choice == '6':
                    print("\\n👋 程序退出")
                    break
                    
                else:
                    print("❌ 无效选择，请输入 1-6")
                    
            except KeyboardInterrupt:
                print("\\n👋 程序退出")
                break
            except Exception as e:
                logger.error(f"用户交互错误: {e}")
        
        return True
        
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        print(f"\\n❌ 导入失败: {e}")
        return False
        
    except Exception as e:
        logger.error(f"程序运行错误: {e}")
        print(f"\\n❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\\n👋 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\\n💥 程序崩溃: {e}")
        sys.exit(1)