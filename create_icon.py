"""
创建应用图标
"""
from PIL import Image, ImageDraw, ImageFont
import os

def create_app_icon():
    """创建应用图标"""
    # 创建一个512x512的图像，带有透明背景
    img = Image.new('RGBA', (512, 512), color=(0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制圆形背景
    center = (256, 256)
    radius = 240
    # 渐变背景 - 深蓝到紫色
    for r in range(radius, 0, -1):
        # 计算渐变颜色
        ratio = r / radius
        red = int(41 * (1 - ratio) + 89 * ratio)
        green = int(65 * (1 - ratio) + 180 * ratio)
        blue = int(121 * (1 - ratio) + 250 * ratio)
        draw.ellipse(
            [(center[0] - r, center[1] - r), 
             (center[0] + r, center[1] + r)], 
            fill=(red, green, blue, 255)
        )
    
    # 绘制内部图案 - 简单的游戏控制器形状
    # 外环
    draw.ellipse([(106, 156), (406, 356)], outline=(255, 255, 255, 255), width=15)
    
    # 中心十字
    draw.line([(256, 196), (256, 316)], fill=(255, 255, 255, 255), width=15)
    draw.line([(146, 256), (366, 256)], fill=(255, 255, 255, 255), width=15)
    
    # 保存为PNG
    img.save('app_icon.png')
    
    # 尝试转换为ICO（如果PIL支持）
    try:
        # 创建不同尺寸的图标
        sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        img.save('icon.ico', sizes=sizes)
        print("已创建ICO图标文件")
    except Exception as e:
        print(f"无法创建ICO文件: {e}")
        print("请使用在线转换工具将PNG转换为ICO")

if __name__ == "__main__":
    create_app_icon()
    print("图标已生成: app_icon.png")
