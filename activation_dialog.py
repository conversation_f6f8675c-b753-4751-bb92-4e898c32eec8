#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
硬件激活对话框模块 - 负责显示和处理硬件激活
"""

import os
import sys
import logging
import hashlib
import platform
import subprocess
import uuid
import json
import hmac
import base64
from datetime import datetime, timedelta
import winreg

from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, 
    QMessageBox, QFormLayout, QDialogButtonBox, QApplication
)

# 设置日志
logger = logging.getLogger('activation_dialog')

class HardwareInfo:
    """获取并处理硬件信息"""
    
    @staticmethod
    def get_cpu_id():
        """获取CPU ID"""
        if platform.system() == "Windows":
            try:
                # 使用WMI获取处理器ID
                result = subprocess.check_output('wmic cpu get ProcessorId').decode('utf-8').strip()
                lines = result.split('\n')
                if len(lines) >= 2:
                    return lines[1].strip()
            except:
                pass
        return "UNKNOWN_CPU"
    
    @staticmethod
    def get_disk_serial():
        """获取系统磁盘序列号"""
        if platform.system() == "Windows":
            try:
                result = subprocess.check_output('wmic diskdrive get SerialNumber').decode('utf-8').strip()
                lines = result.split('\n')
                if len(lines) >= 2:
                    return lines[1].strip()
            except:
                pass
        return "UNKNOWN_DISK"
    
    @staticmethod
    def get_bios_serial():
        """获取BIOS序列号"""
        if platform.system() == "Windows":
            try:
                result = subprocess.check_output('wmic bios get SerialNumber').decode('utf-8').strip()
                lines = result.split('\n')
                if len(lines) >= 2:
                    return lines[1].strip()
            except:
                pass
        return "UNKNOWN_BIOS"
    
    @staticmethod
    def get_mac_address():
        """获取第一个网络适配器的MAC地址"""
        if platform.system() == "Windows":
            try:
                result = subprocess.check_output('getmac /fo csv /nh').decode('utf-8')
                mac = result.split(',')[0].strip('"')
                return mac
            except:
                pass
        return "00:00:00:00:00:00"
    
    @staticmethod
    def get_machine_guid():
        """获取Windows的MachineGUID"""
        if platform.system() == "Windows":
            try:
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                    r"SOFTWARE\Microsoft\Cryptography")
                value, _ = winreg.QueryValueEx(key, "MachineGuid")
                winreg.CloseKey(key)
                return value
            except:
                pass
        return str(uuid.uuid4())
    
    @staticmethod
    def generate_machine_id():
        """生成基于多种硬件信息的唯一机器ID"""
        # 收集硬件信息
        cpu_id = HardwareInfo.get_cpu_id()
        disk_serial = HardwareInfo.get_disk_serial()
        bios_serial = HardwareInfo.get_bios_serial()
        mac_address = HardwareInfo.get_mac_address()
        machine_guid = HardwareInfo.get_machine_guid()
        
        # 创建唯一标识字符串
        hardware_str = f"{cpu_id}|{disk_serial}|{bios_serial}|{mac_address}|{machine_guid}"
        
        # 使用SHA256哈希
        machine_hash = hashlib.sha256(hardware_str.encode()).hexdigest()
        
        # 返回格式化的机器ID (8-4-4-4-12格式)
        return f"{machine_hash[:8]}-{machine_hash[8:12]}-{machine_hash[12:16]}-{machine_hash[16:20]}-{machine_hash[20:32]}"


class HardwareActivationDialog(QDialog):
    """硬件激活对话框"""
    
    def __init__(self, parent=None, license_checker=None, hardware_info=None):
        super().__init__(parent)
        
        self.license_checker = license_checker
        self.hardware_info_class = hardware_info
        
        self.setWindowTitle("硬件激活")
        self.setMinimumWidth(500)
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("硬件激活")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 机器ID
        machine_id_layout = QHBoxLayout()
        self.machine_id = HardwareInfo.generate_machine_id()
        machine_id_label = QLabel(f"机器ID: {self.machine_id}")
        machine_id_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        machine_id_layout.addWidget(machine_id_label)
        
        # 复制按钮
        copy_btn = QPushButton("复制")
        copy_btn.setMaximumWidth(60)
        copy_btn.clicked.connect(self.copy_machine_id)
        machine_id_layout.addWidget(copy_btn)
        
        layout.addLayout(machine_id_layout)
        
        # 提示信息
        instruction = QLabel("请输入激活码进行激活。激活码应当与您的机器ID绑定。")
        instruction.setWordWrap(True)
        layout.addWidget(instruction)
        
        # 激活码输入
        form_layout = QFormLayout()
        self.activation_key = QLineEdit()
        self.activation_key.setPlaceholderText("格式: XXXX-XXXX-XXXX-XXXX")
        form_layout.addRow("激活码:", self.activation_key)
        layout.addLayout(form_layout)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # 状态信息
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: red;")
        self.status_label.setWordWrap(True)
        layout.addWidget(self.status_label)
    
    def copy_machine_id(self):
        """复制机器ID到剪贴板"""
        clipboard = QApplication.clipboard()
        clipboard.setText(self.machine_id)
        self.status_label.setText("机器ID已复制到剪贴板")
        self.status_label.setStyleSheet("color: green;")
        
        # 2秒后清除状态
        QTimer.singleShot(2000, lambda: self.status_label.setText(""))
    
    def accept(self):
        """处理确认按钮点击"""
        activation_key = self.activation_key.text().strip()
        
        if not activation_key:
            self.status_label.setText("请输入激活码")
            self.status_label.setStyleSheet("color: red;")
            return
            
        # 这里可以添加验证激活码的逻辑
        # 简单的演示逻辑：检查激活码长度和格式
        key_parts = activation_key.split('-')
        if len(key_parts) != 4 or not all(len(part) == 4 for part in key_parts):
            self.status_label.setText("激活码格式不正确")
            self.status_label.setStyleSheet("color: red;")
            return
            
        # 假设激活成功，保存激活信息
        self.save_activation_info(activation_key)
        super().accept()
    
    def save_activation_info(self, activation_key):
        """保存激活信息"""
        # 这里可以添加保存激活信息的逻辑
        # 演示代码：将激活信息保存到文件
        try:
            activation_info = {
                "machine_id": self.machine_id,
                "activation_key": activation_key,
                "activation_date": datetime.now().isoformat(),
                "expiry_date": (datetime.now() + timedelta(days=365)).isoformat()
            }
            
            with open(".activation", "w") as f:
                json.dump(activation_info, f)
                
            logger.info(f"已保存激活信息，到期日期: {activation_info['expiry_date']}")
        except Exception as e:
            logger.error(f"保存激活信息失败: {str(e)}")


def check_activation():
    """检查激活状态，返回(is_activated, expiry_date)元组"""
    try:
        if not os.path.exists(".activation"):
            return False, None
            
        with open(".activation", "r") as f:
            activation_info = json.load(f)
            
        # 验证机器ID
        current_machine_id = HardwareInfo.generate_machine_id()
        if activation_info.get("machine_id") != current_machine_id:
            return False, None
            
        # 验证到期日期
        if "expiry_date" in activation_info:
            expiry_date = datetime.fromisoformat(activation_info["expiry_date"])
            return True, expiry_date
            
    except Exception as e:
        logger.error(f"检查激活状态时出错: {str(e)}")
        
    return False, None


if __name__ == "__main__":
    # 简单测试
    app = QApplication(sys.argv)
    dialog = HardwareActivationDialog()
    result = dialog.exec()
    print(f"Dialog result: {result}")
    sys.exit(0)
