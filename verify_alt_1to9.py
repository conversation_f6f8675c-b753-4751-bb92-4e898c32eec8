#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证Alt+1到Alt+9组合键技能配置
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_alt_1to9_skills():
    """验证Alt+1到Alt+9技能配置"""
    print("=" * 60)
    print("Alt+1到Alt+9组合键技能验证")
    print("=" * 60)
    
    try:
        from battle_logic import BattleManager
        from memory_reader import MemoryReader
        from input_simulator import InputSimulator
        from monster_select import MonsterSelector
        from tactical_ai import TacticalAI
        from screen_detector import ScreenDetector
        
        # 创建组件
        memory = MemoryReader()
        input_sim = InputSimulator()
        monster_selector = MonsterSelector(memory)
        battle_manager = BattleManager(memory, input_sim, monster_selector)
        screen = ScreenDetector()
        ai = TacticalAI(memory, screen)
        
        print("✓ 组件初始化成功")
        
        # 验证战斗管理器中的组合键技能
        print(f"\n🔥 战斗管理器技能配置验证:")
        
        expected_combo_skills = [f"alt+{i}" for i in range(1, 10)]  # alt+1 到 alt+9
        
        combo_skills_in_battle = [k for k, v in battle_manager.skills.items() if v['type'] == 'combo']
        combo_keys_in_battle = [v['key'] for k, v in battle_manager.skills.items() if v['type'] == 'combo']
        
        print(f"  组合键技能数量: {len(combo_skills_in_battle)}")
        print(f"  组合键列表: {combo_keys_in_battle}")
        
        missing_in_battle = []
        for expected in expected_combo_skills:
            if expected in combo_keys_in_battle:
                print(f"  ✅ {expected}")
            else:
                print(f"  ❌ {expected}")
                missing_in_battle.append(expected)
        
        # 验证攻击序列
        print(f"\n⚔️ 攻击序列验证:")
        print(f"  序列长度: {len(battle_manager.attack_sequence)}")
        print(f"  完整序列: {battle_manager.attack_sequence}")
        
        expected_sequence = ["1", "2", "3", "4", "5", "6", "7", "8", "9"] + expected_combo_skills
        sequence_correct = battle_manager.attack_sequence == expected_sequence
        
        print(f"  序列正确性: {'✅' if sequence_correct else '❌'}")
        
        if not sequence_correct:
            print(f"  期望序列: {expected_sequence}")
            print(f"  实际序列: {battle_manager.attack_sequence}")
        
        # 验证AI技能优先级配置
        print(f"\n🤖 AI技能优先级验证:")
        
        ai_combo_skills = [k for k in ai.skill_priorities.keys() if k.startswith('alt+')]
        print(f"  AI组合键技能数量: {len(ai_combo_skills)}")
        print(f"  AI组合键列表: {ai_combo_skills}")
        
        missing_in_ai = []
        for expected in expected_combo_skills:
            if expected in ai.skill_priorities:
                priority = ai.skill_priorities[expected]['priority']
                skill_type = ai.skill_priorities[expected]['type']
                print(f"  ✅ {expected} - 优先级: {priority}, 类型: {skill_type}")
            else:
                print(f"  ❌ {expected}")
                missing_in_ai.append(expected)
        
        # 验证范围技能配置
        print(f"\n🌀 范围技能配置验证:")
        print(f"  范围技能列表: {battle_manager.aoe_skills}")
        
        expected_aoe = ["3", "6", "9", "alt+3", "alt+6", "alt+9"]
        aoe_correct = battle_manager.aoe_skills == expected_aoe
        print(f"  范围技能正确性: {'✅' if aoe_correct else '❌'}")
        
        # 总体验证结果
        all_correct = (
            len(missing_in_battle) == 0 and
            len(missing_in_ai) == 0 and
            sequence_correct and
            aoe_correct
        )
        
        return all_correct, {
            'battle_missing': missing_in_battle,
            'ai_missing': missing_in_ai,
            'sequence_correct': sequence_correct,
            'aoe_correct': aoe_correct,
            'combo_count': len(combo_skills_in_battle)
        }
        
    except Exception as e:
        print(f"✗ 验证失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def show_skill_mapping():
    """显示技能映射"""
    print("\n" + "=" * 60)
    print("技能映射对照表")
    print("=" * 60)
    
    mapping = """
🎮 完整技能映射 (18个技能):

📋 基础技能 (1-9):
  1 → 基础攻击
  2 → 连击技能
  3 → 范围攻击
  4 → 高伤害技能
  5 → 减益技能
  6 → 持续伤害
  7 → 终极技能
  8 → 控制技能
  9 → 爆发技能

⚡ 组合键技能 (Alt+1到Alt+9):
  Alt+1 → 增强基础攻击
  Alt+2 → 增强连击技能
  Alt+3 → 增强范围攻击
  Alt+4 → 增强高伤害技能
  Alt+5 → 增强减益技能
  Alt+6 → 增强持续伤害
  Alt+7 → 增强终极技能
  Alt+8 → 增强控制技能
  Alt+9 → 增强爆发技能

🔄 技能循环顺序:
1 → 2 → 3 → 4 → 5 → 6 → 7 → 8 → 9 → 
Alt+1 → Alt+2 → Alt+3 → Alt+4 → Alt+5 → Alt+6 → Alt+7 → Alt+8 → Alt+9
(循环重复，每个技能间隔0.5毫秒)

🌀 范围技能:
基础范围: 3, 6, 9
组合范围: Alt+3, Alt+6, Alt+9
"""
    
    print(mapping)

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "=" * 60)
    print("使用指南")
    print("=" * 60)
    
    guide = """
🚀 Alt+1到Alt+9组合键使用方法:

1️⃣ 自动模式 (推荐):
   • 启动副本辅助
   • AI自动使用所有18个技能
   • 包括Alt+1到Alt+9组合键
   • 极限速度循环释放

2️⃣ 手动模式:
   • 按住Alt键 + 数字键1-9
   • 例如: Alt+1, Alt+2, Alt+3等
   • 可以在战斗中手动释放

3️⃣ 混合模式:
   • 启用"手动点怪自动技能"
   • 手动选择目标
   • AI自动选择最佳技能释放

🎯 技能特点:

• 极限速度: 0.5毫秒冷却
• 智能优先级: AI自动选择最佳技能
• 完美循环: 18技能无缝衔接
• 增强威力: 组合键技能威力更强

⚠️ 注意事项:

• 确保游戏中Alt+数字键没有冲突
• 组合键需要同时按下Alt和数字键
• 建议在游戏设置中清空Alt+1-9绑定

🔧 故障排除:

• 组合键无效: 检查游戏按键绑定
• AI不使用: 确保AI功能已启用
• 技能冲突: 调整游戏内快捷键设置
"""
    
    print(guide)

def main():
    """主验证函数"""
    print("Alt+1到Alt+9组合键技能验证")
    print("=" * 60)
    
    # 验证配置
    success, result_data = verify_alt_1to9_skills()
    
    # 显示技能映射
    show_skill_mapping()
    
    # 显示使用指南
    show_usage_guide()
    
    # 输出验证结果
    print("\n" + "=" * 60)
    print("验证结果")
    print("=" * 60)
    
    if success:
        print("✅ Alt+1到Alt+9组合键技能验证: 通过")
        print("\n🎉 组合键技能配置完全正确！")
        print("\n现在您拥有:")
        print("⚔️ 18个技能 (9个基础 + 9个Alt组合)")
        print("🎮 Alt+1, Alt+2, Alt+3, Alt+4, Alt+5, Alt+6, Alt+7, Alt+8, Alt+9")
        print("🤖 AI智能使用所有技能")
        print("⚡ 极限速度释放 (0.5毫秒间隔)")
        print("🔄 完美的18技能循环")
        
        print("\n技能循环顺序:")
        print("1→2→3→4→5→6→7→8→9→Alt+1→Alt+2→Alt+3→Alt+4→Alt+5→Alt+6→Alt+7→Alt+8→Alt+9")
        
        print("\n立即生效:")
        print("1. 重新启动程序")
        print("2. 连接游戏进程")
        print("3. 启动副本辅助")
        print("4. 享受18技能极限战斗！")
    else:
        print("❌ Alt+1到Alt+9组合键技能验证失败")
        if result_data:
            if result_data['battle_missing']:
                print(f"战斗管理器缺失: {result_data['battle_missing']}")
            if result_data['ai_missing']:
                print(f"AI配置缺失: {result_data['ai_missing']}")
            if not result_data['sequence_correct']:
                print("攻击序列不正确")
            if not result_data['aoe_correct']:
                print("范围技能配置不正确")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
