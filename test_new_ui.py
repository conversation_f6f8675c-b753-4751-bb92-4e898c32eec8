#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
最小测试UI - 验证新功能
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class TestMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("新功能测试UI")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标题
        title = QLabel("新功能测试界面")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # 自动范围打怪组
        auto_aoe_group = QGroupBox("🌀 自动范围打怪")
        auto_aoe_layout = QVBoxLayout(auto_aoe_group)
        
        self.auto_aoe_check = QCheckBox("启用自动范围打怪")
        auto_aoe_layout.addWidget(self.auto_aoe_check)
        
        # 范围技能设置
        aoe_settings_layout = QHBoxLayout()
        aoe_settings_layout.addWidget(QLabel("范围技能:"))
        self.aoe_skills_edit = QLineEdit("3,6,9")
        aoe_settings_layout.addWidget(self.aoe_skills_edit)
        
        aoe_settings_layout.addWidget(QLabel("最少目标:"))
        self.aoe_min_targets_spin = QSpinBox()
        self.aoe_min_targets_spin.setRange(1, 10)
        self.aoe_min_targets_spin.setValue(2)
        aoe_settings_layout.addWidget(self.aoe_min_targets_spin)
        
        auto_aoe_layout.addLayout(aoe_settings_layout)
        layout.addWidget(auto_aoe_group)
        
        # 手动点怪自动技能组
        manual_target_group = QGroupBox("👆 手动点怪自动技能")
        manual_target_layout = QVBoxLayout(manual_target_group)
        
        self.manual_target_auto_skill_check = QCheckBox("启用手动点怪自动技能")
        manual_target_layout.addWidget(self.manual_target_auto_skill_check)
        
        # 手动目标控制
        manual_control_layout = QHBoxLayout()
        self.force_manual_target_button = QPushButton("设置当前目标为手动")
        manual_control_layout.addWidget(self.force_manual_target_button)
        
        self.clear_manual_target_button = QPushButton("清除手动目标")
        manual_control_layout.addWidget(self.clear_manual_target_button)
        
        manual_target_layout.addLayout(manual_control_layout)
        layout.addWidget(manual_target_group)
        
        # 专注击杀模式组
        focus_kill_group = QGroupBox("🎯 专注击杀模式")
        focus_kill_layout = QVBoxLayout(focus_kill_group)
        
        self.focus_kill_check = QCheckBox("启用专注击杀模式")
        self.focus_kill_check.setChecked(True)
        focus_kill_layout.addWidget(self.focus_kill_check)
        
        # 专注击杀参数
        focus_params_layout = QHBoxLayout()
        focus_params_layout.addWidget(QLabel("最大专注时间:"))
        self.max_focus_time_spin = QSpinBox()
        self.max_focus_time_spin.setRange(5, 60)
        self.max_focus_time_spin.setValue(15)
        self.max_focus_time_spin.setSuffix(" 秒")
        focus_params_layout.addWidget(self.max_focus_time_spin)
        
        focus_params_layout.addWidget(QLabel("无伤害超时:"))
        self.no_damage_timeout_spin = QSpinBox()
        self.no_damage_timeout_spin.setRange(2, 20)
        self.no_damage_timeout_spin.setValue(5)
        self.no_damage_timeout_spin.setSuffix(" 秒")
        focus_params_layout.addWidget(self.no_damage_timeout_spin)
        
        focus_kill_layout.addLayout(focus_params_layout)
        layout.addWidget(focus_kill_group)
        
        # 自动攻击周围怪物组
        auto_attack_group = QGroupBox("⚔️ 自动攻击周围怪物")
        auto_attack_layout = QVBoxLayout(auto_attack_group)
        
        self.auto_attack_check = QCheckBox("启用自动攻击周围怪物")
        auto_attack_layout.addWidget(self.auto_attack_check)
        
        # 攻击范围设置
        range_layout = QHBoxLayout()
        range_layout.addWidget(QLabel("攻击范围:"))
        self.attack_range_spin = QSpinBox()
        self.attack_range_spin.setRange(5, 50)
        self.attack_range_spin.setValue(30)
        self.attack_range_spin.setSuffix(" 米")
        range_layout.addWidget(self.attack_range_spin)
        
        range_layout.addWidget(QLabel("扫描间隔:"))
        self.scan_interval_spin = QSpinBox()
        self.scan_interval_spin.setRange(50, 1000)
        self.scan_interval_spin.setValue(100)
        self.scan_interval_spin.setSuffix(" 毫秒")
        range_layout.addWidget(self.scan_interval_spin)
        
        auto_attack_layout.addLayout(range_layout)
        layout.addWidget(auto_attack_group)
        
        # 状态显示
        status_group = QGroupBox("📊 功能状态")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("所有新功能已加载完成！")
        self.status_label.setStyleSheet("color: green; font-weight: bold;")
        status_layout.addWidget(self.status_label)
        
        layout.addWidget(status_group)
        
        # 连接信号
        self.auto_aoe_check.stateChanged.connect(self.on_aoe_changed)
        self.manual_target_auto_skill_check.stateChanged.connect(self.on_manual_changed)
        self.focus_kill_check.stateChanged.connect(self.on_focus_changed)
        self.auto_attack_check.stateChanged.connect(self.on_attack_changed)
        
    def on_aoe_changed(self, state):
        status = "启用" if state == Qt.Checked else "禁用"
        print(f"自动范围打怪: {status}")
        
    def on_manual_changed(self, state):
        status = "启用" if state == Qt.Checked else "禁用"
        print(f"手动点怪自动技能: {status}")
        
    def on_focus_changed(self, state):
        status = "启用" if state == Qt.Checked else "禁用"
        print(f"专注击杀模式: {status}")
        
    def on_attack_changed(self, state):
        status = "启用" if state == Qt.Checked else "禁用"
        print(f"自动攻击周围怪物: {status}")

def main():
    app = QApplication(sys.argv)
    window = TestMainWindow()
    window.show()
    
    print("=" * 60)
    print("测试UI已启动")
    print("=" * 60)
    print("您应该看到以下功能组:")
    print("🌀 自动范围打怪")
    print("👆 手动点怪自动技能") 
    print("🎯 专注击杀模式")
    print("⚔️ 自动攻击周围怪物")
    print("📊 功能状态")
    print("=" * 60)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
