{{ ... }}

# 锁空功能
def toggle_air_lock(memory_helper, checked):
    """切换锁空状态"""
    if not memory_helper or not memory_helper.process_handle:
        return False
    
    try:
        # 获取锁空地址
        base_offset = int(game_versions[current_version]["base_offset"], 16)
        level1_offset = int(game_versions[current_version]["level1_offset"], 16)
        level2_offset = int(game_versions[current_version]["level2_offset"], 16)
        level3_offset = int(game_versions[current_version]["level3_offset"], 16)
        character_offset = int(game_versions[current_version]["character_base"], 16)
        air_lock_offset = int(game_versions[current_version]["air_lock"], 16)
        
        # 读取多级指针
        base_addr = memory_helper.read_long(memory_helper.module_base + base_offset)
        if not base_addr:
            return False
            
        level1_addr = memory_helper.read_long(base_addr + level1_offset)
        if not level1_addr:
            return False
            
        level2_addr = memory_helper.read_long(level1_addr + level2_offset)
        if not level2_addr:
            return False
            
        level3_addr = memory_helper.read_long(level2_addr + level3_offset)
        if not level3_addr:
            return False
            
        character_addr = memory_helper.read_long(level3_addr + character_offset)
        if not character_addr:
            return False
            
        air_lock_addr = character_addr + air_lock_offset
        
        if checked:
            # 写入锁空值
            result = memory_helper.write_int(air_lock_addr, 5)
        else:
            # 写入解除锁空值
            result = memory_helper.write_int(air_lock_addr, 0)
        
        return result
    except Exception as e:
        logger.error(f"切换锁空状态时出错: {str(e)}")
        return False

# 飞天功能
def toggle_fly(memory_helper, checked, height_value=5.0, direction="up"):
    """飞天功能"""
    if not memory_helper or not memory_helper.process_handle:
        return False
    
    try:
        # 获取高度地址
        base_offset = int(game_versions[current_version]["base_offset"], 16)
        level1_offset = int(game_versions[current_version]["level1_offset"], 16)
        level2_offset = int(game_versions[current_version]["level2_offset"], 16)
        level3_offset = int(game_versions[current_version]["level3_offset"], 16)
        height_offset = int(game_versions[current_version]["height_base"], 16)
        z_coord_offset = int(game_versions[current_version]["z_coord"], 16)
        
        # 读取多级指针
        base_addr = memory_helper.read_long(memory_helper.module_base + base_offset)
        if not base_addr:
            return False
            
        level1_addr = memory_helper.read_long(base_addr + level1_offset)
        if not level1_addr:
            return False
            
        level2_addr = memory_helper.read_long(level1_addr + level2_offset)
        if not level2_addr:
            return False
            
        level3_addr = memory_helper.read_long(level2_addr + level3_offset)
        if not level3_addr:
            return False
            
        height_addr = memory_helper.read_long(level3_addr + height_offset)
        if not height_addr:
            return False
            
        z_addr = height_addr + z_coord_offset
        
        # 读取当前高度
        current_height = memory_helper.read_float(z_addr)
        if current_height is None:
            return False
            
        # 计算新高度
        if direction == "up":
            new_height = current_height + height_value
        else:
            new_height = current_height - height_value
        
        # 写入新高度
        result = memory_helper.write_float(z_addr, new_height)
        return result
    except Exception as e:
        logger.error(f"飞天功能出错: {str(e)}")
        return False

# 反隐功能
def toggle_stealth(memory_helper, checked):
    """切换反隐状态"""
    if not memory_helper or not memory_helper.process_handle:
        return False
    
    try:
        # 获取反隐地址
        stealth_offset = int(game_versions[current_version]["stealth_offset"], 16)
        stealth_addr = memory_helper.module_base + stealth_offset
        
        if checked:
            # 写入反隐值
            result = memory_helper.write_bytes(stealth_addr, bytearray([0x90, 0x90]))
        else:
            # 恢复原始值
            result = memory_helper.write_bytes(stealth_addr, bytearray([0x74, 0x09]))
        
        return result
    except Exception as e:
        logger.error(f"切换反隐状态时出错: {str(e)}")
        return False

# 视野扩展功能
def toggle_view_range(memory_helper, checked, view_value=100.0):
    """切换视野扩展状态"""
    if not memory_helper or not memory_helper.process_handle:
        return False
    
    global view_range_timer
    
    try:
        if checked:
            # 创建定时器，持续更新视野范围
            if view_range_timer is None:
                view_range_timer = QTimer()
                view_range_timer.timeout.connect(lambda: update_view_range(memory_helper, view_value))
            
            view_range_timer.start(100)  # 每100ms更新一次
        else:
            # 关闭视野扩展
            if view_range_timer:
                view_range_timer.stop()
            
            # 恢复默认视野
            view_offset = int(game_versions[current_version]["view_offset"], 16)
            view_offset1 = int(game_versions[current_version]["view_offset1"], 16)
            
            view_base1 = memory_helper.module_base + view_offset
            view_base2 = memory_helper.read_long(view_base1)
            if view_base2:
                view_base3 = view_base2 + view_offset1
                memory_helper.write_float(view_base3, 35.0)  # 恢复默认视野值
        
        return True
    except Exception as e:
        logger.error(f"切换视野扩展状态时出错: {str(e)}")
        return False

def update_view_range(memory_helper, view_value):
    """定时更新视野范围"""
    if not memory_helper or not memory_helper.process_handle:
        return
        
    try:
        # 获取视野地址
        view_offset = int(game_versions[current_version]["view_offset"], 16)
        view_offset1 = int(game_versions[current_version]["view_offset1"], 16)
        
        view_base1 = memory_helper.module_base + view_offset
        view_base2 = memory_helper.read_long(view_base1)
        if not view_base2:
            return
            
        view_base3 = view_base2 + view_offset1
        
        # 写入视野值
        memory_helper.write_float(view_base3, view_value)
        
    except Exception as e:
        logger.error(f"更新视野范围时出错: {str(e)}")
        # 出错时停止计时器
        if view_range_timer:
            view_range_timer.stop()

# 显血功能
def toggle_show_hp(memory_helper, checked):
    """切换显血状态"""
    if not memory_helper or not memory_helper.process_handle:
        return False
    
    try:
        # 获取显血地址
        show_hp_offset = int(game_versions[current_version]["show_hp"], 16)
        show_hp_addr = memory_helper.module_base + show_hp_offset
        
        if checked:
            # 写入NOP指令
            patch_bytes = bytearray([0x90, 0x90])
            result = memory_helper.write_bytes(show_hp_addr, patch_bytes)
        else:
            # 恢复原始指令
            original_bytes = bytearray([0x74, 0x1B])  # 原始跳转指令
            result = memory_helper.write_bytes(show_hp_addr, original_bytes)
        
        return result
    except Exception as e:
        logger.error(f"切换显血功能时出错: {str(e)}")
        return False

# 地图扩展功能
def toggle_map_extend(memory_helper, checked, map_value=200.0):
    """切换地图扩展状态"""
    if not memory_helper or not memory_helper.process_handle:
        return False
    
    global map_extend_timer
    
    try:
        if checked:
            # 创建定时器，持续更新地图范围
            if map_extend_timer is None:
                map_extend_timer = QTimer()
                map_extend_timer.timeout.connect(lambda: update_map_extend(memory_helper, map_value))
            
            map_extend_timer.start(100)  # 每100ms更新一次
        else:
            # 关闭地图扩展
            if map_extend_timer:
                map_extend_timer.stop()
            
            # 恢复默认地图范围
            map_offset = int(game_versions[current_version]["map_offset"], 16)
            map_addr = memory_helper.module_base + map_offset
            memory_helper.write_float(map_addr, 90.0)  # 恢复默认地图值
        
        return True
    except Exception as e:
        logger.error(f"切换地图扩展状态时出错: {str(e)}")
        return False

def update_map_extend(memory_helper, map_value):
    """定时更新地图范围"""
    if not memory_helper or not memory_helper.process_handle:
        return
        
    try:
        # 获取地图地址
        map_offset = int(game_versions[current_version]["map_offset"], 16)
        map_addr = memory_helper.module_base + map_offset
        
        # 写入地图值
        memory_helper.write_float(map_addr, map_value)
        
    except Exception as e:
        logger.error(f"更新地图范围时出错: {str(e)}")
        # 出错时停止计时器
        if map_extend_timer:
            map_extend_timer.stop()

{{ ... }}

# 初始化全局变量
view_range_timer = None
map_extend_timer = None

{{ ... }}