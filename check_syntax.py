#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
语法检查脚本
"""

import ast
import sys

def check_syntax(filename):
    """检查Python文件语法"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 尝试解析AST
        ast.parse(source)
        print(f"✅ {filename} 语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ {filename} 语法错误:")
        print(f"   行 {e.lineno}: {e.text.strip() if e.text else ''}")
        print(f"   错误: {e.msg}")
        return False
        
    except Exception as e:
        print(f"❌ {filename} 检查失败: {e}")
        return False

def main():
    """主函数"""
    files_to_check = [
        'main_enhanced.py',
        'working_input_simulator.py',
        'smart_target_manager.py',
        'tactical_ai.py',
        'enhanced_auto_combat.py'
    ]
    
    print("🔍 开始语法检查...")
    print("=" * 50)
    
    all_passed = True
    
    for filename in files_to_check:
        try:
            if check_syntax(filename):
                continue
            else:
                all_passed = False
        except FileNotFoundError:
            print(f"⚠ {filename} 文件不存在")
    
    print("=" * 50)
    if all_passed:
        print("🎉 所有文件语法检查通过!")
    else:
        print("❌ 发现语法错误，需要修复")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)