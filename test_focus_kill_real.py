#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
专注击杀真实测试脚本 - 验证专注击杀是否真正避免频繁切换
"""

import sys
import os
import time
import logging

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_target_switching_behavior():
    """测试目标切换行为"""
    print("=" * 60)
    print("专注击杀 vs 频繁切换 对比测试")
    print("=" * 60)
    
    try:
        from battle_logic import BattleManager
        from memory_reader import MemoryReader
        from input_simulator import InputSimulator
        from monster_select import MonsterSelector
        from tactical_ai import Target
        
        # 创建组件
        memory = MemoryReader()
        input_sim = InputSimulator()
        monster_selector = MonsterSelector(memory)
        battle_manager = BattleManager(memory, input_sim, monster_selector)
        
        # 启用AI功能
        battle_manager.toggle_ai_mode(True)
        battle_manager.toggle_auto_attack(True)
        battle_manager.is_active = True
        
        print("✓ 战斗管理器初始化成功")
        
        # 创建多个测试目标
        test_targets = []
        for i in range(5):
            target = Target(
                id=2000 + i,
                name=f"测试怪物{i+1}",
                level=50,
                hp_percent=0.8,
                distance=10.0 + i * 2,  # 不同距离
                position=(900 + i * 50, 500),
                last_seen=time.time(),
                threat_level=2 + (i % 3)  # 不同威胁等级
            )
            test_targets.append(target)
        
        print(f"创建了 {len(test_targets)} 个测试目标")
        
        # 测试1: 频繁切换模式
        print("\n" + "=" * 40)
        print("测试1: 频繁切换模式")
        print("=" * 40)
        
        battle_manager.toggle_focus_kill_mode(False)  # 禁用专注击杀
        target_switches_normal = test_target_selection_pattern(battle_manager, test_targets, "频繁切换模式")
        
        # 测试2: 专注击杀模式
        print("\n" + "=" * 40)
        print("测试2: 专注击杀模式")
        print("=" * 40)
        
        battle_manager.toggle_focus_kill_mode(True)  # 启用专注击杀
        target_switches_focus = test_target_selection_pattern(battle_manager, test_targets, "专注击杀模式")
        
        # 对比结果
        print("\n" + "=" * 60)
        print("对比结果")
        print("=" * 60)
        
        print(f"频繁切换模式 - 目标切换次数: {target_switches_normal}")
        print(f"专注击杀模式 - 目标切换次数: {target_switches_focus}")
        
        improvement = ((target_switches_normal - target_switches_focus) / target_switches_normal * 100) if target_switches_normal > 0 else 0
        print(f"切换次数减少: {improvement:.1f}%")
        
        if target_switches_focus < target_switches_normal:
            print("✅ 专注击杀模式成功减少了目标切换次数！")
            return True
        else:
            print("❌ 专注击杀模式没有减少目标切换次数")
            return False
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_target_selection_pattern(battle_manager, test_targets, mode_name):
    """测试目标选择模式"""
    print(f"\n开始 {mode_name} 测试...")
    
    target_switches = 0
    last_target_id = None
    
    # 模拟战术AI的目标列表
    if battle_manager.tactical_ai:
        # 清除之前的目标
        battle_manager.tactical_ai.current_target = None
        battle_manager.current_focus_target = None
    
    # 进行10轮目标选择测试
    for round_num in range(10):
        print(f"\n--- 第 {round_num + 1} 轮 ---")
        
        # 模拟目标扫描（随机改变目标顺序和属性）
        import random
        current_targets = test_targets.copy()
        random.shuffle(current_targets)
        
        # 随机改变一些目标的距离（模拟移动）
        for target in current_targets:
            target.distance += random.uniform(-2, 2)
            target.distance = max(5.0, min(30.0, target.distance))
        
        # 让AI选择目标
        if battle_manager.tactical_ai:
            # 确保AI知道当前模式
            battle_manager.sync_focus_mode_to_ai()
            
            selected_target = battle_manager.tactical_ai.select_best_target(current_targets)
            
            if selected_target:
                print(f"选择目标: {selected_target.name} (ID: {selected_target.id})")
                
                # 检查是否切换了目标
                if last_target_id is not None and last_target_id != selected_target.id:
                    target_switches += 1
                    print(f"🔄 目标切换: {last_target_id} -> {selected_target.id}")
                elif last_target_id == selected_target.id:
                    print(f"🔒 保持目标: {selected_target.id}")
                
                last_target_id = selected_target.id
                
                # 更新AI的当前目标
                battle_manager.tactical_ai.current_target = selected_target
                
                # 如果是专注击杀模式，设置专注目标
                if battle_manager.focus_kill_mode:
                    battle_manager.current_focus_target = selected_target
            else:
                print("未选择到目标")
        
        time.sleep(0.1)  # 短暂延迟
    
    print(f"\n{mode_name} 测试完成")
    print(f"总目标切换次数: {target_switches}")
    
    return target_switches

def test_focus_target_persistence():
    """测试专注目标的持久性"""
    print("\n" + "=" * 60)
    print("专注目标持久性测试")
    print("=" * 60)
    
    try:
        from battle_logic import BattleManager
        from memory_reader import MemoryReader
        from input_simulator import InputSimulator
        from monster_select import MonsterSelector
        from tactical_ai import Target
        
        # 创建组件
        memory = MemoryReader()
        input_sim = InputSimulator()
        monster_selector = MonsterSelector(memory)
        battle_manager = BattleManager(memory, input_sim, monster_selector)
        
        # 启用专注击杀模式
        battle_manager.toggle_ai_mode(True)
        battle_manager.toggle_focus_kill_mode(True)
        battle_manager.is_active = True
        
        print("✓ 专注击杀模式已启用")
        
        # 创建一个主要目标和几个干扰目标
        main_target = Target(
            id=3001,
            name="主要目标",
            level=50,
            hp_percent=0.8,
            distance=15.0,
            position=(960, 540),
            last_seen=time.time(),
            threat_level=2
        )
        
        distraction_targets = [
            Target(
                id=3002,
                name="干扰目标1",
                level=52,
                hp_percent=0.9,
                distance=12.0,  # 更近
                position=(900, 500),
                last_seen=time.time(),
                threat_level=3  # 更高威胁
            ),
            Target(
                id=3003,
                name="干扰目标2",
                level=48,
                hp_percent=0.7,
                distance=10.0,  # 最近
                position=(1000, 580),
                last_seen=time.time(),
                threat_level=4  # 最高威胁
            )
        ]
        
        all_targets = [main_target] + distraction_targets
        
        print(f"创建主要目标: {main_target.name}")
        print(f"创建 {len(distraction_targets)} 个干扰目标")
        
        # 首先选择主要目标
        battle_manager.tactical_ai.current_target = main_target
        battle_manager.current_focus_target = main_target
        
        print(f"\n设置专注目标: {main_target.name}")
        
        # 测试在有更好目标的情况下是否会坚持当前目标
        focus_maintained = 0
        total_rounds = 8
        
        for round_num in range(total_rounds):
            print(f"\n--- 第 {round_num + 1} 轮测试 ---")
            
            # 确保AI处于专注模式
            battle_manager.sync_focus_mode_to_ai()
            
            # 让AI重新选择目标
            selected_target = battle_manager.tactical_ai.select_best_target(all_targets)
            
            if selected_target:
                if selected_target.id == main_target.id:
                    focus_maintained += 1
                    print(f"✅ 保持专注: {selected_target.name}")
                else:
                    print(f"❌ 切换目标: {main_target.name} -> {selected_target.name}")
                
                # 更新当前目标
                battle_manager.tactical_ai.current_target = selected_target
            
            time.sleep(0.1)
        
        focus_rate = (focus_maintained / total_rounds) * 100
        print(f"\n专注目标保持率: {focus_rate:.1f}% ({focus_maintained}/{total_rounds})")
        
        if focus_rate >= 80:  # 80%以上认为成功
            print("✅ 专注击杀模式成功保持目标专注！")
            return True
        else:
            print("❌ 专注击杀模式未能保持目标专注")
            return False
        
    except Exception as e:
        print(f"✗ 专注目标持久性测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("专注击杀真实效果验证测试")
    print("=" * 60)
    
    # 执行测试
    test_results = []
    
    test_results.append(("目标切换行为对比", test_target_switching_behavior()))
    test_results.append(("专注目标持久性", test_focus_target_persistence()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 专注击杀功能验证成功！")
        print("\n修复效果:")
        print("✅ 专注击杀模式确实减少了目标切换")
        print("✅ AI能够保持对当前目标的专注")
        print("✅ 不会被更近或更高威胁的目标干扰")
        print("✅ 真正实现了'一个一个清理'的效果")
        
        print("\n现在重新启动程序，您将看到:")
        print("🎯 AI选择一个目标后会专注攻击")
        print("🔒 不会频繁切换到其他目标")
        print("⚔️ 直到目标死亡才会寻找下一个")
        print("🔄 有序高效的副本清理")
    else:
        print("⚠ 部分测试失败，专注击杀功能可能仍有问题。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
