#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自动攻击功能测试脚本 - 测试自动攻击周围怪物功能
"""

import sys
import os
import time
import logging

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_auto_attack_system():
    """测试自动攻击系统"""
    print("=" * 60)
    print("自动攻击系统测试")
    print("=" * 60)
    
    try:
        from battle_logic import BattleManager
        from memory_reader import MemoryReader
        from input_simulator import InputSimulator
        from monster_select import MonsterSelector
        
        # 创建组件
        memory = MemoryReader()
        input_sim = InputSimulator()
        monster_selector = MonsterSelector(memory)
        battle_manager = BattleManager(memory, input_sim, monster_selector)
        
        print("✓ 战斗管理器初始化成功")
        print(f"  - 自动攻击启用: {battle_manager.auto_attack_enabled}")
        print(f"  - 攻击范围: {battle_manager.attack_range}米")
        print(f"  - 扫描间隔: {battle_manager.auto_target_scan_interval}秒")
        
        # 启用AI功能
        battle_manager.toggle_ai_mode(True)
        battle_manager.toggle_smart_targeting(True)
        battle_manager.toggle_auto_attack(True)
        
        print("\n✓ AI功能已启用")
        print("  - AI增强模式: 启用")
        print("  - 智能目标选择: 启用")
        print("  - 自动攻击: 启用")
        
        # 测试攻击范围设置
        print("\n测试攻击范围设置:")
        test_ranges = [15, 25, 35, 45]
        for range_val in test_ranges:
            battle_manager.set_auto_attack_range(range_val)
            print(f"  设置攻击范围: {range_val}米 -> 实际: {battle_manager.attack_range}米")
        
        # 测试自动扫描功能
        print("\n测试自动扫描功能:")
        for i in range(5):
            print(f"\n--- 第 {i+1} 次扫描 ---")
            
            # 模拟扫描
            result = battle_manager._scan_for_nearby_monsters()
            
            if result:
                print("✓ 扫描成功，发现目标")
                print(f"  - 当前目标ID: {battle_manager.target_id}")
                print(f"  - 目标检测状态: {battle_manager.target_detected}")
            else:
                print("○ 未发现目标或扫描失败")
            
            time.sleep(0.5)
        
        # 测试自动攻击循环
        print("\n测试自动攻击循环:")
        battle_manager.is_active = True  # 激活战斗状态
        
        for i in range(10):
            print(f"\n--- 攻击循环 {i+1} ---")
            
            # 执行自动扫描和攻击
            result = battle_manager.auto_scan_and_attack()
            
            if result:
                print("✓ 自动攻击执行成功")
            else:
                print("○ 自动攻击未执行")
            
            # 模拟战斗状态更新
            battle_manager.update_battle_state()
            
            time.sleep(0.2)
        
        return True
        
    except Exception as e:
        print(f"✗ 自动攻击系统测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_target_scanning():
    """测试目标扫描功能"""
    print("\n" + "=" * 60)
    print("目标扫描功能测试")
    print("=" * 60)
    
    try:
        from tactical_ai import TacticalAI
        from screen_detector import ScreenDetector
        
        class MockMemoryReader:
            def __init__(self):
                self.process_id = 1234
        
        ai = TacticalAI(MockMemoryReader(), ScreenDetector())
        ai.debug_mode = True
        
        print("✓ 战术AI初始化成功")
        
        # 测试目标扫描
        print("\n目标扫描测试:")
        for i in range(3):
            print(f"\n--- 扫描 {i+1} ---")
            
            targets = ai.scan_for_targets()
            print(f"发现目标数量: {len(targets)}")
            
            for j, target in enumerate(targets):
                print(f"  目标{j+1}: {target.name} (距离: {target.distance:.1f}米, 优先级: {target.priority.name})")
            
            # 测试目标选择
            if targets:
                best_target = ai.select_best_target(targets)
                if best_target:
                    print(f"最佳目标: {best_target.name} (ID: {best_target.id})")
                else:
                    print("未选择到最佳目标")
            
            time.sleep(0.3)
        
        return True
        
    except Exception as e:
        print(f"✗ 目标扫描测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_attack_execution():
    """测试攻击执行功能"""
    print("\n" + "=" * 60)
    print("攻击执行功能测试")
    print("=" * 60)
    
    try:
        from battle_logic import BattleManager
        from memory_reader import MemoryReader
        from input_simulator import InputSimulator
        from monster_select import MonsterSelector
        from tactical_ai import Target
        
        # 创建组件
        memory = MemoryReader()
        input_sim = InputSimulator()
        monster_selector = MonsterSelector(memory)
        battle_manager = BattleManager(memory, input_sim, monster_selector)
        
        # 启用AI
        battle_manager.toggle_ai_mode(True)
        
        print("✓ 战斗管理器初始化成功")
        
        # 创建测试目标
        test_target = Target(
            id=9999,
            name="测试怪物",
            level=50,
            hp_percent=0.8,
            distance=15.0,
            position=(960, 540),
            last_seen=time.time(),
            threat_level=2
        )
        
        print(f"\n创建测试目标: {test_target.name}")
        print(f"  - 等级: {test_target.level}")
        print(f"  - 血量: {test_target.hp_percent * 100:.0f}%")
        print(f"  - 距离: {test_target.distance}米")
        print(f"  - 威胁等级: {test_target.threat_level}")
        
        # 测试目标选择
        print("\n测试目标选择:")
        result = battle_manager._select_target(test_target)
        print(f"目标选择结果: {'成功' if result else '失败'}")
        
        # 测试立即攻击
        print("\n测试立即攻击:")
        for i in range(5):
            result = battle_manager._immediate_attack(test_target)
            print(f"攻击 {i+1}: {'成功' if result else '失败'}")
            time.sleep(0.1)
        
        # 测试AI攻击决策
        print("\n测试AI攻击决策:")
        if battle_manager.tactical_ai:
            player_state = battle_manager.tactical_ai.update_player_state()
            action = battle_manager.tactical_ai.decide_combat_action(player_state, test_target)
            
            print(f"AI决策结果:")
            print(f"  - 行动类型: {action['type']}")
            print(f"  - 技能: {action.get('skill', 'N/A')}")
            print(f"  - 原因: {action['reason']}")
            
            # 执行AI行动
            result = battle_manager._execute_ai_action(action, test_target)
            print(f"AI行动执行: {'成功' if result else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"✗ 攻击执行测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_auto_attack_features():
    """显示自动攻击功能特性"""
    print("\n" + "=" * 60)
    print("自动攻击功能特性")
    print("=" * 60)
    
    features = [
        "🎯 智能目标扫描 - 自动发现周围怪物",
        "📏 可调攻击范围 - 5-50米自由设置",
        "⚡ 高频扫描 - 100毫秒间隔实时扫描",
        "🧠 AI目标选择 - 智能选择最佳目标",
        "🔄 自动目标切换 - 目标死亡后自动寻找新目标",
        "⚔️ 多种攻击模式 - AI决策 + 传统Tab选择",
        "🎮 无缝集成 - 与现有战斗系统完美结合",
        "🔧 实时配置 - 运行时可调整参数",
        "📊 状态监控 - 实时显示攻击状态",
        "🛡️ 错误恢复 - 自动处理异常情况"
    ]
    
    print("新增功能:")
    for feature in features:
        print(f"  {feature}")
    
    print("\n使用方法:")
    print("1. 启动游戏辅助程序")
    print("2. 连接到游戏进程")
    print("3. 切换到'AI增强'选项卡")
    print("4. 勾选'启用自动攻击周围怪物'")
    print("5. 设置攻击范围和扫描间隔")
    print("6. 开始副本辅助")
    
    print("\n预期效果:")
    print("✅ 自动发现并攻击周围怪物")
    print("✅ 无需手动选择目标")
    print("✅ 高效清理副本")
    print("✅ 智能战斗决策")

def main():
    """主测试函数"""
    print("自动攻击周围怪物功能测试")
    print("=" * 60)
    
    # 执行测试
    test_results = []
    
    test_results.append(("自动攻击系统", test_auto_attack_system()))
    test_results.append(("目标扫描功能", test_target_scanning()))
    test_results.append(("攻击执行功能", test_attack_execution()))
    
    # 显示功能特性
    show_auto_attack_features()
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 自动攻击功能实现成功！")
        print("\n现在您的副本辅助具有:")
        print("🎯 自动发现周围怪物")
        print("⚔️ 智能攻击目标选择")
        print("🔄 自动目标切换")
        print("⚡ 高效战斗执行")
        print("🧠 AI驱动的战斗决策")
        
        print("\n使用建议:")
        print("1. 重新启动程序以应用新功能")
        print("2. 在副本中启用自动攻击")
        print("3. 根据需要调整攻击范围")
        print("4. 观察自动攻击效果")
    else:
        print("⚠ 部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
