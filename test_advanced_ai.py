#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高级AI功能测试脚本 - 测试技能多样化、目标优先级和战斗策略
"""

import sys
import os
import time
import logging

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_skill_diversification():
    """测试技能多样化"""
    print("=" * 60)
    print("技能多样化测试")
    print("=" * 60)
    
    try:
        from tactical_ai import TacticalAI, PlayerState, Target
        from screen_detector import ScreenDetector
        
        # 创建AI实例
        class MockMemoryReader:
            def __init__(self):
                self.process_id = 1234
        
        ai = TacticalAI(MockMemoryReader(), ScreenDetector())
        ai.debug_mode = True
        
        print("✓ 战术AI初始化成功")
        print(f"  - 技能配置数量: {len(ai.skill_priorities)}")
        
        # 显示技能配置
        print("\n技能配置详情:")
        for skill, config in ai.skill_priorities.items():
            print(f"  技能{skill}: {config['description']} (优先级:{config['priority']}, 类型:{config['type']})")
        
        # 测试不同情况下的技能选择
        test_scenarios = [
            {
                'name': '高血量高MP',
                'player': PlayerState(hp_percent=0.9, mp_percent=0.9),
                'target': Target(id=1, hp_percent=0.8, threat_level=2, distance=15.0)
            },
            {
                'name': '低血量低MP',
                'player': PlayerState(hp_percent=0.3, mp_percent=0.2),
                'target': Target(id=2, hp_percent=0.5, threat_level=3, distance=10.0)
            },
            {
                'name': 'BOSS战',
                'player': PlayerState(hp_percent=0.7, mp_percent=0.8),
                'target': Target(id=3, hp_percent=0.9, threat_level=4, distance=20.0, is_boss=True)
            },
            {
                'name': '残血目标',
                'player': PlayerState(hp_percent=0.8, mp_percent=0.6),
                'target': Target(id=4, hp_percent=0.1, threat_level=2, distance=12.0)
            }
        ]
        
        print("\n技能选择测试:")
        for scenario in test_scenarios:
            print(f"\n--- {scenario['name']} ---")
            
            for i in range(3):  # 测试3次技能选择
                skill = ai._select_best_skill(scenario['player'], scenario['target'])
                config = ai.skill_priorities.get(skill, {})
                print(f"  选择技能{skill}: {config.get('description', '未知')} (类型: {config.get('type', '未知')})")
                
                # 模拟技能使用
                ai._update_skill_usage(skill, time.time())
                time.sleep(0.1)
        
        return True
        
    except Exception as e:
        print(f"✗ 技能多样化测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_target_priority_system():
    """测试目标优先级系统"""
    print("\n" + "=" * 60)
    print("目标优先级系统测试")
    print("=" * 60)
    
    try:
        from tactical_ai import TacticalAI, Target, TargetPriority
        from screen_detector import ScreenDetector
        
        class MockMemoryReader:
            def __init__(self):
                self.process_id = 1234
        
        ai = TacticalAI(MockMemoryReader(), ScreenDetector())
        ai.debug_mode = True
        
        # 创建不同类型的测试目标
        test_targets = [
            Target(id=1, name="普通怪物", level=48, hp_percent=0.8, distance=15.0, 
                  threat_level=1, target_type="monster"),
            Target(id=2, name="精英怪物", level=50, hp_percent=0.9, distance=12.0, 
                  threat_level=3, target_type="elite", is_elite=True),
            Target(id=3, name="BOSS", level=52, hp_percent=1.0, distance=25.0, 
                  threat_level=4, target_type="boss", is_boss=True),
            Target(id=4, name="攻击我的怪", level=49, hp_percent=0.6, distance=8.0, 
                  threat_level=2, target_type="monster", is_attacking_me=True),
            Target(id=5, name="残血精英", level=51, hp_percent=0.15, distance=18.0, 
                  threat_level=3, target_type="elite", is_elite=True),
            Target(id=6, name="远距离怪物", level=47, hp_percent=0.7, distance=35.0, 
                  threat_level=2, target_type="monster")
        ]
        
        print("目标优先级计算:")
        for target in test_targets:
            priority = ai._calculate_target_priority(target)
            print(f"  {target.name}: {priority.name} (威胁:{target.threat_level}, 血量:{target.hp_percent:.1f}, 距离:{target.distance:.1f})")
        
        # 测试目标选择
        print("\n目标选择测试:")
        best_target = ai.select_best_target(test_targets)
        if best_target:
            print(f"✓ 选择最佳目标: {best_target.name} (ID:{best_target.id}, 优先级:{best_target.priority.name})")
        else:
            print("✗ 未选择到目标")
        
        # 测试目标创建
        print("\n增强目标创建测试:")
        for i in range(3):
            target = ai._create_enhanced_target(i, time.time())
            print(f"  目标{i+1}: {target.name} (类型:{target.target_type}, 威胁:{target.threat_level}, 优先级:{target.priority.name})")
        
        return True
        
    except Exception as e:
        print(f"✗ 目标优先级测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_combat_strategy_system():
    """测试战斗策略系统"""
    print("\n" + "=" * 60)
    print("战斗策略系统测试")
    print("=" * 60)
    
    try:
        from tactical_ai import TacticalAI, PlayerState, Target
        from screen_detector import ScreenDetector
        
        class MockMemoryReader:
            def __init__(self):
                self.process_id = 1234
        
        ai = TacticalAI(MockMemoryReader(), ScreenDetector())
        ai.debug_mode = True
        
        # 测试不同战斗场景
        combat_scenarios = [
            {
                'name': '紧急生存',
                'player': PlayerState(hp_percent=0.1, mp_percent=0.3),
                'target': Target(id=1, hp_percent=0.8, threat_level=3, distance=10.0)
            },
            {
                'name': '资源不足',
                'player': PlayerState(hp_percent=0.6, mp_percent=0.1),
                'target': Target(id=2, hp_percent=0.7, threat_level=2, distance=15.0)
            },
            {
                'name': 'BOSS战',
                'player': PlayerState(hp_percent=0.8, mp_percent=0.9),
                'target': Target(id=3, hp_percent=0.95, threat_level=4, distance=20.0, is_boss=True)
            },
            {
                'name': '精英战',
                'player': PlayerState(hp_percent=0.7, mp_percent=0.6),
                'target': Target(id=4, hp_percent=0.8, threat_level=3, distance=12.0, is_elite=True)
            },
            {
                'name': '优势局面',
                'player': PlayerState(hp_percent=0.9, mp_percent=0.8),
                'target': Target(id=5, hp_percent=0.4, threat_level=1, distance=8.0)
            },
            {
                'name': '收尾阶段',
                'player': PlayerState(hp_percent=0.6, mp_percent=0.5),
                'target': Target(id=6, hp_percent=0.1, threat_level=2, distance=10.0)
            }
        ]
        
        print("战斗策略决策测试:")
        for scenario in combat_scenarios:
            print(f"\n--- {scenario['name']} ---")
            
            # 分析战斗情况
            situation = ai._analyze_combat_situation(scenario['player'], scenario['target'])
            print(f"  战斗情况: {situation}")
            
            # 选择策略
            strategy = ai._select_combat_strategy(situation, scenario['player'], scenario['target'])
            print(f"  选择策略: {strategy}")
            
            # 执行决策
            action = ai.decide_combat_action(scenario['player'], scenario['target'])
            print(f"  执行行动: {action['type']} - {action.get('skill', 'N/A')} (原因: {action['reason']})")
        
        return True
        
    except Exception as e:
        print(f"✗ 战斗策略测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("高级AI功能测试")
    print("=" * 60)
    
    # 执行所有测试
    test_results = []
    
    test_results.append(("技能多样化", test_skill_diversification()))
    test_results.append(("目标优先级系统", test_target_priority_system()))
    test_results.append(("战斗策略系统", test_combat_strategy_system()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有高级AI功能测试通过！")
        print("\n新功能特性:")
        print("✅ 智能技能选择 - 根据战斗情况动态选择最佳技能")
        print("✅ 高级目标优先级 - 多因素评估目标威胁和价值")
        print("✅ 复杂战斗策略 - 9种不同战斗策略自动切换")
        print("✅ 技能连击系统 - 智能技能组合和连击奖励")
        print("✅ 资源管理 - 根据血量和MP智能调整策略")
        print("✅ 敌人类型识别 - 针对不同敌人类型采用专门策略")
    else:
        print("⚠ 部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
