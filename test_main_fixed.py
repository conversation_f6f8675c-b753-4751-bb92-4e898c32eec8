#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复后的main.py功能
"""

import sys
import os
import logging

# 设置路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有必要的导入"""
    print("=" * 50)
    print("测试模块导入...")
    print("=" * 50)
    
    try:
        # 测试核心组件导入
        from memory_reader import MemoryReader
        print("✓ memory_reader 导入成功")
        
        from battle_logic import BattleManager
        print("✓ battle_logic 导入成功")
        
        from monster_select import MonsterSelector
        print("✓ monster_select 导入成功")
        
        from input_simulator import InputSimulator
        print("✓ input_simulator 导入成功")
        
        from auto_combat_system import AutoCombatSystem
        print("✓ auto_combat_system 导入成功")
        
        from ui.main_window import MainWindow
        print("✓ ui.main_window 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_component_creation():
    """测试组件创建"""
    print("\\n" + "=" * 50)
    print("测试组件创建...")
    print("=" * 50)
    
    try:
        from memory_reader import MemoryReader
        from battle_logic import BattleManager
        from monster_select import MonsterSelector
        from input_simulator import InputSimulator
        from auto_combat_system import AutoCombatSystem
        
        # 创建组件
        memory = MemoryReader()
        print("✓ MemoryReader 创建成功")
        
        input_sim = InputSimulator()
        print("✓ InputSimulator 创建成功")
        
        monster_selector = MonsterSelector(memory)
        print("✓ MonsterSelector 创建成功")
        
        battle_manager = BattleManager(memory, input_sim, monster_selector)
        print("✓ BattleManager 创建成功")
        
        auto_combat = AutoCombatSystem(memory, input_sim, monster_selector)
        print("✓ AutoCombatSystem 创建成功")
        
        # 测试调试模式
        auto_combat.enable_debug_mode(True)
        print("✓ 调试模式启用成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 组件创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_combat_functionality():
    """测试自动战斗功能"""
    print("\\n" + "=" * 50)
    print("测试自动战斗功能...")
    print("=" * 50)
    
    try:
        from memory_reader import MemoryReader
        from input_simulator import InputSimulator
        from monster_select import MonsterSelector
        from auto_combat_system import AutoCombatSystem
        
        # 创建模拟组件
        memory = MemoryReader()
        input_sim = InputSimulator()
        monster_selector = MonsterSelector(memory)
        auto_combat = AutoCombatSystem(memory, input_sim, monster_selector)
        
        # 启用调试模式
        auto_combat.enable_debug_mode(True)
        
        # 测试配置设置
        config = {
            "attack_range": 15.0,
            "search_range": 25.0,
            "auto_loot_enabled": True
        }
        auto_combat.set_config(config)
        print("✓ 配置设置成功")
        
        # 测试目标扫描
        targets = auto_combat._scan_nearby_monsters()
        print(f"✓ 目标扫描成功: 发现 {len(targets)} 个目标")
        
        # 测试技能系统
        skills = auto_combat.skills
        print(f"✓ 技能系统加载成功: {len(skills)} 个技能")
        
        # 测试玩家信息
        player_info = auto_combat._get_player_info()
        if player_info:
            print(f"✓ 玩家信息读取成功: HP={player_info.get('hp_percent', 0)}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 自动战斗功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试修复后的main.py功能...")
    
    # 设置日志
    logging.basicConfig(level=logging.WARNING)  # 减少日志输出
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 导入测试
    if test_imports():
        success_count += 1
    
    # 测试2: 组件创建测试
    if test_component_creation():
        success_count += 1
    
    # 测试3: 功能测试
    if test_auto_combat_functionality():
        success_count += 1
    
    # 总结
    print("\\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 所有测试通过! main.py修复成功")
        print("\\n建议:")
        print("1. 将 main_fixed.py 重命名为 main.py")
        print("2. 或者将修复的代码合并到原始 main.py 中")
        print("3. 运行程序测试完整功能")
        return True
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)