"""
增强的许可证验证系统
提供更安全的激活码验证、在线验证和加密存储功能
"""

import os
import sys
import hashlib
import hmac
import json
import base64
import uuid
import winreg
import subprocess
import platform
import logging
import requests
from datetime import datetime, timedelta

# 导入加密库
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('license_system.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('license_system')

class HardwareInfo:
    """获取并处理硬件信息 - 用于生成唯一的机器ID"""
    
    @staticmethod
    def get_cpu_id():
        """获取CPU ID"""
        try:
            if platform.system() == "Windows":
                # 使用WMI获取CPU ID
                result = subprocess.check_output('wmic cpu get ProcessorId', shell=True).decode().strip()
                lines = result.split('\n')
                if len(lines) >= 2:
                    return lines[1].strip()
            return ""
        except Exception as e:
            logger.error(f"获取CPU ID失败: {str(e)}")
            return ""
    
    @staticmethod
    def get_disk_serial():
        """获取系统磁盘序列号"""
        try:
            if platform.system() == "Windows":
                # 获取C盘序列号
                result = subprocess.check_output('wmic diskdrive get SerialNumber', shell=True).decode().strip()
                lines = result.split('\n')
                if len(lines) >= 2:
                    return lines[1].strip()
            return ""
        except Exception as e:
            logger.error(f"获取磁盘序列号失败: {str(e)}")
            return ""
    
    @staticmethod
    def get_bios_serial():
        """获取BIOS序列号"""
        try:
            if platform.system() == "Windows":
                result = subprocess.check_output('wmic bios get SerialNumber', shell=True).decode().strip()
                lines = result.split('\n')
                if len(lines) >= 2:
                    return lines[1].strip()
            return ""
        except Exception as e:
            logger.error(f"获取BIOS序列号失败: {str(e)}")
            return ""
    
    @staticmethod
    def get_mac_address():
        """获取第一个网络适配器的MAC地址"""
        try:
            if platform.system() == "Windows":
                result = subprocess.check_output('getmac /fo csv /nh', shell=True).decode().strip()
                if result:
                    # 提取第一个MAC地址
                    mac = result.split(',')[0].strip('"')
                    return mac
            return ""
        except Exception as e:
            logger.error(f"获取MAC地址失败: {str(e)}")
            return ""
    
    @staticmethod
    def get_machine_guid():
        """获取Windows的MachineGUID"""
        try:
            if platform.system() == "Windows":
                reg_path = r"SOFTWARE\Microsoft\Cryptography"
                reg_key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path)
                machine_guid, _ = winreg.QueryValueEx(reg_key, "MachineGuid")
                winreg.CloseKey(reg_key)
                return machine_guid
            return ""
        except Exception as e:
            logger.error(f"获取MachineGUID失败: {str(e)}")
            return ""
    
    @staticmethod
    def generate_machine_id():
        """生成基于多种硬件信息的唯一机器ID"""
        # 收集硬件信息
        cpu_id = HardwareInfo.get_cpu_id()
        disk_serial = HardwareInfo.get_disk_serial()
        bios_serial = HardwareInfo.get_bios_serial()
        mac_address = HardwareInfo.get_mac_address()
        machine_guid = HardwareInfo.get_machine_guid()
        
        # 组合硬件信息
        hardware_str = f"{cpu_id}|{disk_serial}|{bios_serial}|{mac_address}|{machine_guid}"
        
        # 生成哈希
        machine_hash = hashlib.sha256(hardware_str.encode()).hexdigest()
        
        # 返回格式化的机器ID (8-4-4-4-12格式)
        return f"{machine_hash[:8]}-{machine_hash[8:12]}-{machine_hash[12:16]}-{machine_hash[16:20]}-{machine_hash[20:32]}"


class EnhancedLicenseChecker:
    """增强的许可证检查器"""
    
    SECRET_KEY = "3fa85f64-5717-4562-b3fc-2c963f66afa6"  # 与原系统相同的密钥
    API_ENDPOINT = "https://api.yourdomain.com/validate"  # 替换为实际的API端点
    BACKUP_ENDPOINTS = [  # 备用验证服务器
        "https://backup1.yourdomain.com/validate",
        "https://backup2.yourdomain.com/validate"
    ]
    LICENSE_FILE = "enhanced_license.dat"  # 新的许可证文件名
    
    @staticmethod
    def generate_encryption_key(machine_id):
        """基于机器ID生成加密密钥"""
        # 使用PBKDF2生成密钥
        salt = EnhancedLicenseChecker.SECRET_KEY.encode()
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(machine_id.encode()))
        return key
    
    @staticmethod
    def encrypt_data(data, key):
        """使用Fernet对数据进行加密"""
        f = Fernet(key)
        return f.encrypt(json.dumps(data).encode())
    
    @staticmethod
    def decrypt_data(encrypted_data, key):
        """解密数据"""
        try:
            f = Fernet(key)
            return json.loads(f.decrypt(encrypted_data).decode())
        except Exception as e:
            logger.error(f"解密失败: {str(e)}")
            return None
    
    @staticmethod
    def generate_signature(data, key):
        """生成HMAC签名以验证数据完整性"""
        message = json.dumps(data, sort_keys=True).encode()
        signature = hmac.new(key.encode(), message, hashlib.sha256).hexdigest()
        return signature
    
    @staticmethod
    def online_verify(activation_key, machine_id):
        """在线验证激活码"""
        try:
            # 准备验证数据
            verification_data = {
                "activation_key": activation_key,
                "machine_id": machine_id,
                "timestamp": datetime.now().isoformat(),
                "app_version": "1.0.0"  # 替换为实际版本号
            }
            
            # 添加签名
            verification_data["signature"] = EnhancedLicenseChecker.generate_signature(
                verification_data, EnhancedLicenseChecker.SECRET_KEY
            )
            
            # 尝试主服务器
            try:
                response = requests.post(
                    EnhancedLicenseChecker.API_ENDPOINT, 
                    json=verification_data,
                    timeout=5  # 5秒超时
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("valid", False):
                        return True, result.get("message", "验证成功")
                    else:
                        return False, result.get("message", "验证失败")
            except requests.RequestException:
                # 主服务器失败，尝试备用服务器
                for backup_url in EnhancedLicenseChecker.BACKUP_ENDPOINTS:
                    try:
                        response = requests.post(
                            backup_url, 
                            json=verification_data,
                            timeout=5
                        )
                        
                        if response.status_code == 200:
                            result = response.json()
                            if result.get("valid", False):
                                return True, result.get("message", "验证成功")
                            else:
                                return False, result.get("message", "验证失败")
                    except requests.RequestException:
                        continue
            
            # 所有服务器都失败，使用离线模式
            logger.warning("无法连接到验证服务器，使用离线验证模式")
            return False, "无法连接到验证服务器"
            
        except Exception as e:
            logger.error(f"在线验证出错: {str(e)}")
            return False, f"在线验证出错: {str(e)}"
    
    @staticmethod
    def verify_activation_key(activation_key, machine_id):
        """验证激活码是否有效 - 增强版验证逻辑"""
        try:
            # 规范化激活码格式
            activation_key = activation_key.upper().strip()
            
            # 解析激活码
            parts = activation_key.split('-')
            if len(parts) != 5 or any(len(part) != 5 for part in parts):
                return False, "无效的激活码格式", None
            
            # 提取日期信息 (YYMMDD)
            date_code = parts[1][:2] + parts[2][:2] + parts[3][:2]
            
            try:
                expiry_year = int("20" + date_code[:2])  # 假设年份是 20xx
                expiry_month = int(date_code[2:4])
                expiry_day = int(date_code[4:6])
                expiry_date = datetime(expiry_year, expiry_month, expiry_day, 23, 59, 59)
            except ValueError:
                return False, "激活码中的日期无效", None
            
            # 检查是否过期
            if datetime.now() > expiry_date:
                return False, f"激活码已过期 (有效期至: {expiry_date.strftime('%Y-%m-%d')})", expiry_date
            
            # 验证激活码与机器ID的绑定
            # 计算预期的机器ID哈希
            expected_hash = hashlib.sha256((machine_id + EnhancedLicenseChecker.SECRET_KEY).encode()).hexdigest()[:8]
            actual_hash = parts[4][:8]
            
            # 验证机器ID哈希
            if expected_hash.upper() != actual_hash.upper():
                return False, "激活码与当前机器不匹配", None
            
            # 尝试在线验证
            try:
                online_valid, online_message = EnhancedLicenseChecker.online_verify(activation_key, machine_id)
                if online_valid:
                    return True, f"激活码有效，到期时间: {expiry_date.strftime('%Y-%m-%d')}", expiry_date
                else:
                    # 如果在线验证失败但本地验证通过，仍然接受但记录警告
                    logger.warning(f"在线验证失败: {online_message}，使用本地验证")
            except Exception as e:
                logger.warning(f"在线验证异常: {str(e)}，使用本地验证")
                
            # 本地验证通过
            return True, f"激活码有效 (本地验证)，到期时间: {expiry_date.strftime('%Y-%m-%d')}", expiry_date
            
        except Exception as e:
            import traceback
            logger.error(traceback.format_exc())
            return False, f"验证过程出错: {str(e)}", None
    
    @staticmethod
    def check_activation():
        """检查软件是否已激活 - 增强版"""
        try:
            # 检查是否存在激活文件
            if not os.path.exists(EnhancedLicenseChecker.LICENSE_FILE):
                return False, "未找到激活信息", None
            
            # 获取当前机器ID
            current_machine_id = HardwareInfo.generate_machine_id()
            
            # 基于机器ID生成解密密钥
            key = EnhancedLicenseChecker.generate_encryption_key(current_machine_id)
            
            # 读取加密的激活信息
            try:
                with open(EnhancedLicenseChecker.LICENSE_FILE, "rb") as f:
                    encrypted_data = f.read()
                
                # 尝试解密
                activation_info = EnhancedLicenseChecker.decrypt_data(encrypted_data, key)
                
                # 如果解密失败，可能是机器ID变化或文件被篡改
                if activation_info is None:
                    return False, "激活信息无效或已被篡改", None
                
                # 检查激活信息完整性
                if not all(k in activation_info for k in ["machine_id", "activation_key", "signature"]):
                    return False, "激活信息不完整", None
                
                # 验证签名
                stored_signature = activation_info.pop("signature")
                calculated_signature = EnhancedLicenseChecker.generate_signature(
                    activation_info, EnhancedLicenseChecker.SECRET_KEY
                )
                
                if stored_signature != calculated_signature:
                    return False, "激活信息签名无效", None
                
                # 验证机器ID
                if current_machine_id != activation_info["machine_id"]:
                    return False, "激活信息与当前机器不匹配", None
                
                # 检查过期时间
                expiry_date = None
                if "expiry_date" in activation_info and activation_info["expiry_date"]:
                    expiry_date = datetime.fromisoformat(activation_info["expiry_date"])
                    
                    # 检查是否已过期
                    if datetime.now() > expiry_date:
                        return False, f"激活码已过期 (有效期至: {expiry_date.strftime('%Y-%m-%d')})", expiry_date
                
                # 尝试在线验证
                try:
                    online_valid, online_message = EnhancedLicenseChecker.online_verify(
                        activation_info["activation_key"], current_machine_id
                    )
                    
                    # 如果在线验证失败但本地验证通过，记录警告但仍然接受
                    if not online_valid:
                        logger.warning(f"在线验证失败: {online_message}，使用本地验证")
                except Exception as e:
                    logger.warning(f"在线验证异常: {str(e)}，使用本地验证")
                
                # 验证成功
                return True, "软件已激活", expiry_date
                
            except Exception as e:
                logger.error(f"读取激活信息出错: {str(e)}")
                return False, f"读取激活信息出错: {str(e)}", None
            
        except Exception as e:
            logger.error(f"验证激活状态出错: {str(e)}")
            return False, f"验证激活状态出错: {str(e)}", None
    
    @staticmethod
    def activate(activation_key):
        """激活软件 - 增强版"""
        try:
            # 获取当前机器ID
            machine_id = HardwareInfo.generate_machine_id()
            
            # 验证激活码
            is_valid, message, expiry_date = EnhancedLicenseChecker.verify_activation_key(
                activation_key, machine_id
            )
            
            if not is_valid:
                return False, message
            
            # 保存激活信息
            activation_info = {
                "machine_id": machine_id,
                "activation_key": activation_key,
                "activated_date": datetime.now().isoformat(),
                "expiry_date": expiry_date.isoformat() if expiry_date else None,
                "last_verified": datetime.now().isoformat()
            }
            
            # 添加签名
            activation_info["signature"] = EnhancedLicenseChecker.generate_signature(
                activation_info, EnhancedLicenseChecker.SECRET_KEY
            )
            
            # 基于机器ID生成加密密钥
            key = EnhancedLicenseChecker.generate_encryption_key(machine_id)
            
            # 加密保存
            encrypted_data = EnhancedLicenseChecker.encrypt_data(activation_info, key)
            
            with open(EnhancedLicenseChecker.LICENSE_FILE, "wb") as f:
                f.write(encrypted_data)
            
            return True, f"软件激活成功! 有效期至: {expiry_date.strftime('%Y-%m-%d')}"
            
        except Exception as e:
            logger.error(f"激活过程出错: {str(e)}")
            return False, f"激活过程出错: {str(e)}"

    @staticmethod
    def periodic_check():
        """定期检查激活状态，应该由定时器调用"""
        try:
            # 获取当前机器ID
            machine_id = HardwareInfo.generate_machine_id()
            
            # 检查是否存在激活文件
            if not os.path.exists(EnhancedLicenseChecker.LICENSE_FILE):
                logger.warning("定期检查：未找到激活信息")
                return False, "未找到激活信息", None
            
            # 基于机器ID生成解密密钥
            key = EnhancedLicenseChecker.generate_encryption_key(machine_id)
            
            # 读取加密的激活信息
            try:
                with open(EnhancedLicenseChecker.LICENSE_FILE, "rb") as f:
                    encrypted_data = f.read()
                
                # 尝试解密
                activation_info = EnhancedLicenseChecker.decrypt_data(encrypted_data, key)
                
                if activation_info is None:
                    logger.warning("定期检查：激活信息无效或已被篡改")
                    return False, "激活信息无效或已被篡改", None
                
                # 检查激活信息完整性
                if not all(k in activation_info for k in ["machine_id", "activation_key", "signature"]):
                    logger.warning("定期检查：激活信息不完整")
                    return False, "激活信息不完整", None
                
                # 验证签名
                stored_signature = activation_info.pop("signature")
                calculated_signature = EnhancedLicenseChecker.generate_signature(
                    activation_info, EnhancedLicenseChecker.SECRET_KEY
                )
                
                if stored_signature != calculated_signature:
                    logger.warning("定期检查：激活信息签名无效")
                    return False, "激活信息签名无效", None
                
                # 验证机器ID
                if machine_id != activation_info["machine_id"]:
                    logger.warning("定期检查：激活信息与当前机器不匹配")
                    return False, "激活信息与当前机器不匹配", None
                
                # 检查过期时间
                expiry_date = None
                if "expiry_date" in activation_info and activation_info["expiry_date"]:
                    expiry_date = datetime.fromisoformat(activation_info["expiry_date"])
                    
                    # 检查是否已过期
                    if datetime.now() > expiry_date:
                        logger.warning(f"定期检查：激活码已过期 (有效期至: {expiry_date.strftime('%Y-%m-%d')})")
                        return False, f"激活码已过期 (有效期至: {expiry_date.strftime('%Y-%m-%d')})", expiry_date
                
                # 尝试在线验证
                try:
                    online_valid, online_message = EnhancedLicenseChecker.online_verify(
                        activation_info["activation_key"], machine_id
                    )
                    
                    if online_valid:
                        # 更新上次验证时间
                        activation_info["last_verified"] = datetime.now().isoformat()
                        activation_info["signature"] = stored_signature  # 恢复签名
                        
                        # 重新加密保存
                        encrypted_data = EnhancedLicenseChecker.encrypt_data(activation_info, key)
                        
                        with open(EnhancedLicenseChecker.LICENSE_FILE, "wb") as f:
                            f.write(encrypted_data)
                            
                        logger.info("定期检查：在线验证成功")
                    else:
                        logger.warning(f"定期检查：在线验证失败 - {online_message}")
                        # 如果在线验证失败但本地验证通过，记录警告但仍然接受
                        if "last_verified" in activation_info:
                            last_verified = datetime.fromisoformat(activation_info["last_verified"])
                            # 如果超过7天未能在线验证，则视为无效
                            if (datetime.now() - last_verified).days > 7:
                                logger.error("定期检查：超过7天未能在线验证，激活无效")
                                return False, "超过7天未能在线验证，请连接网络重新验证", expiry_date
                except Exception as e:
                    logger.warning(f"定期检查：在线验证异常 - {str(e)}")
                
                # 验证成功
                return True, "软件已激活", expiry_date
                
            except Exception as e:
                logger.error(f"定期检查：读取激活信息出错 - {str(e)}")
                return False, f"读取激活信息出错: {str(e)}", None
            
        except Exception as e:
            logger.error(f"定期检查：验证激活状态出错 - {str(e)}")
            return False, f"验证激活状态出错: {str(e)}", None


# 生成激活码的辅助函数（仅供开发者使用）
def generate_activation_code(machine_id, expiry_date):
    """
    生成绑定到特定机器ID的激活码
    
    参数:
        machine_id: 机器ID
        expiry_date: 过期日期，格式为datetime对象
    
    返回:
        激活码字符串
    """
    try:
        # 生成随机部分
        random_part = uuid.uuid4().hex[:5].upper()
        
        # 日期编码 (YYMMDD)
        date_code = expiry_date.strftime("%y%m%d")
        year_code = date_code[:2]
        month_code = date_code[2:4]
        day_code = date_code[4:6]
        
        # 机器ID哈希
        machine_hash = hashlib.sha256(
            (machine_id + EnhancedLicenseChecker.SECRET_KEY).encode()
        ).hexdigest()[:8].upper()
        
        # 构建激活码
        part1 = random_part
        part2 = f"{year_code}ABC"
        part3 = f"{month_code}DEF"
        part4 = f"{day_code}GHI"
        part5 = f"{machine_hash[:5]}"
        
        activation_code = f"{part1}-{part2}-{part3}-{part4}-{part5}"
        return activation_code
        
    except Exception as e:
        logger.error(f"生成激活码出错: {str(e)}")
        return None


# 示例：如何在主程序中使用增强的许可证系统
if __name__ == "__main__":
    # 测试硬件ID生成
    machine_id = HardwareInfo.generate_machine_id()
    print(f"机器ID: {machine_id}")
    
    # 测试生成激活码
    expiry_date = datetime.now() + timedelta(days=365)  # 一年后过期
    activation_code = generate_activation_code(machine_id, expiry_date)
    print(f"生成的激活码: {activation_code}")
    print(f"过期时间: {expiry_date.strftime('%Y-%m-%d')}")
    
    # 测试验证激活码
    is_valid, message, expiry = EnhancedLicenseChecker.verify_activation_key(
        activation_code, machine_id
    )
    print(f"验证结果: {is_valid}, 消息: {message}")
    
    # 测试激活
    if is_valid:
        success, activate_message = EnhancedLicenseChecker.activate(activation_code)
        print(f"激活结果: {success}, 消息: {activate_message}")
        
        # 测试检查激活状态
        is_activated, check_message, _ = EnhancedLicenseChecker.check_activation()
        print(f"检查激活状态: {is_activated}, 消息: {check_message}")
