"""
代码混淆工具
用于增加软件的逆向工程难度
"""

import os
import sys
import subprocess
import random
import string
import logging
import argparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('obfuscate.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('obfuscate')

def check_pyarmor_installed():
    """检查是否已安装PyArmor"""
    try:
        subprocess.run(["pyarmor", "--version"], capture_output=True, text=True)
        return True
    except FileNotFoundError:
        return False

def install_pyarmor():
    """安装PyArmor"""
    try:
        logger.info("正在安装PyArmor...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyarmor"], check=True)
        logger.info("PyArmor安装成功")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"安装PyArmor失败: {str(e)}")
        return False

def generate_random_string(length=8):
    """生成随机字符串"""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(length))

def obfuscate_file(file_path, output_dir=None, advanced=False):
    """使用PyArmor对文件进行混淆"""
    if not os.path.exists(file_path):
        logger.error(f"文件不存在: {file_path}")
        return False
    
    if not check_pyarmor_installed():
        logger.warning("未安装PyArmor，正在尝试安装...")
        if not install_pyarmor():
            logger.error("无法安装PyArmor，混淆失败")
            return False
    
    try:
        # 确定输出目录
        if output_dir is None:
            output_dir = os.path.join(os.path.dirname(file_path), "dist")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 基本混淆命令
        cmd = ["pyarmor", "obfuscate"]
        
        # 高级混淆选项
        if advanced:
            # 添加高级混淆选项
            cmd.extend([
                "--advanced", "2",  # 高级模式
                "--restrict", "0",  # 限制模式
                "--bootstrap", "2",  # 引导模式
                "--obf-code", "2",  # 代码混淆级别
                "--obf-mod", "2",   # 模块混淆级别
                "--wrap-mode", "1", # 包装模式
                "--protection", "1" # 保护模式
            ])
            
            # 添加随机名称
            cmd.extend(["--name", f"obf_{generate_random_string()}"])
        
        # 添加输出目录
        cmd.extend(["-O", output_dir])
        
        # 添加目标文件
        cmd.append(file_path)
        
        # 执行混淆
        logger.info(f"正在混淆文件: {file_path}")
        logger.info(f"混淆命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"混淆成功，输出目录: {output_dir}")
            logger.info(result.stdout)
            return True
        else:
            logger.error(f"混淆失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"混淆过程出错: {str(e)}")
        return False

def obfuscate_project(project_dir, output_dir=None, advanced=False, exclude=None):
    """混淆整个项目"""
    if not os.path.isdir(project_dir):
        logger.error(f"项目目录不存在: {project_dir}")
        return False
    
    if not check_pyarmor_installed():
        logger.warning("未安装PyArmor，正在尝试安装...")
        if not install_pyarmor():
            logger.error("无法安装PyArmor，混淆失败")
            return False
    
    try:
        # 确定输出目录
        if output_dir is None:
            output_dir = os.path.join(project_dir, "dist")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 基本混淆命令
        cmd = ["pyarmor", "obfuscate"]
        
        # 高级混淆选项
        if advanced:
            # 添加高级混淆选项
            cmd.extend([
                "--advanced", "2",  # 高级模式
                "--restrict", "0",  # 限制模式
                "--bootstrap", "2",  # 引导模式
                "--obf-code", "2",  # 代码混淆级别
                "--obf-mod", "2",   # 模块混淆级别
                "--wrap-mode", "1", # 包装模式
                "--protection", "1" # 保护模式
            ])
            
            # 添加随机名称
            cmd.extend(["--name", f"obf_{generate_random_string()}"])
        
        # 添加输出目录
        cmd.extend(["-O", output_dir])
        
        # 添加排除文件
        if exclude:
            for item in exclude:
                cmd.extend(["--exclude", item])
        
        # 添加项目目录
        cmd.append(os.path.join(project_dir, "*.py"))
        
        # 执行混淆
        logger.info(f"正在混淆项目: {project_dir}")
        logger.info(f"混淆命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"项目混淆成功，输出目录: {output_dir}")
            logger.info(result.stdout)
            return True
        else:
            logger.error(f"项目混淆失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"混淆过程出错: {str(e)}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Python代码混淆工具")
    parser.add_argument("target", help="要混淆的文件或目录")
    parser.add_argument("-o", "--output", help="输出目录")
    parser.add_argument("-a", "--advanced", action="store_true", help="使用高级混淆选项")
    parser.add_argument("-e", "--exclude", nargs="+", help="排除的文件或目录")
    
    args = parser.parse_args()
    
    if os.path.isfile(args.target):
        # 混淆单个文件
        success = obfuscate_file(args.target, args.output, args.advanced)
    elif os.path.isdir(args.target):
        # 混淆整个项目
        success = obfuscate_project(args.target, args.output, args.advanced, args.exclude)
    else:
        logger.error(f"目标不存在: {args.target}")
        success = False
    
    if success:
        print("混淆成功！")
        return 0
    else:
        print("混淆失败，请查看日志了解详情。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
