"""
动态加密系统
实现定期更新的混淆和加密算法
"""

import os
import sys
import time
import random
import hashlib
import logging
import threading
import json
import base64
import uuid
from datetime import datetime, timedelta
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import serialization

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dynamic_encryption.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('dynamic_encryption')


class EncryptionAlgorithm:
    """加密算法基类"""
    
    def __init__(self, key=None):
        self.key = key if key else self.generate_key()
    
    def generate_key(self):
        """生成加密密钥"""
        raise NotImplementedError("子类必须实现此方法")
    
    def encrypt(self, data):
        """加密数据"""
        raise NotImplementedError("子类必须实现此方法")
    
    def decrypt(self, encrypted_data):
        """解密数据"""
        raise NotImplementedError("子类必须实现此方法")
    
    def get_algorithm_info(self):
        """获取算法信息"""
        return {
            "algorithm": self.__class__.__name__,
            "key": base64.b64encode(self.key).decode() if isinstance(self.key, bytes) else self.key
        }


class FernetEncryption(EncryptionAlgorithm):
    """Fernet对称加密算法"""
    
    def generate_key(self):
        """生成Fernet密钥"""
        return Fernet.generate_key()
    
    def encrypt(self, data):
        """加密数据"""
        if not isinstance(data, bytes):
            data = data.encode() if isinstance(data, str) else json.dumps(data).encode()
        
        f = Fernet(self.key)
        return f.encrypt(data)
    
    def decrypt(self, encrypted_data):
        """解密数据"""
        f = Fernet(self.key)
        return f.decrypt(encrypted_data)


class AESEncryption(EncryptionAlgorithm):
    """AES对称加密算法"""
    
    def __init__(self, key=None, iv=None):
        self.iv = iv if iv else os.urandom(16)  # AES块大小为16字节
        super().__init__(key)
    
    def generate_key(self):
        """生成AES密钥"""
        return os.urandom(32)  # AES-256
    
    def encrypt(self, data):
        """加密数据"""
        if not isinstance(data, bytes):
            data = data.encode() if isinstance(data, str) else json.dumps(data).encode()
        
        # 填充数据
        padder = padding.PKCS7(algorithms.AES.block_size).padder()
        padded_data = padder.update(data) + padder.finalize()
        
        # 加密
        cipher = Cipher(algorithms.AES(self.key), modes.CBC(self.iv))
        encryptor = cipher.encryptor()
        encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
        
        # 返回IV和加密数据
        return self.iv + encrypted_data
    
    def decrypt(self, encrypted_data):
        """解密数据"""
        # 提取IV
        iv = encrypted_data[:16]
        ciphertext = encrypted_data[16:]
        
        # 解密
        cipher = Cipher(algorithms.AES(self.key), modes.CBC(iv))
        decryptor = cipher.decryptor()
        padded_data = decryptor.update(ciphertext) + decryptor.finalize()
        
        # 去除填充
        unpadder = padding.PKCS7(algorithms.AES.block_size).unpadder()
        return unpadder.update(padded_data) + unpadder.finalize()
    
    def get_algorithm_info(self):
        """获取算法信息"""
        info = super().get_algorithm_info()
        info["iv"] = base64.b64encode(self.iv).decode()
        return info


class RSAEncryption(EncryptionAlgorithm):
    """RSA非对称加密算法"""
    
    def __init__(self, key=None):
        self.public_key = None
        self.private_key = None
        super().__init__(key)
    
    def generate_key(self):
        """生成RSA密钥对"""
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048
        )
        public_key = private_key.public_key()
        
        # 序列化私钥
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        # 序列化公钥
        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        self.public_key = public_pem
        self.private_key = private_pem
        
        return {
            "public_key": public_pem,
            "private_key": private_pem
        }
    
    def encrypt(self, data):
        """加密数据（使用公钥）"""
        if not isinstance(data, bytes):
            data = data.encode() if isinstance(data, str) else json.dumps(data).encode()
        
        # 加载公钥
        public_key = serialization.load_pem_public_key(
            self.key["public_key"] if isinstance(self.key, dict) else self.public_key
        )
        
        # RSA加密有大小限制，这里使用分块加密
        chunk_size = 190  # RSA-2048的最大加密块大小约为245字节，留出一些余量
        chunks = [data[i:i+chunk_size] for i in range(0, len(data), chunk_size)]
        
        encrypted_chunks = []
        for chunk in chunks:
            encrypted_chunk = public_key.encrypt(
                chunk,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            encrypted_chunks.append(encrypted_chunk)
        
        # 返回加密块数量和加密数据
        return json.dumps({
            "chunks": len(encrypted_chunks),
            "data": [base64.b64encode(chunk).decode() for chunk in encrypted_chunks]
        }).encode()
    
    def decrypt(self, encrypted_data):
        """解密数据（使用私钥）"""
        # 解析加密数据
        encrypted_json = json.loads(encrypted_data.decode())
        chunks = encrypted_json["chunks"]
        encrypted_chunks = [base64.b64decode(chunk) for chunk in encrypted_json["data"]]
        
        # 加载私钥
        private_key = serialization.load_pem_private_key(
            self.key["private_key"] if isinstance(self.key, dict) else self.private_key,
            password=None
        )
        
        # 解密所有块
        decrypted_chunks = []
        for chunk in encrypted_chunks:
            decrypted_chunk = private_key.decrypt(
                chunk,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            decrypted_chunks.append(decrypted_chunk)
        
        # 合并解密后的数据
        return b''.join(decrypted_chunks)
    
    def get_algorithm_info(self):
        """获取算法信息"""
        return {
            "algorithm": self.__class__.__name__,
            "public_key": base64.b64encode(self.public_key).decode() if self.public_key else None
        }


class HybridEncryption(EncryptionAlgorithm):
    """混合加密算法（RSA + AES）"""
    
    def __init__(self, key=None):
        self.rsa = RSAEncryption()
        self.aes = None
        super().__init__(key)
    
    def generate_key(self):
        """生成混合加密密钥"""
        return self.rsa.generate_key()
    
    def encrypt(self, data):
        """加密数据"""
        if not isinstance(data, bytes):
            data = data.encode() if isinstance(data, str) else json.dumps(data).encode()
        
        # 生成随机AES密钥
        aes_key = os.urandom(32)
        self.aes = AESEncryption(key=aes_key)
        
        # 使用AES加密数据
        aes_encrypted = self.aes.encrypt(data)
        
        # 使用RSA加密AES密钥和IV
        key_info = {
            "key": base64.b64encode(aes_key).decode(),
            "iv": base64.b64encode(self.aes.iv).decode()
        }
        rsa_encrypted = self.rsa.encrypt(json.dumps(key_info))
        
        # 返回混合加密结果
        return json.dumps({
            "rsa": base64.b64encode(rsa_encrypted).decode(),
            "aes": base64.b64encode(aes_encrypted).decode()
        }).encode()
    
    def decrypt(self, encrypted_data):
        """解密数据"""
        # 解析混合加密数据
        encrypted_json = json.loads(encrypted_data.decode())
        rsa_encrypted = base64.b64decode(encrypted_json["rsa"])
        aes_encrypted = base64.b64decode(encrypted_json["aes"])
        
        # 使用RSA解密AES密钥和IV
        key_info_json = self.rsa.decrypt(rsa_encrypted)
        key_info = json.loads(key_info_json)
        
        aes_key = base64.b64decode(key_info["key"])
        aes_iv = base64.b64decode(key_info["iv"])
        
        # 使用AES解密数据
        aes = AESEncryption(key=aes_key, iv=aes_iv)
        return aes.decrypt(aes_encrypted)
    
    def get_algorithm_info(self):
        """获取算法信息"""
        return {
            "algorithm": self.__class__.__name__,
            "rsa": self.rsa.get_algorithm_info()
        }


class CustomEncryption(EncryptionAlgorithm):
    """自定义多层加密算法"""
    
    def __init__(self, key=None, layers=3):
        self.layers = layers
        self.layer_keys = []
        self.layer_algorithms = []
        super().__init__(key)
    
    def generate_key(self):
        """生成多层加密密钥"""
        master_key = os.urandom(32)
        
        # 为每一层生成不同的密钥
        self.layer_keys = []
        for i in range(self.layers):
            # 使用PBKDF2派生密钥
            salt = hashlib.sha256(f"layer{i}".encode()).digest()
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000 + i * 10000
            )
            layer_key = kdf.derive(master_key)
            self.layer_keys.append(layer_key)
        
        # 为每一层选择不同的算法
        self.layer_algorithms = []
        for i in range(self.layers):
            if i % 3 == 0:
                self.layer_algorithms.append(FernetEncryption(key=self.layer_keys[i]))
            elif i % 3 == 1:
                self.layer_algorithms.append(AESEncryption(key=self.layer_keys[i]))
            else:
                # 最后一层使用简单的XOR加密
                self.layer_algorithms.append("XOR")
        
        return master_key
    
    def _xor_encrypt(self, data, key):
        """XOR加密"""
        # 扩展密钥以匹配数据长度
        extended_key = b''
        for i in range(0, len(data), len(key)):
            extended_key += key[:min(len(key), len(data) - i)]
        
        # XOR操作
        return bytes(a ^ b for a, b in zip(data, extended_key))
    
    def encrypt(self, data):
        """多层加密数据"""
        if not isinstance(data, bytes):
            data = data.encode() if isinstance(data, str) else json.dumps(data).encode()
        
        encrypted = data
        layer_info = []
        
        # 逐层加密
        for i, algorithm in enumerate(self.layer_algorithms):
            if algorithm == "XOR":
                encrypted = self._xor_encrypt(encrypted, self.layer_keys[i])
                layer_info.append({
                    "type": "XOR",
                    "key_hash": hashlib.sha256(self.layer_keys[i]).hexdigest()[:8]
                })
            else:
                encrypted = algorithm.encrypt(encrypted)
                layer_info.append({
                    "type": algorithm.__class__.__name__,
                    "key_hash": hashlib.sha256(algorithm.key).hexdigest()[:8]
                })
        
        # 添加随机填充
        padding_size = random.randint(16, 64)
        padding = os.urandom(padding_size)
        
        # 添加元数据
        metadata = {
            "layers": self.layers,
            "layer_info": layer_info,
            "padding_size": padding_size,
            "timestamp": datetime.now().isoformat()
        }
        metadata_json = json.dumps(metadata).encode()
        metadata_size = len(metadata_json)
        
        # 组合最终加密数据
        final_data = metadata_size.to_bytes(4, byteorder='big') + metadata_json + encrypted + padding
        
        return final_data
    
    def decrypt(self, encrypted_data):
        """多层解密数据"""
        # 提取元数据
        metadata_size = int.from_bytes(encrypted_data[:4], byteorder='big')
        metadata_json = encrypted_data[4:4+metadata_size]
        metadata = json.loads(metadata_json)
        
        # 提取加密数据
        padding_size = metadata["padding_size"]
        encrypted = encrypted_data[4+metadata_size:-padding_size]
        
        # 逐层解密（反向）
        for i in range(metadata["layers"] - 1, -1, -1):
            algorithm = self.layer_algorithms[i]
            if algorithm == "XOR":
                encrypted = self._xor_encrypt(encrypted, self.layer_keys[i])
            else:
                encrypted = algorithm.decrypt(encrypted)
        
        return encrypted
    
    def get_algorithm_info(self):
        """获取算法信息"""
        layer_info = []
        for i, algorithm in enumerate(self.layer_algorithms):
            if algorithm == "XOR":
                layer_info.append({
                    "type": "XOR",
                    "key_hash": hashlib.sha256(self.layer_keys[i]).hexdigest()[:8]
                })
            else:
                layer_info.append({
                    "type": algorithm.__class__.__name__,
                    "key_hash": hashlib.sha256(algorithm.key).hexdigest()[:8]
                })
        
        return {
            "algorithm": self.__class__.__name__,
            "layers": self.layers,
            "layer_info": layer_info
        }


class DynamicEncryptionSystem:
    """动态加密系统"""
    
    def __init__(self, update_interval=86400):  # 默认24小时更新一次
        self.current_algorithm = None
        self.algorithm_history = []
        self.update_interval = update_interval
        self.last_update = datetime.now()
        self.update_lock = threading.Lock()
        self.initialize()
    
    def initialize(self):
        """初始化加密系统"""
        self.update_algorithm()
    
    def update_algorithm(self):
        """更新加密算法"""
        with self.update_lock:
            # 选择新的加密算法
            algorithm_choices = [
                FernetEncryption,
                AESEncryption,
                HybridEncryption,
                CustomEncryption
            ]
            
            # 随机选择算法
            algorithm_class = random.choice(algorithm_choices)
            
            # 如果选择了CustomEncryption，随机选择层数
            if algorithm_class == CustomEncryption:
                layers = random.randint(2, 5)
                new_algorithm = algorithm_class(layers=layers)
            else:
                new_algorithm = algorithm_class()
            
            # 保存旧算法到历史记录
            if self.current_algorithm:
                self.algorithm_history.append({
                    "algorithm": self.current_algorithm,
                    "valid_until": datetime.now()
                })
                
                # 只保留最近5个算法
                if len(self.algorithm_history) > 5:
                    self.algorithm_history = self.algorithm_history[-5:]
            
            # 更新当前算法
            self.current_algorithm = new_algorithm
            self.last_update = datetime.now()
            
            logger.info(f"加密算法已更新: {new_algorithm.__class__.__name__}")
    
    def encrypt(self, data):
        """加密数据"""
        # 检查是否需要更新算法
        if (datetime.now() - self.last_update).total_seconds() > self.update_interval:
            self.update_algorithm()
        
        # 加密数据
        encrypted = self.current_algorithm.encrypt(data)
        
        # 添加算法标识符
        algorithm_id = str(uuid.uuid4())
        metadata = {
            "algorithm_id": algorithm_id,
            "timestamp": datetime.now().isoformat(),
            "algorithm_info": self.current_algorithm.get_algorithm_info()
        }
        metadata_json = json.dumps(metadata).encode()
        metadata_size = len(metadata_json)
        
        # 组合最终加密数据
        final_data = metadata_size.to_bytes(4, byteorder='big') + metadata_json + encrypted
        
        return final_data
    
    def decrypt(self, encrypted_data):
        """解密数据"""
        # 提取元数据
        metadata_size = int.from_bytes(encrypted_data[:4], byteorder='big')
        metadata_json = encrypted_data[4:4+metadata_size]
        metadata = json.loads(metadata_json)
        
        # 提取加密数据
        encrypted = encrypted_data[4+metadata_size:]
        
        # 获取算法信息
        algorithm_info = metadata["algorithm_info"]
        algorithm_type = algorithm_info["algorithm"]
        
        # 选择解密算法
        if algorithm_info["algorithm"] == self.current_algorithm.__class__.__name__:
            # 使用当前算法解密
            return self.current_algorithm.decrypt(encrypted)
        else:
            # 在历史算法中查找
            for history in self.algorithm_history:
                if history["algorithm"].__class__.__name__ == algorithm_type:
                    return history["algorithm"].decrypt(encrypted)
            
            # 如果找不到算法，尝试重新创建
            if algorithm_type == "FernetEncryption":
                key = base64.b64decode(algorithm_info["key"])
                algorithm = FernetEncryption(key=key)
            elif algorithm_type == "AESEncryption":
                key = base64.b64decode(algorithm_info["key"])
                iv = base64.b64decode(algorithm_info["iv"])
                algorithm = AESEncryption(key=key, iv=iv)
            elif algorithm_type == "RSAEncryption":
                # RSA需要私钥解密，如果没有私钥则无法解密
                logger.error("无法解密RSA加密的数据：缺少私钥")
                return None
            elif algorithm_type == "HybridEncryption":
                # 混合加密也需要私钥
                logger.error("无法解密混合加密的数据：缺少私钥")
                return None
            elif algorithm_type == "CustomEncryption":
                # 自定义多层加密需要所有层的密钥
                logger.error("无法解密自定义多层加密的数据：缺少层密钥")
                return None
            else:
                logger.error(f"未知的加密算法：{algorithm_type}")
                return None
            
            # 尝试解密
            try:
                return algorithm.decrypt(encrypted)
            except Exception as e:
                logger.error(f"解密失败：{str(e)}")
                return None
    
    def start_auto_update(self):
        """启动自动更新线程"""
        def update_thread():
            while True:
                try:
                    # 计算下次更新时间
                    next_update = self.last_update + timedelta(seconds=self.update_interval)
                    now = datetime.now()
                    
                    if now >= next_update:
                        # 更新算法
                        self.update_algorithm()
                    else:
                        # 等待到下次更新时间
                        sleep_time = (next_update - now).total_seconds()
                        time.sleep(min(sleep_time, 60))  # 最多睡眠60秒，以便响应程序退出
                except Exception as e:
                    logger.error(f"自动更新线程出错：{str(e)}")
                    time.sleep(60)  # 出错后等待60秒
        
        # 创建并启动更新线程
        thread = threading.Thread(target=update_thread, daemon=True)
        thread.start()
        
        return thread


# 示例：如何使用动态加密系统
if __name__ == "__main__":
    # 创建动态加密系统（设置为60秒更新一次，方便测试）
    encryption_system = DynamicEncryptionSystem(update_interval=60)
    
    # 启动自动更新
    update_thread = encryption_system.start_auto_update()
    
    # 测试加密和解密
    test_data = {
        "username": "test_user",
        "password": "test_password",
        "email": "<EMAIL>",
        "created_at": datetime.now().isoformat()
    }
    
    print(f"原始数据: {test_data}")
    
    # 加密
    encrypted = encryption_system.encrypt(json.dumps(test_data))
    print(f"加密后数据大小: {len(encrypted)} 字节")
    
    # 解密
    decrypted = encryption_system.decrypt(encrypted)
    decrypted_data = json.loads(decrypted)
    print(f"解密后数据: {decrypted_data}")
    
    # 等待算法更新
    print("等待60秒，让算法自动更新...")
    time.sleep(65)
    
    # 使用新算法加密
    encrypted2 = encryption_system.encrypt(json.dumps(test_data))
    print(f"使用新算法加密后数据大小: {len(encrypted2)} 字节")
    
    # 解密新算法加密的数据
    decrypted2 = encryption_system.decrypt(encrypted2)
    decrypted_data2 = json.loads(decrypted2)
    print(f"解密后数据: {decrypted_data2}")
    
    # 验证旧算法加密的数据仍然可以解密
    decrypted_old = encryption_system.decrypt(encrypted)
    decrypted_data_old = json.loads(decrypted_old)
    print(f"旧算法加密数据解密结果: {decrypted_data_old}")
    
    print("测试完成！")
