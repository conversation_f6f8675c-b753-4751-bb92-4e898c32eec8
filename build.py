"""
打包脚本 - 将游戏增强工具打包成可执行文件
使用 PyInstaller 进行打包，并集成安全保护功能
"""

import os
import sys
import shutil
import subprocess
import random
import string
from datetime import datetime

def check_pyinstaller():
    """检查是否已安装 PyInstaller，如果没有则安装"""
    try:
        import PyInstaller
        print("PyInstaller 已安装")
        return True
    except ImportError:
        print("正在安装 PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("PyInstaller 安装成功")
            return True
        except subprocess.CalledProcessError:
            print("安装 PyInstaller 失败，请手动安装: pip install pyinstaller")
            return False

def check_pyarmor():
    """检查是否已安装 PyArmor，如果没有则安装"""
    try:
        subprocess.run(["pyarmor", "--version"], capture_output=True, text=True)
        print("PyArmor 已安装")
        return True
    except FileNotFoundError:
        print("正在安装 PyArmor...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyarmor"])
            print("PyArmor 安装成功")
            return True
        except subprocess.CalledProcessError:
            print("安装 PyArmor 失败，请手动安装: pip install pyarmor")
            return False

def check_dependencies():
    """检查并安装所有依赖"""
    try:
        with open('requirements.txt', 'r') as f:
            requirements = f.read().splitlines()
        
        print("正在检查依赖...")
        for req in requirements:
            if req.strip() and not req.startswith('#'):
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", req])
                    print(f"已安装: {req}")
                except subprocess.CalledProcessError:
                    print(f"安装 {req} 失败，请手动安装")
        
        return True
    except Exception as e:
        print(f"检查依赖出错: {str(e)}")
        return False

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', 'obfuscated']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"正在清理 {dir_name} 目录...")
            shutil.rmtree(dir_name)
    
    spec_file = 'game_enhancer.spec'
    if os.path.exists(spec_file):
        print(f"正在删除 {spec_file}...")
        os.remove(spec_file)

def obfuscate_code():
    """使用 PyArmor 混淆代码"""
    print("开始混淆代码...")
    
    # 创建混淆目录
    os.makedirs('obfuscated', exist_ok=True)
    
    # 生成随机项目名称
    random_name = ''.join(random.choice(string.ascii_letters) for _ in range(8))
    
    # 混淆命令
    cmd = [
        'pyarmor',
        'obfuscate',
        '--advanced', '2',  # 高级模式
        '--restrict', '0',  # 限制模式
        '--bootstrap', '2',  # 引导模式
        '--obf-code', '2',  # 代码混淆级别
        '--obf-mod', '2',   # 模块混淆级别
        '--wrap-mode', '1', # 包装模式
        '--protection', '1', # 保护模式
        '--name', f'obf_{random_name}',
        '-O', 'obfuscated',
        '--exclude', 'build.py',
        '--exclude', 'create_icon.py',
        '--exclude', 'version_info.txt',
        '--exclude', 'build',
        '--exclude', 'dist',
        'main_secure.py'  # 使用安全增强版主程序
    ]
    
    try:
        subprocess.check_call(cmd)
        print("代码混淆成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"代码混淆失败: {str(e)}")
        return False

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    # 创建版本信息文件
    version_info = f"""
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'080404b0',
          [StringStruct(u'CompanyName', u'Aion增强工具'),
          StringStruct(u'FileDescription', u'Aion 4.5 4.6 5.8 7.7通用'),
          StringStruct(u'FileVersion', u'1.0.0'),
          StringStruct(u'InternalName', u'aion_enhancer'),
          StringStruct(u'LegalCopyright', u'Copyright 2025'),
          StringStruct(u'OriginalFilename', u'aion_enhancer.exe'),
          StringStruct(u'ProductName', u'Aion 4.5 4.6 5.8 7.7通用'),
          StringStruct(u'ProductVersion', u'1.0.0')])
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
"""
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    # 确定主脚本路径
    main_script = 'obfuscated/main_secure.py' if os.path.exists('obfuscated/main_secure.py') else 'main_secure.py'
    if not os.path.exists(main_script):
        main_script = 'main.py'
    
    print(f"使用主脚本: {main_script}")
    
    # 构建命令
    cmd = [
        'pyinstaller',
        '--name=Aion4.5-7.7通用-安全版',
        '--onefile',
        '--windowed',
        '--icon=icon.ico' if os.path.exists('icon.ico') else '',
        '--version-file=version_info.txt',
        '--add-data=ui;ui',
        '--noconsole',
        '--clean',
        '--exclude-module=PyQt5',
        '--exclude-module=PySide2',
        '--exclude-module=PySide6',
        # 添加额外的数据文件
        '--add-data=license_system.py;.',
        '--add-data=anti_debug.py;.',
        '--add-data=dynamic_encryption.py;.',
        '--add-data=secure_license_system.py;.',
        # 添加隐藏导入
        '--hidden-import=requests',
        '--hidden-import=cryptography',
        '--hidden-import=hmac',
        main_script
    ]
    
    # 过滤掉空字符串
    cmd = [item for item in cmd if item]
    
    try:
        subprocess.check_call(cmd)
        print("可执行文件构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"构建可执行文件失败: {str(e)}")
        return False

def create_release_package():
    """创建发布包"""
    print("正在创建发布包...")
    
    # 创建发布目录
    release_dir = f"Aion增强工具_v1.0.0_{datetime.now().strftime('%Y%m%d')}"
    os.makedirs(release_dir, exist_ok=True)
    
    # 复制可执行文件
    exe_file = "dist/Aion4.5-7.7通用-安全版.exe"
    if os.path.exists(exe_file):
        shutil.copy(exe_file, release_dir)
    else:
        print(f"警告: 找不到可执行文件 {exe_file}")
        exe_file = "dist/Aion4.5-7.7通用.exe"
        if os.path.exists(exe_file):
            shutil.copy(exe_file, release_dir)
        else:
            print(f"错误: 找不到可执行文件 {exe_file}")
            return False
    
    # 创建说明文件
    readme_content = """
Aion增强工具 - 安全版
=====================

功能特点:
1. 支持Aion 4.5-7.7版本
2. 内置反调试和反篡改保护
3. 动态加密系统
4. 在线激活验证

使用说明:
1. 首次运行需要激活，请联系开发者获取激活码
2. 激活码与您的机器绑定，不可在其他机器上使用
3. 请勿尝试调试或修改程序，这将导致程序自动关闭

如有问题，请联系开发者。
"""
    
    with open(f"{release_dir}/说明.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    # 创建ZIP压缩包
    shutil.make_archive(release_dir, 'zip', '.', release_dir)
    
    # 清理临时目录
    shutil.rmtree(release_dir)
    
    print(f"发布包已创建: {release_dir}.zip")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("游戏增强工具打包脚本 - 安全增强版")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("依赖检查失败，打包终止")
        return
    
    # 检查PyInstaller
    if not check_pyinstaller():
        print("PyInstaller 检查失败，打包终止")
        return
    
    # 检查PyArmor
    pyarmor_available = check_pyarmor()
    
    # 清理构建目录
    clean_build_dirs()
    
    # 混淆代码（如果PyArmor可用）
    if pyarmor_available:
        obfuscate_code()
    else:
        print("PyArmor 不可用，跳过代码混淆步骤")
    
    # 构建可执行文件
    if not build_executable():
        print("构建可执行文件失败，打包终止")
        return
    
    # 创建发布包
    if not create_release_package():
        print("创建发布包失败")
        return
    
    print("=" * 50)
    print("打包完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()
