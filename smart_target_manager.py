#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能目标管理系统 - 专注击杀单个目标
"""

import time
import logging
from enum import Enum
from dataclasses import dataclass
from typing import Optional, Dict, List

logger = logging.getLogger('smart_target_manager')

class TargetState(Enum):
    """目标状态"""
    UNKNOWN = "unknown"
    ALIVE = "alive"
    DEAD = "dead"
    LOST = "lost"

@dataclass
class TargetInfo:
    """目标信息"""
    id: str
    name: str
    hp_percent: float = 100.0
    last_seen: float = 0.0
    state: TargetState = TargetState.UNKNOWN
    attack_start_time: float = 0.0
    estimated_hp: float = 100.0
    damage_dealt: int = 0

class SmartTargetManager:
    """智能目标管理器"""
    
    def __init__(self, input_simulator):
        self.input = input_simulator
        self.logger = logging.getLogger('smart_target_manager')
        
        # 当前目标
        self.current_target: Optional[TargetInfo] = None
        self.target_locked = False
        
        # 目标管理配置
        self.target_timeout = 10.0  # 目标超时时间（秒）
        self.hp_check_interval = 2.0  # 血量检查间隔
        self.last_hp_check = 0.0
        
        # 目标选择策略
        self.max_retarget_attempts = 3  # 最大重新选择目标次数
        self.retarget_attempts = 0
        
        # 统计信息
        self.stats = {
            'targets_killed': 0,
            'targets_lost': 0,
            'total_damage': 0,
            'average_kill_time': 0.0,
            'kill_times': []
        }
        
        self.logger.info("智能目标管理器已初始化")
    
    def should_select_new_target(self) -> bool:
        """判断是否应该选择新目标"""
        current_time = time.time()
        
        # 1. 没有当前目标
        if not self.current_target:
            self.logger.debug("没有当前目标，需要选择新目标")
            return True
        
        # 2. 目标已死亡
        if self.current_target.state == TargetState.DEAD:
            self.logger.info(f"目标 {self.current_target.name} 已死亡，选择新目标")
            self._record_kill()
            self.current_target = None
            self.target_locked = False
            return True
        
        # 3. 目标丢失超时
        if (current_time - self.current_target.last_seen) > self.target_timeout:
            self.logger.warning(f"目标 {self.current_target.name} 丢失超时，选择新目标")
            self.stats['targets_lost'] += 1
            self.current_target = None
            self.target_locked = False
            return True
        
        # 4. 目标血量检查
        if (current_time - self.last_hp_check) > self.hp_check_interval:
            self._check_target_hp()
            self.last_hp_check = current_time
        
        # 5. 如果目标仍然存活且在范围内，继续攻击
        return False
    
    def select_target(self) -> bool:
        """选择新目标"""
        try:
            # 发送Tab键选择目标
            success = self.input.press_key('tab')
            
            if success:
                # 创建新的目标信息
                current_time = time.time()
                target_id = f"target_{int(current_time)}"
                
                self.current_target = TargetInfo(
                    id=target_id,
                    name=f"怪物_{target_id[-4:]}",
                    hp_percent=100.0,
                    last_seen=current_time,
                    state=TargetState.ALIVE,
                    attack_start_time=current_time
                )
                
                self.target_locked = True
                self.retarget_attempts = 0
                
                self.logger.info(f"🎯 选择新目标: {self.current_target.name}")
                return True
            else:
                self.retarget_attempts += 1
                self.logger.warning(f"目标选择失败 (尝试 {self.retarget_attempts}/{self.max_retarget_attempts})")
                
                if self.retarget_attempts >= self.max_retarget_attempts:
                    self.logger.error("达到最大重试次数，暂停目标选择")
                    return False
                
                return False
                
        except Exception as e:
            self.logger.error(f"选择目标时出错: {e}")
            return False
    
    def _check_target_hp(self):
        """检查目标血量（模拟）"""
        if not self.current_target:
            return
        
        current_time = time.time()
        attack_duration = current_time - self.current_target.attack_start_time
        
        # 模拟血量下降（基于攻击时间）
        # 假设每秒造成10%伤害
        damage_per_second = 10.0
        estimated_damage = attack_duration * damage_per_second
        self.current_target.estimated_hp = max(0, 100.0 - estimated_damage)
        
        # 更新目标状态
        if self.current_target.estimated_hp <= 0:
            self.current_target.state = TargetState.DEAD
            self.logger.info(f"目标 {self.current_target.name} 血量归零")
        else:
            self.current_target.state = TargetState.ALIVE
            self.current_target.last_seen = current_time
            self.logger.debug(f"目标 {self.current_target.name} 估计血量: {self.current_target.estimated_hp:.1f}%")
    
    def _record_kill(self):
        """记录击杀"""
        if not self.current_target:
            return
        
        kill_time = time.time() - self.current_target.attack_start_time
        self.stats['targets_killed'] += 1
        self.stats['kill_times'].append(kill_time)
        
        # 计算平均击杀时间
        if self.stats['kill_times']:
            self.stats['average_kill_time'] = sum(self.stats['kill_times']) / len(self.stats['kill_times'])
        
        self.logger.info(f"✅ 击杀完成: {self.current_target.name} (用时: {kill_time:.1f}秒)")
    
    def get_current_target_info(self) -> Optional[Dict]:
        """获取当前目标信息"""
        if not self.current_target:
            return None
        
        current_time = time.time()
        attack_duration = current_time - self.current_target.attack_start_time
        
        return {
            'name': self.current_target.name,
            'hp_percent': self.current_target.estimated_hp,
            'state': self.current_target.state.value,
            'attack_duration': attack_duration,
            'is_locked': self.target_locked
        }
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        return self.stats.copy()
    
    def reset_target(self):
        """重置当前目标"""
        if self.current_target:
            self.logger.info(f"重置目标: {self.current_target.name}")
        
        self.current_target = None
        self.target_locked = False
        self.retarget_attempts = 0