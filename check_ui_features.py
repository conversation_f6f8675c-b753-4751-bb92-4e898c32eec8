#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI功能检查脚本 - 检查UI中是否包含所有新功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_ui_features():
    """检查UI功能"""
    print("=" * 60)
    print("UI功能检查")
    print("=" * 60)
    
    try:
        # 检查UI文件中的功能
        ui_file = "ui/main_window.py"
        
        print(f"检查文件: {ui_file}")
        
        with open(ui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查自动范围打怪功能
        aoe_features = [
            "auto_aoe_check",
            "启用自动范围打怪",
            "aoe_skills_edit",
            "aoe_min_targets_spin",
            "toggle_auto_aoe",
            "update_aoe_skills",
            "update_aoe_min_targets"
        ]
        
        print("\n🌀 自动范围打怪功能检查:")
        for feature in aoe_features:
            if feature in content:
                print(f"  ✅ {feature}")
            else:
                print(f"  ❌ {feature} - 缺失")
        
        # 检查手动点怪自动技能功能
        manual_features = [
            "manual_target_auto_skill_check",
            "启用手动点怪自动技能",
            "force_manual_target_button",
            "clear_manual_target_button",
            "toggle_manual_target_auto_skill",
            "force_manual_target",
            "clear_manual_target"
        ]
        
        print("\n👆 手动点怪自动技能功能检查:")
        for feature in manual_features:
            if feature in content:
                print(f"  ✅ {feature}")
            else:
                print(f"  ❌ {feature} - 缺失")
        
        # 检查专注击杀功能
        focus_features = [
            "focus_kill_check",
            "启用专注击杀模式",
            "max_focus_time_spin",
            "no_damage_timeout_spin",
            "toggle_focus_kill_mode",
            "update_focus_parameters"
        ]
        
        print("\n🎯 专注击杀功能检查:")
        for feature in focus_features:
            if feature in content:
                print(f"  ✅ {feature}")
            else:
                print(f"  ❌ {feature} - 缺失")
        
        # 检查自动攻击功能
        auto_attack_features = [
            "auto_attack_check",
            "启用自动攻击周围怪物",
            "attack_range_spin",
            "scan_interval_spin",
            "toggle_auto_attack",
            "update_attack_range",
            "update_scan_interval"
        ]
        
        print("\n⚔️ 自动攻击功能检查:")
        for feature in auto_attack_features:
            if feature in content:
                print(f"  ✅ {feature}")
            else:
                print(f"  ❌ {feature} - 缺失")
        
        return True
        
    except Exception as e:
        print(f"✗ UI功能检查失败: {str(e)}")
        return False

def check_battle_logic_features():
    """检查战斗逻辑功能"""
    print("\n" + "=" * 60)
    print("战斗逻辑功能检查")
    print("=" * 60)
    
    try:
        # 检查战斗逻辑文件
        battle_file = "battle_logic.py"
        
        print(f"检查文件: {battle_file}")
        
        with open(battle_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查自动范围打怪功能
        aoe_logic_features = [
            "auto_aoe_enabled",
            "aoe_skills",
            "aoe_min_targets",
            "check_and_use_aoe_skills",
            "_select_best_aoe_skill",
            "toggle_auto_aoe",
            "set_aoe_skills",
            "set_aoe_min_targets"
        ]
        
        print("\n🌀 自动范围打怪逻辑检查:")
        for feature in aoe_logic_features:
            if feature in content:
                print(f"  ✅ {feature}")
            else:
                print(f"  ❌ {feature} - 缺失")
        
        # 检查手动点怪自动技能功能
        manual_logic_features = [
            "manual_target_auto_skill",
            "manual_target_id",
            "manual_target_detected",
            "handle_manual_target_auto_skill",
            "_detect_manual_target",
            "_auto_skill_for_manual_target",
            "_create_manual_target_object",
            "toggle_manual_target_auto_skill",
            "force_manual_target"
        ]
        
        print("\n👆 手动点怪自动技能逻辑检查:")
        for feature in manual_logic_features:
            if feature in content:
                print(f"  ✅ {feature}")
            else:
                print(f"  ❌ {feature} - 缺失")
        
        # 检查专注击杀功能
        focus_logic_features = [
            "focus_kill_mode",
            "current_focus_target",
            "_focus_kill_logic",
            "_is_focus_target_valid",
            "_check_target_killed",
            "_select_new_focus_target",
            "_set_focus_target",
            "_clear_focus_target",
            "_continue_focus_attack",
            "toggle_focus_kill_mode"
        ]
        
        print("\n🎯 专注击杀逻辑检查:")
        for feature in focus_logic_features:
            if feature in content:
                print(f"  ✅ {feature}")
            else:
                print(f"  ❌ {feature} - 缺失")
        
        return True
        
    except Exception as e:
        print(f"✗ 战斗逻辑功能检查失败: {str(e)}")
        return False

def show_feature_summary():
    """显示功能总结"""
    print("\n" + "=" * 60)
    print("功能总结")
    print("=" * 60)
    
    print("您的副本辅助现在应该包含以下功能:")
    
    print("\n🎮 在'AI增强'选项卡中，您应该看到:")
    
    print("\n📦 自动攻击周围怪物组:")
    print("  ☑️ 启用自动攻击周围怪物")
    print("  🎯 攻击范围: [5-50米]")
    print("  ⏱️ 扫描间隔: [50-1000毫秒]")
    
    print("\n📦 专注击杀模式组:")
    print("  ☑️ 启用专注击杀模式")
    print("  ⏰ 最大专注时间: [5-60秒]")
    print("  💥 无伤害超时: [2-20秒]")
    
    print("\n📦 自动范围打怪组:")
    print("  ☑️ 启用自动范围打怪")
    print("  🎯 范围技能: [输入框，如: 3,6,9]")
    print("  🔢 最少目标: [1-10个]")
    
    print("\n📦 手动点怪自动技能组:")
    print("  ☑️ 启用手动点怪自动技能")
    print("  🎯 [设置当前目标为手动] 按钮")
    print("  🗑️ [清除手动目标] 按钮")
    
    print("\n🎯 如果您没有看到这些功能，可能的原因:")
    print("  1. 需要重新启动程序")
    print("  2. UI文件可能没有正确更新")
    print("  3. 需要连接到游戏进程后才能启用")
    
    print("\n💡 解决方法:")
    print("  1. 完全关闭程序")
    print("  2. 重新启动程序")
    print("  3. 连接到游戏进程")
    print("  4. 切换到'AI增强'选项卡")
    print("  5. 查看是否有新功能")

def main():
    """主检查函数"""
    print("UI功能完整性检查")
    print("=" * 60)
    
    # 执行检查
    ui_check = check_ui_features()
    logic_check = check_battle_logic_features()
    
    # 显示功能总结
    show_feature_summary()
    
    # 输出检查结果
    print("\n" + "=" * 60)
    print("检查结果汇总")
    print("=" * 60)
    
    if ui_check and logic_check:
        print("✅ UI功能检查: 通过")
        print("✅ 战斗逻辑检查: 通过")
        print("\n🎉 所有功能都已正确实现！")
        print("\n如果您在UI中没有看到新功能，请:")
        print("1. 重新启动程序")
        print("2. 连接到游戏进程")
        print("3. 切换到'AI增强'选项卡")
    else:
        print("❌ 部分功能可能缺失")
        print("请检查相关文件是否正确更新")
    
    return ui_check and logic_check

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
