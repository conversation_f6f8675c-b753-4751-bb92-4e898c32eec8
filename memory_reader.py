#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
内存读取模块 - 直接从游戏内存中读取数据
"""

import os
import sys
import time
import logging
import ctypes
import random
import math
import numpy as np
import threading
from collections import defaultdict
from ctypes import windll, c_void_p, c_ulong, byref, Structure, Union, POINTER, sizeof, create_string_buffer
from ctypes.wintypes import (
    DWORD, HANDLE, BOOL, LPVOID, WORD, BYTE, SHORT, LONG, USHORT,
    CHAR, WCHAR, ULONG, LPCVOID, LPSTR, LPWSTR, HWND
)

# 补充缺少的类型
if not hasattr(ctypes.wintypes, 'LONG_PTR'):
    if ctypes.sizeof(ctypes.c_void_p) == 8:
        LONG_PTR = ctypes.c_int64
    else:
        LONG_PTR = ctypes.c_long
else:
    from ctypes.wintypes import LONG_PTR

# Windows API常量
PROCESS_ALL_ACCESS = 0x1F0FFF
PROCESS_REQUIRED_ACCESS = 0x1F0FFF
INVALID_HANDLE_VALUE = -1
TH32CS_SNAPMODULE = 0x00000008
TH32CS_SNAPMODULE32 = 0x00000010

# 从ctypes.wintypes导入HMODULE
try:
    from ctypes.wintypes import HMODULE
except ImportError:
    HMODULE = HANDLE  # 如果导入失败，使用HANDLE作为备用

# 结构体定义
class MODULEENTRY32(Structure):
    _fields_ = [
        ('dwSize', DWORD),
        ('th32ModuleID', DWORD),
        ('th32ProcessID', DWORD),
        ('GlblcntUsage', DWORD),
        ('ProccntUsage', DWORD),
        ('modBaseAddr', LPVOID),
        ('modBaseSize', DWORD),
        ('hModule', HMODULE),
        ('szModule', CHAR * 256),
        ('szExePath', CHAR * 260)
    ]

# 简化函数名
OpenProcess = windll.kernel32.OpenProcess
CloseHandle = windll.kernel32.CloseHandle
ReadProcessMemory = windll.kernel32.ReadProcessMemory
WriteProcessMemory = windll.kernel32.WriteProcessMemory
GetLastError = windll.kernel32.GetLastError
CreateToolhelp32Snapshot = windll.kernel32.CreateToolhelp32Snapshot
Module32First = windll.kernel32.Module32First
Module32Next = windll.kernel32.Module32Next

# 内存信息结构体
class MEMORY_BASIC_INFORMATION(ctypes.Structure):
    _fields_ = [
        ("BaseAddress", ctypes.c_void_p),
        ("AllocationBase", ctypes.c_void_p),
        ("AllocationProtect", DWORD),
        ("RegionSize", ctypes.c_size_t),
        ("State", DWORD),
        ("Protect", DWORD),
        ("Type", DWORD)
    ]

# 设置日志
logger = logging.getLogger('aion_memory_reader')
logger.setLevel(logging.INFO)

if not logger.handlers:
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 设置日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    
    # 添加处理器到日志
    logger.addHandler(console_handler)

# 内存读取异常
class MemoryReadError(Exception):
    """内存读取过程中的错误"""
    pass

class MemoryPageInfo:
    """内存页信息类"""
    def __init__(self, base_address, size, protection, state, type):
        self.base_address = base_address
        self.size = size
        self.protection = protection
        self.state = state
        self.type = type
        self.content = None  # 可以按需加载内容

    def is_readable(self):
        """检查页面是否可读"""
        # 0x04 = PAGE_READWRITE, 0x02 = PAGE_READONLY
        return self.protection in [0x04, 0x02]

    def __str__(self):
        return f"MemoryPage: 0x{self.base_address:X} - Size: {self.size}, Protection: {self.protection}"

class MemoryReader:
    """游戏内存读取类，带高级功能和缓存"""
    
    def __init__(self):
        """初始化内存读取器"""
        self.process_handle = None
        self.process_id = None
        self.module_base_addr = 0
        self.offsets = None
        self.process_name = None
        self.debug = True
        self.character_base_addr = 0
        
        # 测试模式设置
        self.test_mode = False  # 默认禁用测试模式
        self.auto_test_mode = True  # 当内存读取失败时自动切换到测试模式
        self.test_data_interval = 1.0  # 测试数据更新间隔（秒）
        self.last_test_data_update = 0
        self.test_character_data = None
        self.test_monsters_data = None
        
        # 缓存系统
        self.cache_enabled = True
        self.cache_ttl = 0.5  # 缓存生存时间(秒)
        self.value_cache = {}  # 值缓存
        self.address_cache = {}  # 地址缓存
        self.last_cache_clear = time.time()
        self.cache_lock = threading.Lock()
        
        # 内存页缓存
        self.memory_pages = []
        self.memory_page_cache_time = 0
        
        # 创建自动检测和恢复线程
        self.auto_recovery = True
        self.recovery_thread = None
        self.stop_recovery = False
        
        # 战斗状态监控
        self.combat_state_cache = {}  # 战斗状态缓存
        self.last_hit_time = 0  # 上次命中时间
        self.hit_detection_enabled = True  # 启用命中检测
        self.damage_history = []  # 伤害历史记录
        self.max_damage_history = 10  # 最大伤害历史记录数
        
        # 读取偏移量配置
        self.load_offsets()
        
        # 多版本偏移量支持
        self.version = None
        self.version_offsets = {}
        
        # 版本指纹 - 用于识别不同游戏版本
        self.version_fingerprints = {
            "7.7.0.5": {"size": 31457280, "time": 1577836800.0},  # 示例值
            "7.7.0.7": {"size": 31654912, "time": 1585699200.0},  # 示例值
            "7.8.0.0": {"size": 32145408, "time": 1593561600.0},  # 示例值
        }
        
        logger.info("内存读取器已初始化")
    
    def __del__(self):
        """析构函数，关闭进程句柄"""
        self.stop_auto_recovery()
        if self.process_handle:
            CloseHandle(self.process_handle)
    
    def close_process(self):
        """关闭进程句柄"""
        if self.process_handle:
            CloseHandle(self.process_handle)
            self.process_handle = None
            self.process_id = None
            self.module_base_addr = 0
            self.character_base_addr = 0
            logger.info("已关闭进程句柄")
        return True
    
    def load_offsets(self):
        """加载偏移量配置"""
        # 默认偏移量配置
        self.offsets = {
            # 游戏模块基址相关
            "game_initial_offset": 0xDC8918,  # 游戏最初基址偏移
            "first_level_offset": 0x70,       # 一级偏移
            "second_level_offset": 0x10,      # 二级偏移
            "third_level_offset": 0x20,       # 三级偏移
            "character_base_offset": 0x368,   # 角色基址偏移
            
            # 角色状态偏移
            "character_base": {
                "hp_offset": 0x4F0,         # 血量偏移
                "hp_max_offset": 0x4F4,     # 最大血量偏移
                "mp_offset": 0x4F8,         # 魔法值偏移
                "mp_max_offset": 0x4FC,     # 最大魔法值偏移
                "id_offset": 0x368,         # 角色ID偏移
                "level_offset": 0x480,      # 角色等级偏移
                "x_coord": 0x98,           # X坐标偏移
                "y_coord": 0x9C,           # Y坐标偏移
                "z_coord": 0xA0,           # Z坐标偏移
                "target_id_offset": 0x540,  # 目标ID偏移
                "combat_state": 0x5A0,      # 战斗状态偏移
                "moving_offset": 0x6CC,     # 移动状态偏移
                "casting_offset": 0x610,    # 施法状态偏移
            },
            
            # 其他状态偏移
            "attack_speed_offset": 0x4FA,   # 攻击速度偏移
            "move_speed_offset": 0x6CC,     # 移动速度偏移
            "flight_offset": 0x8D8,         # 飞行状态偏移
        }
        
        # 尝试从文件加载偏移量配置
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "offsets.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    loaded_offsets = json.load(f)
                # 更新默认偏移量配置
                self.update_dict(self.offsets, loaded_offsets)
                logger.info("已加载偏移量配置")
            except Exception as e:
                logger.warning(f"加载偏移量配置失败: {str(e)}")
        
    def update_dict(self, d, u):
        """递归更新字典"""
        for k, v in u.items():
            if isinstance(v, dict):
                d[k] = self.update_dict(d.get(k, {}), v)
            else:
                d[k] = v
        return d
    
    def detect_game_version(self):
        """尝试检测游戏版本"""
        if not self.process_handle:
            return None
            
        try:
            # 这里只是示例，实际检测逻辑取决于游戏
            # 可能是检查特定内存位置的值，或者读取游戏EXE信息
            
            # 方法1: 检查特定内存位置的值
            version_addr = self.module_base_addr + 0x12345  # 示例地址
            version_bytes = self.read_bytes(version_addr, 8)
            if version_bytes:
                version_str = version_bytes.decode('ascii', errors='ignore').strip('\x00')
                pattern = re.compile(r'(\d+\.\d+\.\d+)')
                match = pattern.search(version_str)
                if match:
                    self.version = match.group(1)
                    logger.info(f"检测到游戏版本: {self.version}")
                    return self.version
            
            # 方法2: 检查文件版本
            if self.process_id:
                process = psutil.Process(self.process_id)
                exe_path = process.exe()
                if os.path.exists(exe_path):
                    # 这里可以使用Windows API获取文件版本信息
                    # 示例使用文件大小和修改时间作为替代
                    file_size = os.path.getsize(exe_path)
                    mod_time = os.path.getmtime(exe_path)
                    
                    # 基于文件特征猜测版本
                    for ver, data in self.version_fingerprints.items():
                        if abs(file_size - data['size']) < 1000 and abs(mod_time - data['time']) < 86400:
                            self.version = ver
                            logger.info(f"基于文件特征检测到游戏版本: {self.version}")
                            return self.version
            
            logger.warning("无法检测游戏版本，使用默认偏移量")
            return None
            
        except Exception as e:
            logger.error(f"版本检测失败: {str(e)}")
            return None
    
    def update_offsets_for_version(self):
        """根据检测的游戏版本更新偏移量"""
        if not self.version or not self.version_offsets:
            return
            
        if self.version in self.version_offsets:
            # 合并版本特定的偏移量到主偏移量
            version_offsets = self.version_offsets[self.version]
            for category, offsets in version_offsets.items():
                if category in self.offsets:
                    self.offsets[category].update(offsets)
                else:
                    self.offsets[category] = offsets
                    
            logger.info(f"已更新偏移量配置为版本 {self.version}")
        else:
            logger.warning(f"版本 {self.version} 没有对应的偏移量配置")
    
    def clear_cache(self):
        """清除内存缓存"""
        with self.cache_lock:
            self.value_cache.clear()
            self.address_cache.clear()
            self.last_cache_clear = time.time()
            logger.debug("已清除内存缓存")
    
    def cache_check(self):
        """检查是否需要清除缓存"""
        current_time = time.time()
        if current_time - self.last_cache_clear > self.cache_ttl:
            self.clear_cache()

    def get_cached_value(self, key):
        """从缓存获取值"""
        if not self.cache_enabled:
            return None
            
        with self.cache_lock:
            cache_entry = self.value_cache.get(key)
            if cache_entry:
                timestamp, value = cache_entry
                if time.time() - timestamp <= self.cache_ttl:
                    return value
        return None
        
    def set_cached_value(self, key, value):
        """设置缓存值"""
        if not self.cache_enabled:
            return
            
        with self.cache_lock:
            self.value_cache[key] = (time.time(), value)
    
    def get_cached_address(self, key):
        """从缓存获取地址"""
        if not self.cache_enabled:
            return None
            
        with self.cache_lock:
            cache_entry = self.address_cache.get(key)
            if cache_entry:
                timestamp, address = cache_entry
                if time.time() - timestamp <= self.cache_ttl:
                    return address
        return None
        
    def set_cached_address(self, key, address):
        """设置缓存地址"""
        if not self.cache_enabled:
            return
            
        with self.cache_lock:
            self.address_cache[key] = (time.time(), address)
            
    def start_auto_recovery(self):
        """启动自动恢复线程"""
        if self.auto_recovery and not self.recovery_thread:
            self.stop_recovery = False
            self.recovery_thread = threading.Thread(target=self.recovery_worker, daemon=True)
            self.recovery_thread.start()
            logger.info("自动恢复线程已启动")
            
    def stop_auto_recovery(self):
        """停止自动恢复线程"""
        if self.recovery_thread:
            self.stop_recovery = True
            if self.recovery_thread.is_alive():
                self.recovery_thread.join(2.0)  # 等待最多2秒
            self.recovery_thread = None
            logger.info("自动恢复线程已停止")
            
    def recovery_worker(self):
        """自动恢复工作线程"""
        while not self.stop_recovery:
            try:
                # 每5秒检查一次
                time.sleep(5)
                
                # 如果测试模式已开启，不需要恢复
                if self.test_mode:
                    continue
                
                # 如果进程句柄无效，尝试重新连接
                if not self.process_handle or self.process_handle == 0:
                    if self.process_id:
                        logger.debug("检测到进程句柄无效，尝试重新连接")
                        self.open_process(self.process_id)
                    elif self.auto_test_mode:
                        # 如果允许自动测试模式，切换到测试模式
                        logger.warning("无法连接到游戏进程，自动切换到测试模式")
                        self.test_mode = True
                        
                # 如果角色基址失效，尝试重新获取
                elif self.character_base_addr == 0:
                    logger.debug("检测到角色基址无效，尝试重新初始化")
                    if not self.initialize_character_base() and self.auto_test_mode:
                        # 如果初始化失败且允许自动测试模式，切换到测试模式
                        logger.warning("无法获取角色基址，自动切换到测试模式")
                        self.test_mode = True
                    
            except Exception as e:
                logger.debug(f"恢复线程错误: {str(e)}")
                
    def open_process(self, pid):
        """打开游戏进程"""
        try:
            # 关闭现有句柄
            if self.process_handle:
                CloseHandle(self.process_handle)
                self.process_handle = None
            
            self.process_id = pid
            self.process_handle = OpenProcess(PROCESS_REQUIRED_ACCESS, False, pid)
            
            if not self.process_handle:
                error_code = ctypes.get_last_error()
                logger.error(f"无法打开进程 ID {pid}，错误码: {error_code}")
                return False
            
            logger.info(f"已打开进程 ID {pid} 的句柄")
            
            # 获取模块基址
            self.module_base_addr = self.get_module_base("Game.dll")
            if not self.module_base_addr:
                logger.warning("无法获取模块基址，使用默认值 0")
                self.module_base_addr = 0
            
            # 检测游戏版本并更新偏移量
            self.detect_game_version()
            self.update_offsets_for_version()
            
            # 初始化角色基址
            self.initialize_character_base()
            
            # 开始自动恢复线程
            self.start_auto_recovery()
            
            return True
        except Exception as e:
            logger.error(f"打开进程出错: {str(e)}")
            return False
            
    def get_process_by_name(self, process_name):
        """通过进程名称获取进程 ID"""
        try:
            self.process_name = process_name
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'].lower() == process_name.lower():
                    logger.info(f"找到进程 {process_name}, PID: {proc.info['pid']}")
                    return proc.info['pid']
            logger.warning(f"未找到进程 {process_name}")
            return None
        except Exception as e:
            logger.error(f"查找进程时出错: {str(e)}")
            return None
            
    def connect_to_game(self, process_name="aion.bin"):
        """连接到游戏进程"""
        try:
            pid = self.get_process_by_name(process_name)
            if pid:
                return self.open_process(pid)
            return False
        except Exception as e:
            logger.error(f"连接到游戏出错: {str(e)}")
            return False
    
    def get_module_base(self, module_name=None):
        """获取指定模块的基址"""
        try:
            if not self.process_id:
                return 0
                
            modules = self.get_process_modules(self.process_id)
            for module in modules:
                if module['name'].lower() == module_name.lower():
                    return module['base']
            return 0
        except Exception as e:
            logger.debug(f"获取模块基址失败: {str(e)}")
            return 0
            
    def get_process_modules(self, pid):
        """获取进程模块列表"""
        try:
            modules = []
            if not pid:
                return modules
                
            # 首先尝试使用psutil获取模块信息
            try:
                import psutil
                process = psutil.Process(pid)
                for module in process.memory_maps():
                    module_path = module.path
                    module_name = os.path.basename(module_path)
                    base_addr = int(module.addr.split('-')[0], 16)
                    modules.append({
                        'name': module_name,
                        'path': module_path,
                        'base': base_addr
                    })
                
                if modules:
                    logger.info(f"使用psutil成功获取了{len(modules)}个模块信息")
                    return modules
            except Exception as e:
                logger.debug(f"使用psutil获取模块信息失败: {str(e)}")
            
            # 如果psutil失败，使用备用方法 - 使用EnumProcessModules
            try:
                hProcess = windll.kernel32.OpenProcess(0x1F0FFF, False, pid)
                if not hProcess:
                    logger.debug(f"无法打开进程{pid}以获取模块信息")
                    return modules
                    
                try:
                    # 使用CreateToolhelp32Snapshot获取进程模块
                    TH32CS_SNAPMODULE = 0x00000008
                    TH32CS_SNAPMODULE32 = 0x00000010
                    
                    hModuleSnap = windll.kernel32.CreateToolhelp32Snapshot(
                        TH32CS_SNAPMODULE | TH32CS_SNAPMODULE32, pid)
                    
                    if hModuleSnap != INVALID_HANDLE_VALUE:
                        try:
                            module_entry = MODULEENTRY32()
                            module_entry.dwSize = ctypes.sizeof(module_entry)
                            
                            if windll.kernel32.Module32First(hModuleSnap, ctypes.byref(module_entry)):
                                while True:
                                    modules.append({
                                        'name': module_entry.szModule.decode('utf-8', errors='ignore'),
                                        'path': module_entry.szExePath.decode('utf-8', errors='ignore'),
                                        'base': module_entry.modBaseAddr
                                    })
                                    
                                    if not windll.kernel32.Module32Next(hModuleSnap, ctypes.byref(module_entry)):
                                        break
                        finally:
                            windll.kernel32.CloseHandle(hModuleSnap)
                    
                    if modules:
                        logger.info(f"使用CreateToolhelp32Snapshot成功获取了{len(modules)}个模块信息")
                        return modules
                        
                    # 如果上面都失败了，尝试使用EnumProcessModules
                    hModules = (ctypes.c_void_p * 1024)()
                    cbNeeded = ctypes.c_ulong()
                    
                    if windll.psapi.EnumProcessModules(
                        hProcess, 
                        ctypes.byref(hModules), 
                        ctypes.sizeof(hModules), 
                        ctypes.byref(cbNeeded)
                    ):
                        for i in range(cbNeeded.value // ctypes.sizeof(ctypes.c_void_p)):
                            hMod = hModules[i]
                            
                            # 获取模块名
                            szModName = ctypes.create_string_buffer(260)
                            if windll.psapi.GetModuleBaseNameA(
                                hProcess, hMod, szModName, ctypes.sizeof(szModName)
                            ):
                                # 获取模块路径
                                szModPath = ctypes.create_string_buffer(260)
                                if windll.psapi.GetModuleFileNameExA(
                                    hProcess, hMod, szModPath, ctypes.sizeof(szModPath)
                                ):
                                    modules.append({
                                        'name': szModName.value.decode('utf-8', errors='ignore'),
                                        'path': szModPath.value.decode('utf-8', errors='ignore'),
                                        'base': hMod
                                    })
                                    
                            # 如果获取了足够的模块（防止无限循环）
                            if len(modules) >= 200:
                                break
                                
                    if modules:
                        logger.info(f"使用EnumProcessModules成功获取了{len(modules)}个模块信息")
                        
                finally:
                    windll.kernel32.CloseHandle(hProcess)
                    
            except Exception as e:
                logger.debug(f"获取进程模块信息失败: {str(e)}")
                
            # 没有找到模块，尝试手动添加一些常见的猜测模块
            if not modules:
                # 添加一些常见的模块，仅用于测试
                logger.warning("无法通过常规方法获取模块，使用预设模块进行尝试")
                modules = [
                    {'name': 'Game.dll', 'path': 'C:\\Program Files\\Aion\\bin32\\Game.dll', 'base': 0x10000000},
                    {'name': 'aion.exe', 'path': 'C:\\Program Files\\Aion\\bin32\\aion.exe', 'base': 0x00400000},
                    {'name': 'system.dll', 'path': 'C:\\Program Files\\Aion\\bin32\\system.dll', 'base': 0x20000000}
                ]
                
            return modules
        except Exception as e:
            logger.error(f"获取进程模块列表失败: {str(e)}")
            return []
        
    def get_skill_bar_base(self):
        """获取技能栏基址"""
        try:
            if self.test_mode:
                return 0
                
            if not self.process_handle or not self.character_base_addr:
                return 0
                
            # 技能栏通常在角色结构的某个偏移处
            # 这个偏移需要根据游戏版本调整
            skill_bar_ptr = self.read_int64(self.character_base_addr + 0x2A0)
            if not skill_bar_ptr:
                # 尝试其他常见偏移
                skill_bar_ptr = self.read_int64(self.character_base_addr + 0x2B0)
                
            if not skill_bar_ptr:
                # 再尝试其他偏移
                skill_bar_ptr = self.read_int64(self.character_base_addr + 0x2C0)
                
            if skill_bar_ptr:
                logger.info(f"找到技能栏基址: 0x{skill_bar_ptr:X}")
                return skill_bar_ptr
                
            return 0
        except Exception as e:
            logger.debug(f"获取技能栏基址失败: {str(e)}")
            return 0
    
    def scan_for_monsters(self, max_distance=100.0):
        """扫描游戏内存查找怪物实体"""
        if self.test_mode:
            return self.generate_test_monster_data()
            
        try:
            if not self.process_handle or not self.character_base_addr:
                return []
                
            # 获取角色位置作为参考点
            character = self.get_character_status()
            if not character or 'position' not in character:
                return []
                
            char_pos = character['position']
            
            # 怪物列表
            monsters = []
            
            # 方法1: 通过实体列表查找怪物
            entity_list_ptr = self.read_int64(self.module_base_addr + 0x1A30000)
            if entity_list_ptr:
                # 实体列表通常是一个数组或链表
                max_entities = 100  # 最多扫描100个实体
                entity_size = 0x200  # 实体结构大小（估计值）
                
                for i in range(max_entities):
                    entity_addr = entity_list_ptr + i * entity_size
                    
                    # 检查实体类型（通常有一个类型标识）
                    entity_type = self.read_int(entity_addr + 0x10)
                    
                    # 类型2通常是怪物
                    if entity_type == 2:
                        # 读取怪物数据
                        monster = self.read_monster_data(entity_addr)
                        
                        if monster:
                            # 计算与角色的距离
                            distance = self.calculate_distance(char_pos, monster['position'])
                            
                            if distance <= max_distance:
                                monster['distance'] = distance
                                monsters.append(monster)
            
            # 方法2: 扫描特定内存区域查找怪物特征
            if not monsters:
                # 怪物数据通常在一个特定的内存区域
                start_addr = self.module_base_addr + 0x1000000
                end_addr = start_addr + 0x1000000
                
                # 扫描内存区域
                chunk_size = 0x1000  # 4KB块
                for addr in range(start_addr, end_addr, chunk_size):
                    # 检查这个区域是否包含怪物数据的特征
                    # 怪物通常有HP、等级等特征
                    hp = self.read_float(addr + 0x50)
                    level = self.read_int(addr + 0x40)
                    
                    if 0 < hp < 100000 and 1 <= level <= 99:
                        # 可能是怪物，读取更多数据
                        monster = self.read_monster_data(addr)
                        
                        if monster:
                            # 计算与角色的距离
                            distance = self.calculate_distance(char_pos, monster['position'])
                            
                            if distance <= max_distance:
                                monster['distance'] = distance
                                monsters.append(monster)
            
            # 按距离排序
            monsters.sort(key=lambda m: m.get('distance', 999))
            
            return monsters
            
        except Exception as e:
            logger.error(f"扫描怪物失败: {str(e)}")
            return []
    
    def read_monster_data(self, addr):
        """从内存地址读取怪物数据"""
        try:
            # 基本检查
            if not addr:
                return None
                
            # 读取基本属性
            hp = self.read_float(addr + 0x50)
            hp_max = self.read_float(addr + 0x54)
            level = self.read_int(addr + 0x40)
            
            # 简单验证
            if hp <= 0 or hp > 1000000 or level <= 0 or level > 99:
                return None
                
            # 读取位置
            pos_x = self.read_float(addr + 0x80)
            pos_y = self.read_float(addr + 0x84)
            pos_z = self.read_float(addr + 0x88)
            
            # 读取怪物ID
            monster_id = self.read_int(addr + 0x20)
            
            # 读取怪物名称（如果有）
            name = "未知怪物"
            name_ptr = self.read_int64(addr + 0x30)
            if name_ptr:
                name_bytes = self.read_bytes(name_ptr, 32)
                if name_bytes:
                    try:
                        # 尝试解码名称
                        name = name_bytes.split(b'\x00')[0].decode('utf-8', errors='ignore')
                    except:
                        pass
            
            # 读取怪物状态
            state = self.read_int(addr + 0x60)
            is_dead = (state & 0x1) != 0
            is_aggressive = (state & 0x2) != 0
            
            # 构建怪物数据
            monster = {
                'id': monster_id,
                'address': addr,
                'name': name,
                'level': level,
                'hp': hp,
                'hp_max': hp_max,
                'hp_percent': (hp / hp_max * 100) if hp_max > 0 else 0,
                'position': [pos_x, pos_y, pos_z],
                'is_dead': is_dead,
                'is_aggressive': is_aggressive,
                'distance': 0  # 将在调用函数中计算
            }
            
            return monster
            
        except Exception as e:
            logger.debug(f"读取怪物数据失败: {str(e)}")
            return None
    
    def analyze_monster_type(self, monster):
        """分析怪物类型和特性"""
        if not monster:
            return {}
            
        # 基于怪物ID、名称和等级推断怪物类型
        monster_id = monster.get('id', 0)
        name = monster.get('name', '').lower()
        level = monster.get('level', 1)
        
        # 怪物类型特征
        type_features = {
            'elite': ['精英', 'elite', 'champion', '头目'],
            'boss': ['boss', '首领', '领主', '将军'],
            'normal': ['小怪', '普通', 'normal', 'mob'],
            'aggressive': ['攻击性', '好战', 'aggressive'],
            'passive': ['被动', '和平', 'passive', 'peaceful']
        }
        
        # 分析结果
        analysis = {
            'type': 'normal',  # 默认为普通怪物
            'threat_level': 1,  # 威胁等级1-10
            'recommended_level': level,  # 推荐等级
            'is_aggressive': monster.get('is_aggressive', False)
        }
        
        # 根据名称判断类型
        for type_name, keywords in type_features.items():
            if any(keyword in name for keyword in keywords):
                if type_name in ['elite', 'boss']:
                    analysis['type'] = type_name
                elif type_name in ['aggressive', 'passive']:
                    analysis['is_aggressive'] = (type_name == 'aggressive')
        
        # 根据等级和HP判断威胁等级
        hp_percent = monster.get('hp_percent', 100)
        hp_max = monster.get('hp_max', 100)
        
        if analysis['type'] == 'boss':
            analysis['threat_level'] = min(10, 7 + level // 10)
        elif analysis['type'] == 'elite':
            analysis['threat_level'] = min(8, 5 + level // 10)
        else:
            analysis['threat_level'] = min(5, 1 + level // 10)
            
        # 根据血量调整威胁
        if hp_max > 10000:
            analysis['threat_level'] += 2
        elif hp_max > 5000:
            analysis['threat_level'] += 1
            
        # 确保威胁等级在1-10范围内
        analysis['threat_level'] = max(1, min(10, analysis['threat_level']))
        
        # 推荐等级
        if analysis['type'] == 'boss':
            analysis['recommended_level'] = level + 2
        elif analysis['type'] == 'elite':
            analysis['recommended_level'] = level + 1
        else:
            analysis['recommended_level'] = level
            
        return analysis
    
    def parse_address(self, addr):
        """解析地址表示，支持字符串和数字"""
        if isinstance(addr, int):
            return addr
        elif isinstance(addr, str):
            if addr.startswith('0x'):
                return int(addr, 16)
            else:
                return int(addr)
        return 0
        
    def resolve_pointer_path(self, base_addr, offsets):
        """解析多级指针路径"""
        if not self.process_handle or not base_addr:
            return 0
            
        # 从缓存中获取地址
        cache_key = f"ptr_path_{base_addr}_{str(offsets)}"
        cached_addr = self.get_cached_address(cache_key)
        if cached_addr:
            return cached_addr
            
        try:
            addr = base_addr
            
            # 第一个元素可能是模块相对地址
            if isinstance(offsets[0], str) and offsets[0].startswith('0x'):
                # 将相对地址转为绝对地址
                addr = self.module_base_addr + self.parse_address(offsets[0])
                offsets = offsets[1:]  # 移除第一个偏移量
                
            # 遍历所有偏移量（除了最后一个）
            for i in range(len(offsets) - 1):
                addr = self.read_int(addr)
                if not addr:
                    logger.debug(f"指针路径解析中断，第{i+1}级指针为0")
                    return 0
                addr += offsets[i + 1]
                
            # 缓存结果
            self.set_cached_address(cache_key, addr)
            return addr
            
        except Exception as e:
            logger.error(f"解析指针路径出错: {str(e)}")
            return 0
        
    def read_bytes(self, address, size):
        """从内存读取字节数据"""
        if not self.process_handle or address <= 0:
            return None
            
        # 创建缓冲区接收数据
        buffer = ctypes.create_string_buffer(size)
        bytes_read = ctypes.c_size_t(0)
        
        # 调用ReadProcessMemory进行读取
        result = ReadProcessMemory(
            self.process_handle, 
            ctypes.c_void_p(address), 
            buffer, 
            size, 
            ctypes.byref(bytes_read)
        )
        
        if not result or bytes_read.value != size:
            error_code = ctypes.get_last_error()
            if self.debug:
                logger.debug(f"读取内存失败: 地址 0x{address:X}, 错误码: {error_code}")
            return None
            
        return bytes(buffer)
        
    def read_int(self, address, size=4):
        """读取整数"""
        data = self.read_bytes(address, size)
        if not data:
            return 0
            
        if size == 4:
            return struct.unpack("<I", data)[0]  # 小端序32位整数
        elif size == 8:
            return struct.unpack("<Q", data)[0]  # 小端序64位整数
        elif size == 2:
            return struct.unpack("<H", data)[0]  # 小端序16位整数
        elif size == 1:
            return struct.unpack("<B", data)[0]  # 无符号字节
        else:
            return 0
            
    def read_int64(self, address):
        """读取64位整数"""
        try:
            if not self.process_handle or not address:
                return 0
                
            buffer = ctypes.c_ulonglong()
            bytes_read = ctypes.c_size_t()
            
            if not ReadProcessMemory(
                self.process_handle, 
                ctypes.c_void_p(address),
                ctypes.byref(buffer),
                ctypes.sizeof(buffer),
                ctypes.byref(bytes_read)
            ):
                return 0
                
            return buffer.value
        except Exception as e:
            logger.debug(f"读取64位整数失败: {str(e)}")
            return 0
            
    def read_float(self, address):
        """读取浮点数"""
        data = self.read_bytes(address, 4)
        if not data:
            return 0.0
        return struct.unpack("<f", data)[0]
            
    def read_string(self, address, max_length=128):
        """读取以null结尾的字符串"""
        if not address:
            return ""
            
        # 从缓存获取
        cache_key = f"str_{address}_{max_length}"
        cached_value = self.get_cached_value(cache_key)
        if cached_value:
            return cached_value
            
        try:
            result = ""
            offset = 0
            
            # 逐块读取字符串，直到遇到null或达到最大长度
            while offset < max_length:
                chunk_size = min(32, max_length - offset)  # 每次读取最多32字节
                data = self.read_bytes(address + offset, chunk_size)
                
                if not data:
                    break
                    
                # 查找null终止符
                null_pos = data.find(b'\x00')
                if null_pos != -1:
                    # 找到终止符，提取字符串并结束
                    result += data[:null_pos].decode('utf-8', errors='ignore')
                    break
                else:
                    # 未找到终止符，继续读取
                    result += data.decode('utf-8', errors='ignore')
                    offset += chunk_size
                    
            # 缓存结果
            self.set_cached_value(cache_key, result)
            return result
            
        except Exception as e:
            if self.debug:
                logger.debug(f"读取字符串出错: {str(e)}")
            return ""
            
    def write_bytes(self, address, data):
        """写入字节数据到内存"""
        if not self.process_handle or address <= 0:
            return False
            
        bytes_written = ctypes.c_size_t(0)
        size = len(data)
        
        # 调用WriteProcessMemory进行写入
        result = WriteProcessMemory(
            self.process_handle,
            ctypes.c_void_p(address),
            data,
            size,
            ctypes.byref(bytes_written)
        )
        
        if not result or bytes_written.value != size:
            error_code = ctypes.get_last_error()
            logger.error(f"写入内存失败: 地址 0x{address:X}, 错误码: {error_code}")
            return False
            
        # 修改后清除缓存
        self.clear_cache()
        return True
        
    def write_int(self, address, value, size=4):
        """写入整数到内存"""
        if size == 4:
            data = struct.pack("<I", value)  # 小端序32位整数
        elif size == 8:
            data = struct.pack("<Q", value)  # 小端序64位整数
        elif size == 2:
            data = struct.pack("<H", value)  # 小端序16位整数
        elif size == 1:
            data = struct.pack("<B", value)  # 无符号字节
        else:
            return False
            
        return self.write_bytes(address, data)
        
    def write_float(self, address, value):
        """写入浮点数到内存"""
        data = struct.pack("<f", value)
        return self.write_bytes(address, data)
        
    def scan_memory_region(self, pattern, start_addr=None, end_addr=None, region_filter=None):
        """扫描内存区域，寻找匹配模式"""
        results = []
        
        # 更新内存页信息
        self.update_memory_pages()
        
        # 如果未指定地址范围，扫描所有内存页
        if start_addr is None and end_addr is None:
            for page in self.memory_pages:
                # 应用过滤器
                if region_filter and not region_filter(page):
                    continue
                    
                if not page.is_readable():
                    continue
                    
                # 读取页面内容
                data = self.read_bytes(page.base_address, page.size)
                if not data:
                    continue
                    
                # 查找模式匹配
                pos = 0
                while True:
                    pos = data.find(pattern, pos)
                    if pos == -1:
                        break
                    results.append(page.base_address + pos)
                    pos += 1
                    
        # 指定地址范围的扫描
        else:
            if start_addr is None:
                start_addr = 0
            if end_addr is None:
                end_addr = 0xFFFFFFFF
                
            # 分块扫描，防止内存过大
            chunk_size = 4096
            for addr in range(start_addr, end_addr, chunk_size):  # 每4KB扫描一次
                data = self.read_bytes(addr, min(chunk_size, end_addr - addr))
                if not data:
                    continue
                    
                # 查找模式匹配
                pos = 0
                while True:
                    pos = data.find(pattern, pos)
                    if pos == -1:
                        break
                    results.append(addr + pos)
                    pos += 1
        
        return results
        
    def update_memory_pages(self):
        """更新内存页信息缓存"""
        current_time = time.time()
        
        # 如果缓存不存在或已过期（60秒超时）
        if not self.memory_pages or current_time - self.memory_page_cache_time > 60:
            self.memory_pages = []
            
            if not self.process_handle:
                return
                
            try:
                addr = 0
                mbi = MEMORY_BASIC_INFORMATION()
                
                # 遍历整个进程地址空间
                while True:
                    if VirtualQueryEx(self.process_handle, addr, ctypes.byref(mbi), ctypes.sizeof(mbi)) == 0:
                        break
                        
                    # 创建内存页信息对象
                    page_info = MemoryPageInfo(
                        base_address=mbi.BaseAddress,
                        size=mbi.RegionSize,
                        protection=mbi.Protect,
                        state=mbi.State,
                        type=mbi.Type
                    )
                    
                    # 添加到内存页列表
                    self.memory_pages.append(page_info)
                    
                    # 移动到下一个区域
                    addr = mbi.BaseAddress + mbi.RegionSize
                    
                # 更新缓存时间
                self.memory_page_cache_time = current_time
                logger.debug(f"已更新内存页信息，共 {len(self.memory_pages)} 页")
                
            except Exception as e:
                logger.error(f"更新内存页信息失败: {str(e)}")
                
    def initialize_character_base(self):
        """初始化角色基址"""
        try:
            if self.test_mode:
                logger.debug("测试模式下不初始化角色基址")
                return False
                
            if not self.process_handle or not self.module_base_addr:
                logger.warning("进程句柄或模块基址无效，无法初始化角色基址")
                return False
                
            # 获取游戏版本，加载对应的偏移
            self.detect_game_version()
            
            # 扫描常见偏移尝试
            probable_offsets = [
                0x01D231AC, 0x01D53180, 0x01D63180, 0x01D73180, 
                0x01D83180, 0x01D93180, 0x01DA3180, 0x01C00000,
                0x00D231AC, 0x00D53180, 0x08D231AC, 0x08D53180,
                0x01000000, 0x02000000, 0x03000000, 0x04000000
            ]
            
            # 1. 直接尝试常见偏移获取角色基址
            for offset in probable_offsets:
                try:
                    addr = self.module_base_addr + offset
                    # 直接尝试读取角色数据
                    hp = self.read_float(addr + 0x5B8)
                    mp = self.read_float(addr + 0x5BC)
                    level = self.read_int(addr + 0x37C)
                    
                    # 检查读取的值是否合理
                    if 0 < hp < 100000 and 0 < mp < 100000 and 1 <= level <= 99:
                        logger.info(f"通过直接偏移找到角色基址 0x{addr:X}，HP={hp}, MP={mp}, Level={level}")
                        self.character_base_addr = addr
                        return True
                except Exception as e:
                    pass
            
            # 2. 尝试通过指针链获取角色基址
            pointer_chains = [
                # 常见的指针链配置
                [0x01D231AC, 0x4, 0x8, 0xC, 0x10, 0x20],
                [0x01D53180, 0x4, 0x8, 0xC, 0x10, 0x20],
                [0x01D00000, 0x18, 0x20, 0x28, 0x30],
                [0x01C00000, 0x10, 0x18, 0x20, 0x28],
                [0x00800000, 0x8, 0x10, 0x18, 0x20]
            ]
            
            for base_offset, *chain in pointer_chains:
                try:
                    base_addr = self.module_base_addr + base_offset
                    addr = base_addr
                    
                    # 跟踪指针链
                    valid_chain = True
                    for offset in chain:
                        addr = self.read_int64(addr)
                        if not addr:
                            valid_chain = False
                            break
                        addr += offset
                    
                    if not valid_chain:
                        continue
                    
                    # 验证找到的地址
                    hp = self.read_float(addr + 0x5B8)
                    mp = self.read_float(addr + 0x5BC)
                    
                    if 0 < hp < 100000 and 0 < mp < 100000:
                        logger.info(f"通过指针链找到角色基址 0x{addr:X}，HP={hp}, MP={mp}")
                        self.character_base_addr = addr
                        return True
                except Exception as e:
                    pass
            
            # 3. 内存扫描查找特征
            try:
                start_addr = self.module_base_addr
                end_addr = start_addr + 0x10000000  # 扫描16MB区域
                
                for addr in range(start_addr, end_addr, 4096):  # 每4KB扫描一次
                    # 检查这个区域是否可能包含角色数据
                    hp = self.read_float(addr + 0x5B8)
                    mp = self.read_float(addr + 0x5BC)
                    level = self.read_int(addr + 0x37C)
                    
                    if 0 < hp < 100000 and 0 < mp < 100000 and 1 <= level <= 99:
                        logger.info(f"通过扫描找到角色基址 0x{addr:X}，HP={hp}, MP={mp}, Level={level}")
                        self.character_base_addr = addr
                        return True
                        
                    # 避免过度扫描
                    if addr % 0x100000 == 0:  # 每1MB记录一次
                        logger.debug(f"内存扫描进度: 0x{addr:X}")
            except Exception as e:
                logger.debug(f"内存扫描出错: {str(e)}")
            
            # 如果所有方法都失败，切换到测试模式
            logger.warning("无法获取角色基址，切换到测试模式")
            self.test_mode = True
            return False
            
        except Exception as e:
            logger.error(f"初始化角色基址时出错: {str(e)}")
            self.test_mode = True
            return False
    
    def follow_pointer_chain(self, base_addr, offsets):
        """跟踪指针链获取最终地址"""
        if not base_addr:
            return 0
            
        addr = base_addr
        
        for i, offset in enumerate(offsets):
            addr = self.read_int64(addr)
            if not addr:
                logger.debug(f"指针链断裂于第{i+1}步")
                return 0
                
            if i < len(offsets) - 1:
                addr += offset
                
        return addr
    
    def get_module_base(self, module_name):
        """获取模块基址"""
        try:
            if not self.process_id:
                return 0
                
            modules = self.get_process_modules(self.process_id)
            for module in modules:
                if module['name'].lower() == module_name.lower():
                    return module['base']
            return 0
        except Exception as e:
            logger.debug(f"获取模块基址失败: {str(e)}")
            return 0
            
    def get_process_modules(self, pid):
        """获取进程模块列表"""
        try:
            modules = []
            if not pid:
                return modules
                
            # 首先尝试使用psutil获取模块信息
            try:
                import psutil
                process = psutil.Process(pid)
                for module in process.memory_maps():
                    module_path = module.path
                    module_name = os.path.basename(module_path)
                    base_addr = int(module.addr.split('-')[0], 16)
                    modules.append({
                        'name': module_name,
                        'path': module_path,
                        'base': base_addr
                    })
                
                if modules:
                    logger.info(f"使用psutil成功获取了{len(modules)}个模块信息")
                    return modules
            except Exception as e:
                logger.debug(f"使用psutil获取模块信息失败: {str(e)}")
            
            # 如果psutil失败，使用备用方法 - 使用EnumProcessModules
            try:
                hProcess = windll.kernel32.OpenProcess(0x1F0FFF, False, pid)
                if not hProcess:
                    logger.debug(f"无法打开进程{pid}以获取模块信息")
                    return modules
                    
                try:
                    # 使用CreateToolhelp32Snapshot获取进程模块
                    TH32CS_SNAPMODULE = 0x00000008
                    TH32CS_SNAPMODULE32 = 0x00000010
                    
                    hModuleSnap = windll.kernel32.CreateToolhelp32Snapshot(
                        TH32CS_SNAPMODULE | TH32CS_SNAPMODULE32, pid)
                    
                    if hModuleSnap != INVALID_HANDLE_VALUE:
                        try:
                            module_entry = MODULEENTRY32()
                            module_entry.dwSize = ctypes.sizeof(module_entry)
                            
                            if windll.kernel32.Module32First(hModuleSnap, ctypes.byref(module_entry)):
                                while True:
                                    modules.append({
                                        'name': module_entry.szModule.decode('utf-8', errors='ignore'),
                                        'path': module_entry.szExePath.decode('utf-8', errors='ignore'),
                                        'base': module_entry.modBaseAddr
                                    })
                                    
                                    if not windll.kernel32.Module32Next(hModuleSnap, ctypes.byref(module_entry)):
                                        break
                        finally:
                            windll.kernel32.CloseHandle(hModuleSnap)
                    
                    if modules:
                        logger.info(f"使用CreateToolhelp32Snapshot成功获取了{len(modules)}个模块信息")
                        return modules
                        
                    # 如果上面都失败了，尝试使用EnumProcessModules
                    hModules = (ctypes.c_void_p * 1024)()
                    cbNeeded = ctypes.c_ulong()
                    
                    if windll.psapi.EnumProcessModules(
                        hProcess, 
                        ctypes.byref(hModules), 
                        ctypes.sizeof(hModules), 
                        ctypes.byref(cbNeeded)
                    ):
                        for i in range(cbNeeded.value // ctypes.sizeof(ctypes.c_void_p)):
                            hMod = hModules[i]
                            
                            # 获取模块名
                            szModName = ctypes.create_string_buffer(260)
                            if windll.psapi.GetModuleBaseNameA(
                                hProcess, hMod, szModName, ctypes.sizeof(szModName)
                            ):
                                # 获取模块路径
                                szModPath = ctypes.create_string_buffer(260)
                                if windll.psapi.GetModuleFileNameExA(
                                    hProcess, hMod, szModPath, ctypes.sizeof(szModPath)
                                ):
                                    modules.append({
                                        'name': szModName.value.decode('utf-8', errors='ignore'),
                                        'path': szModPath.value.decode('utf-8', errors='ignore'),
                                        'base': hMod
                                    })
                                    
                            # 如果获取了足够的模块（防止无限循环）
                            if len(modules) >= 200:
                                break
                                
                    if modules:
                        logger.info(f"使用EnumProcessModules成功获取了{len(modules)}个模块信息")
                        
                finally:
                    windll.kernel32.CloseHandle(hProcess)
                    
            except Exception as e:
                logger.debug(f"获取进程模块信息失败: {str(e)}")
                
            # 没有找到模块，尝试手动添加一些常见的猜测模块
            if not modules:
                # 添加一些常见的模块，仅用于测试
                logger.warning("无法通过常规方法获取模块，使用预设模块进行尝试")
                modules = [
                    {'name': 'Game.dll', 'path': 'C:\\Program Files\\Aion\\bin32\\Game.dll', 'base': 0x10000000},
                    {'name': 'aion.exe', 'path': 'C:\\Program Files\\Aion\\bin32\\aion.exe', 'base': 0x00400000},
                    {'name': 'system.dll', 'path': 'C:\\Program Files\\Aion\\bin32\\system.dll', 'base': 0x20000000}
                ]
                
            return modules
        except Exception as e:
            logger.error(f"获取进程模块列表失败: {str(e)}")
            return []
        
    def get_skill_cooldown(self, skill_id):
        """获取技能冷却时间"""
        if not self.process_handle or not self.offsets:
            logger.debug(f"无法获取技能冷却，进程句柄或偏移量无效")
            return 999.0  # 返回一个大值表示无法使用
        
        try:
            # 从缓存获取
            cache_key = f"skill_cd_{skill_id}"
            cached_value = self.get_cached_value(cache_key)
            if cached_value is not None:
                return cached_value
                
            # 尝试获取技能冷却数据
            cooldown = 999.0
            
            # 方法1: 使用技能基址+偏移
            if "skills" in self.offsets and "cooldown_base" in self.offsets["skills"]:
                cooldown_base = self.parse_address(self.offsets["skills"]["cooldown_base"])
                if isinstance(cooldown_base, str) and cooldown_base.startswith("0x"):
                    cooldown_base = self.module_base_addr + int(cooldown_base, 16)
                
                skill_offset_name = f"skill_{skill_id}_cd"
                if skill_offset_name in self.offsets["skills"]:
                    skill_offset = self.offsets["skills"][skill_offset_name]
                    cooldown_raw = self.read_float(cooldown_base + skill_offset)
                    
                    if cooldown_raw is not None:
                        cooldown = cooldown_raw
                        logger.debug(f"技能 {skill_id} 冷却时间: {cooldown}秒")
            
            # 方法2: 使用技能ID映射表
            if cooldown == 999.0 and "skill_mapping" in self.offsets:
                mapping = self.offsets["skill_mapping"]
                if str(skill_id) in mapping:
                    internal_id = mapping[str(skill_id)]
                    # 再次查找对应的偏移
                    if "skill_cooldowns" in self.offsets:
                        cd_base = self.parse_address(self.offsets["skill_cooldowns"]["base"])
                        if isinstance(cd_base, str) and cd_base.startswith("0x"):
                            cd_base = self.module_base_addr + int(cd_base, 16)
                            
                        cd_stride = self.offsets["skill_cooldowns"].get("stride", 8)
                        cd_offset = self.offsets["skill_cooldowns"].get("time_offset", 4)
                        
                        # 计算具体技能的冷却时间地址
                        cooldown_addr = cd_base + internal_id * cd_stride + cd_offset
                        cooldown_raw = self.read_float(cooldown_addr)
                        
                        if cooldown_raw is not None:
                            cooldown = cooldown_raw
                            logger.debug(f"技能 {skill_id} (内部ID: {internal_id}) 冷却时间: {cooldown}秒")
            
            # 缓存结果
            self.set_cached_value(cache_key, cooldown)
            return cooldown
            
        except Exception as e:
            logger.error(f"获取技能冷却失败: {str(e)}")
            return 999.0
    
    def get_combat_state(self):
        """获取角色战斗状态详细信息
        
        返回:
            字典，包含以下字段:
            - in_combat: 是否在战斗中
            - last_hit_time: 上次命中时间
            - last_damage_dealt: 上次造成的伤害
            - last_damage_taken: 上次受到的伤害
            - target_hp_change: 目标血量变化
            - hit_confirmed: 是否确认命中
        """
        # 缓存键
        cache_key = "combat_state"
        
        # 检查缓存
        if self.cache_enabled:
            cached = self.get_cached_value(cache_key)
            if cached:
                return cached
        
        # 初始化返回值
        combat_state = {
            "in_combat": False,
            "last_hit_time": 0,
            "last_damage_dealt": 0,
            "last_damage_taken": 0,
            "target_hp_change": 0,
            "hit_confirmed": False,
            "target_hp": 0,
            "target_hp_max": 0,
            "target_hp_percent": 0,
            "last_skill_hit": None,
            "combat_flags": 0
        }
        
        # 如果处于测试模式，生成模拟数据
        if self.test_mode:
            combat_state["in_combat"] = random.choice([True, False])
            combat_state["last_hit_time"] = time.time() - random.uniform(0, 3)
            combat_state["last_damage_dealt"] = random.randint(10, 500)
            combat_state["last_damage_taken"] = random.randint(0, 100)
            combat_state["target_hp_change"] = -random.randint(10, 500)
            combat_state["hit_confirmed"] = random.choice([True, False])
            combat_state["target_hp"] = random.randint(100, 10000)
            combat_state["target_hp_max"] = 10000
            combat_state["target_hp_percent"] = combat_state["target_hp"] / combat_state["target_hp_max"] * 100
            combat_state["last_skill_hit"] = random.choice(["attack", "skill1", "skill2", None])
            combat_state["combat_flags"] = random.randint(0, 7)
            
            # 缓存结果
            if self.cache_enabled:
                self.set_cached_value(cache_key, combat_state)
                
            return combat_state
        
        # 如果没有进程句柄，返回默认值
        if not self.process_handle:
            return combat_state
        
        try:
            # 获取角色状态
            char_status = self.get_character_status()
            if not char_status:
                return combat_state
                
            # 获取战斗状态
            combat_state["in_combat"] = char_status.get("combat_state", False)
            
            # 获取目标信息
            target = self.get_current_target()
            if target:
                # 记录当前目标血量
                current_target_hp = target.get("hp", 0)
                current_target_hp_max = target.get("hp_max", 100)
                
                # 更新目标血量信息
                combat_state["target_hp"] = current_target_hp
                combat_state["target_hp_max"] = current_target_hp_max
                combat_state["target_hp_percent"] = (current_target_hp / current_target_hp_max * 100) if current_target_hp_max > 0 else 0
                
                # 检查目标血量变化
                target_id = target.get("id", 0)
                if target_id in self.combat_state_cache:
                    prev_target_hp = self.combat_state_cache[target_id].get("target_hp", current_target_hp)
                    hp_change = prev_target_hp - current_target_hp
                    
                    # 如果血量减少，认为是造成了伤害
                    if hp_change > 0:
                        combat_state["target_hp_change"] = hp_change
                        combat_state["last_damage_dealt"] = hp_change
                        combat_state["last_hit_time"] = time.time()
                        combat_state["hit_confirmed"] = True
                        
                        # 记录伤害历史
                        self.damage_history.append({
                            "time": time.time(),
                            "damage": hp_change,
                            "target_id": target_id,
                            "target_hp": current_target_hp,
                            "target_hp_max": current_target_hp_max
                        })
                        
                        # 限制历史记录长度
                        if len(self.damage_history) > self.max_damage_history:
                            self.damage_history.pop(0)
            
            # 读取战斗标志位（如果有）
            if self.character_base_addr and "combat_flags_offset" in self.offsets:
                combat_flags = self.read_int(self.character_base_addr + self.offsets["combat_flags_offset"])
                combat_state["combat_flags"] = combat_flags
                
                # 解析战斗标志位
                # 这里需要根据游戏具体实现来解析，以下仅为示例
                # 比如: 0x01=普通攻击命中, 0x02=技能命中, 0x04=暴击, 0x08=被命中
                if combat_flags & 0x01:
                    combat_state["hit_confirmed"] = True
                    combat_state["last_skill_hit"] = "attack"
                if combat_flags & 0x02:
                    combat_state["hit_confirmed"] = True
                    combat_state["last_skill_hit"] = "skill"
            
            # 更新缓存
            if target:
                self.combat_state_cache[target.get("id", 0)] = combat_state
                
            # 缓存结果
            if self.cache_enabled:
                self.set_cached_value(cache_key, combat_state)
                
            return combat_state
            
        except Exception as e:
            logger.error(f"获取战斗状态失败: {str(e)}")
            return combat_state

    def is_target_hit_confirmed(self):
        """检查目标是否被命中确认
        
        返回:
            bool: 如果目标被命中确认，返回True；否则返回False
        """
        # 获取战斗状态
        combat_state = self.get_combat_state()
        
        # 检查是否确认命中
        if combat_state["hit_confirmed"]:
            return True
            
        # 检查目标血量变化
        if combat_state["target_hp_change"] > 0:
            return True
            
        # 检查战斗标志位
        if combat_state["combat_flags"] & 0x03:  # 0x01 | 0x02 = 0x03 (攻击或技能命中)
            return True
            
        # 检查伤害历史
        current_time = time.time()
        for damage_record in self.damage_history:
            # 检查最近3秒内是否有伤害记录
            if current_time - damage_record["time"] < 3.0:
                return True
                
        return False
        
    def get_target_damage_info(self):
        """获取目标伤害信息
        
        返回:
            字典，包含以下字段:
            - total_damage: 总伤害
            - avg_damage: 平均伤害
            - max_damage: 最大伤害
            - hit_count: 命中次数
            - dps: 每秒伤害
        """
        if not self.damage_history:
            return {
                "total_damage": 0,
                "avg_damage": 0,
                "max_damage": 0,
                "hit_count": 0,
                "dps": 0
            }
            
        # 计算总伤害和最大伤害
        total_damage = sum(record["damage"] for record in self.damage_history)
        max_damage = max(record["damage"] for record in self.damage_history)
        hit_count = len(self.damage_history)
        
        # 计算平均伤害
        avg_damage = total_damage / hit_count if hit_count > 0 else 0
        
        # 计算DPS
        current_time = time.time()
        oldest_time = min(record["time"] for record in self.damage_history)
        time_span = current_time - oldest_time
        dps = total_damage / time_span if time_span > 0 else 0
        
        return {
            "total_damage": total_damage,
            "avg_damage": avg_damage,
            "max_damage": max_damage,
            "hit_count": hit_count,
            "dps": dps
        }
        
    def clear_combat_cache(self):
        """清除战斗状态缓存"""
        self.combat_state_cache.clear()
        self.damage_history.clear()
        
    def get_processes_list(self):
        """获取系统进程列表"""
        try:
            import psutil
            processes = []
            
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    # 检查是否是游戏进程
                    name = proc.info['name'].lower()
                    is_aion = 'game' in name or 'aion' in name
                    
                    processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'is_aion': is_aion
                    })
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass
                    
            # 按名称排序，游戏相关进程优先
            processes.sort(key=lambda x: (not x['is_aion'], x['name'].lower()))
            
            return processes
        except Exception as e:
            logger.error(f"获取进程列表失败: {str(e)}")
            return []
            
    def get_character_status(self):
        """获取角色状态信息"""
        # 如果已在测试模式，直接返回测试数据
        if self.test_mode:
            return self.generate_test_character_data()
        
        # 检查角色基址是否有效
        if not self.process_handle or self.character_base_addr == 0:
            # 尝试初始化
            if not self.initialize_character_base():
                # 如果初始化失败且允许自动测试模式，切换到测试模式
                if self.auto_test_mode:
                    logger.warning("无法获取角色基址，切换到测试模式")
                    self.test_mode = True
                    return self.generate_test_character_data()
                return None
        
        # 缓存键
        cache_key = f"character_status_{self.character_base_addr}"
        cached = self.get_cached_value(cache_key)
        if cached:
            return cached
        
        # 角色状态结构
        status = {
            "id": 0,
            "level": 0,
            "hp": 0,
            "hp_max": 0,
            "mp": 0,
            "mp_max": 0,
            "position": [0, 0, 0],
            "target_id": 0,
            "combat_state": False,
            "moving": False,
            "casting": False,
            "timestamp": time.time()
        }
        
        # 读取各项状态数据
        offsets = self.offsets.get("character_base", {})
        
        # 尝试多种方法读取HP/MP
        success = False
        
        # 方法1: 直接从基址+偏移量读取
        try:
            if "hp_offset" in offsets:
                status["hp"] = self.read_float(self.character_base_addr + offsets["hp_offset"])
                status["hp_max"] = self.read_float(self.character_base_addr + offsets.get("hp_max_offset", offsets["hp_offset"] + 4))
                
            if "mp_offset" in offsets:
                status["mp"] = self.read_float(self.character_base_addr + offsets["mp_offset"])
                status["mp_max"] = self.read_float(self.character_base_addr + offsets.get("mp_max_offset", offsets["mp_offset"] + 4))
                
            # 检查读取的值是否合理
            if status["hp"] > 0 and status["hp_max"] > 0 and status["mp"] >= 0:
                success = True
                logger.debug(f"方法1成功读取角色HP: {status['hp']}/{status['hp_max']}, MP: {status['mp']}/{status['mp_max']}")
        except Exception as e:
            logger.debug(f"方法1读取失败: {str(e)}")
        
        # 如果所有方法都失败，在调试模式下生成测试数据
        if not success:
            if self.debug and self.test_mode:
                # 使用测试数据
                if not self.test_character_data:
                    self.test_character_data = self.generate_test_character_data()
                logger.debug("内存读取失败，使用测试角色数据")
                return self.test_character_data
            else:
                logger.error("所有读取角色血量/魔法值的方法都失败")
                return None
        
        # 缓存结果
        self.set_cached_value(cache_key, status)
        return status
        
    def generate_test_character_data(self):
        """生成测试角色数据"""
        return {
            "id": 12345,
            "name": "测试角色",
            "level": 55,
            "hp": 8500,
            "hp_max": 10000,
            "mp": 4000,
            "mp_max": 5000,
            "position": [
                np.random.uniform(1000, 1500),
                np.random.uniform(800, 1000),
                np.random.uniform(100, 120)
            ],
            "target_id": 0,
            "combat_state": np.random.choice([True, False], p=[0.3, 0.7]),
            "moving": np.random.choice([True, False], p=[0.2, 0.8]),
            "casting": False,
            "target": self.generate_test_target_data() if np.random.random() < 0.7 else None
        }
        
    def generate_test_target_data(self):
        """生成测试目标数据"""
        target_id = np.random.randint(10000, 20000)
        
        # 确保生成的目标是列表中的一个怪物
        test_monsters = self.generate_test_monster_data()
        if test_monsters:
            for monster in test_monsters:
                if monster["id"] == target_id:
                    return monster
        
        # 如果没有匹配的怪物，创建一个新的目标数据
        return {
            "id": target_id,
            "name": f"测试目标-{target_id}",
            "level": np.random.randint(50, 60),
            "hp": np.random.uniform(0.3, 1.0),
            "distance": np.random.uniform(5.0, 25.0),
            "position": [
                np.random.uniform(1000, 1500),
                np.random.uniform(800, 1000),
                np.random.uniform(100, 120)
            ],
            "type": np.random.choice([0, 1, 2], p=[0.7, 0.2, 0.1])  # 0=普通, 1=精英, 2=BOSS
        }
        
    def get_current_target(self):
        """获取当前选中的目标"""
        try:
            # 如果处于测试模式，返回测试数据
            if self.test_mode:
                character = self.get_character_status()
                if character and 'target' in character:
                    return character['target']
                return None
                
            # 获取角色状态
            character = self.get_character_status()
            if not character or 'target_id' not in character:
                return None
                
            target_id = character['target_id']
            if not target_id or target_id <= 0:
                return None
                
            # 获取所有怪物
            monsters = self.get_nearby_monsters(100.0)
            
            # 查找匹配ID的怪物
            for monster in monsters:
                if monster.get('id') == target_id:
                    return monster
            
            # 如果没有在怪物列表中找到，可能是NPC或玩家
            return {
                "id": target_id,
                "name": "未知目标",
                "type": -1  # 未知类型
            }
            
        except Exception as e:
            logger.debug(f"获取当前目标失败: {str(e)}")
            return None
            
    def get_nearby_monsters(self, max_distance=100.0):
        """获取附近的怪物列表"""
        # 测试模式下使用测试数据
        if self.test_mode:
            current_time = time.time()
            
            # 如果测试数据为空或者超过更新间隔，重新生成测试数据
            if (self.test_monsters_data is None or 
                current_time - self.last_test_data_update > self.test_data_interval):
                self.test_monsters_data = self.generate_test_monster_data()
                self.last_test_data_update = current_time
            
            # 过滤掉超出距离的怪物
            filtered_monsters = [m for m in self.test_monsters_data if m['distance'] <= max_distance]
            return filtered_monsters
            
        # 如果不是测试模式，返回空列表
        return []
        
    def generate_test_monster_data(self, count=None):
        """生成测试怪物数据"""
        monsters = []
        # 生成5-10个随机怪物，或者指定数量
        num_monsters = count if count is not None else np.random.randint(5, 11)
        
        for i in range(num_monsters):
            # 随机生成距离在5-50米之间的怪物
            distance = np.random.uniform(5, 50)
            
            # 随机生成等级在角色前后5级的怪物
            char_level = 50  # 假设角色等级是50
            level = max(1, char_level + np.random.randint(-5, 6))
            
            # 随机生成血量（根据等级调整）
            hp_max = level * 100 + np.random.randint(0, 500)
            hp = hp_max * np.random.uniform(0.3, 1.0)
            
            # 随机生成位置
            angle = np.random.uniform(0, 2 * np.pi)
            char_pos = [1250.0, 850.0, 100.0]  # 假设的角色位置
            pos_x = char_pos[0] + distance * np.cos(angle)
            pos_y = char_pos[1] + distance * np.sin(angle)
            pos_z = char_pos[2] + np.random.uniform(-5, 5)
            
            monster = {
                "id": 10000 + i,
                "type": np.random.randint(1, 4),  # 随机类型1-3
                "level": level,
                "hp": hp,
                "hp_max": hp_max,
                "position": [pos_x, pos_y, pos_z],
                "distance": distance,
                "name": f"测试怪物_{i+1}"
            }
            
            monsters.append(monster)
        
        # 按距离排序
        monsters.sort(key=lambda m: m["distance"])
        
        return monsters
    
    def get_skill_bar_data(self):
        """获取技能栏数据"""
        # 测试模式下返回模拟数据
        if self.test_mode:
            skills = {}
            for i in range(1, 10):
                skills[f"skill{i}"] = {
                    "id": 1000 + i,
                    "name": f"技能{i}",
                    "cooldown": 0,
                    "max_cooldown": 5.0 if i > 5 else 2.0,
                    "icon": None,
                    "level": 1,
                    "type": "attack" if i < 7 else "buff"
                }
            return skills
        
        # 非测试模式下返回空字典
        return {}