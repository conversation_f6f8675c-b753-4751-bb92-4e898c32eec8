#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI修复测试脚本 - 验证目标检测修复
"""

import sys
import os
import time
import logging

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志级别为INFO以查看详细输出
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_ai_target_detection():
    """测试AI目标检测修复"""
    print("=" * 60)
    print("AI目标检测修复测试")
    print("=" * 60)
    
    try:
        from tactical_ai import TacticalAI
        from screen_detector import ScreenDetector
        
        # 创建模拟的内存读取器
        class MockMemoryReader:
            def __init__(self):
                self.process_id = 1234
                self.module_base = 0x400000
        
        memory_reader = MockMemoryReader()
        screen_detector = ScreenDetector()
        
        # 创建AI实例
        ai = TacticalAI(memory_reader, screen_detector)
        ai.debug_mode = True  # 启用调试模式
        
        print("✓ 战术AI初始化成功")
        print(f"  - 调试模式: {ai.debug_mode}")
        print(f"  - 最大目标距离: {ai.max_target_distance}")
        print(f"  - 血量阈值: {ai.min_hp_threshold}")
        
        # 测试多次目标扫描
        print("\n进行5次目标扫描测试...")
        
        for i in range(5):
            print(f"\n--- 第 {i+1} 次扫描 ---")
            
            # 扫描目标
            targets = ai.scan_for_targets()
            
            if targets:
                print(f"✓ 找到 {len(targets)} 个目标")
                
                # 选择最佳目标
                best_target = ai.select_best_target(targets)
                if best_target:
                    print(f"✓ 选择最佳目标: ID={best_target.id}, 优先级={best_target.priority.name}")
                    
                    # 更新玩家状态
                    player_state = ai.update_player_state()
                    
                    # 决策战斗行动
                    action = ai.decide_combat_action(player_state, best_target)
                    print(f"✓ 战斗决策: {action['type']} - {action.get('skill', 'N/A')}")
                    
                    # 更新统计
                    ai.update_combat_statistics(action, True)
                    
                else:
                    print("✗ 未能选择最佳目标")
            else:
                print("✗ 未找到任何目标")
            
            time.sleep(0.5)  # 短暂延迟
        
        # 显示AI状态
        print("\n--- AI状态信息 ---")
        status = ai.get_ai_status()
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_battle_manager_integration():
    """测试战斗管理器集成"""
    print("\n" + "=" * 60)
    print("战斗管理器集成测试")
    print("=" * 60)
    
    try:
        from battle_logic import BattleManager
        from memory_reader import MemoryReader
        from input_simulator import InputSimulator
        from monster_select import MonsterSelector
        
        # 创建组件
        memory = MemoryReader()
        input_sim = InputSimulator()
        monster_selector = MonsterSelector(memory)
        battle_manager = BattleManager(memory, input_sim, monster_selector)
        
        print("✓ 战斗管理器初始化成功")
        
        # 启用AI模式
        battle_manager.toggle_ai_mode(True)
        battle_manager.toggle_smart_targeting(True)
        battle_manager.toggle_adaptive_skills(True)
        
        print("✓ AI功能已启用")
        
        # 模拟目标查找过程
        print("\n模拟AI目标查找过程...")
        
        for i in range(3):
            print(f"\n--- 模拟第 {i+1} 次目标查找 ---")
            
            # 调用AI目标查找
            result = battle_manager._ai_find_and_attack_target()
            
            if result:
                print("✓ AI成功找到并攻击目标")
            else:
                print("✗ AI未能找到目标")
            
            time.sleep(0.3)
        
        # 获取AI状态
        ai_status = battle_manager.get_ai_status()
        print(f"\n✓ AI状态获取成功: {len(ai_status)} 个状态项")
        
        return True
        
    except Exception as e:
        print(f"✗ 战斗管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("AI目标检测修复验证")
    print("=" * 60)
    
    # 测试1: AI目标检测
    test1_result = test_ai_target_detection()
    
    # 测试2: 战斗管理器集成
    test2_result = test_battle_manager_integration()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    print(f"AI目标检测: {'✓ 通过' if test1_result else '✗ 失败'}")
    print(f"战斗管理器集成: {'✓ 通过' if test2_result else '✗ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！AI目标检测问题已修复。")
        print("\n现在可以尝试重新运行自动战斗功能:")
        print("1. 启动游戏辅助程序")
        print("2. 连接到游戏进程")
        print("3. 启用AI增强模式")
        print("4. 开始副本辅助")
        print("\nAI现在应该能够:")
        print("- 检测到目标（真实或模拟）")
        print("- 选择最佳攻击目标")
        print("- 执行智能战斗决策")
        print("- 持续进行自动战斗")
    else:
        print("\n⚠ 部分测试失败，请检查错误信息。")
    
    return test1_result and test2_result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
