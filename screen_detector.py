#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
屏幕检测模块 - 提供找色、找图、找字等功能
"""

import os
import time
import logging
import numpy as np
import cv2
from PIL import Image, ImageGrab
import pyautogui
import threading

# 设置日志
logger = logging.getLogger('screen_detector')
logger.setLevel(logging.INFO)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class ScreenDetector:
    """屏幕检测类，提供找色、找图、找字等功能"""
    
    def __init__(self):
        """初始化屏幕检测器"""
        # 基本设置
        self.screen_width, self.screen_height = pyautogui.size()
        self.center_x, self.center_y = self.screen_width // 2, self.screen_height // 2
        
        # 找色设置
        self.color_detection_enabled = True
        self.color_detection_cooldown = 0.1  # 找色冷却时间
        self.last_color_detection_time = 0
        
        # 找图设置
        self.template_detection_enabled = True
        self.template_detection_cooldown = 0.2  # 找图冷却时间
        self.last_template_detection_time = 0
        self.template_cache = {}  # 模板缓存
        
        # 找字设置
        self.text_detection_enabled = True
        self.text_detection_cooldown = 0.5  # 找字冷却时间
        self.last_text_detection_time = 0
        
        # 检测区域设置
        self.default_detection_region = (
            self.center_x - 300,  # 左边界
            self.center_y - 200,  # 上边界
            self.center_x + 300,  # 右边界
            self.center_y + 200   # 下边界
        )
        
        # 伤害数字检测设置
        self.damage_detection_enabled = True
        self.damage_color_ranges = [
            # 红色伤害
            {
                'name': '普通伤害',
                'lower': np.array([0, 120, 120]),
                'upper': np.array([10, 255, 255])
            },
            # 黄色伤害（暴击）
            {
                'name': '暴击伤害',
                'lower': np.array([20, 150, 150]),
                'upper': np.array([30, 255, 255])
            },
            # 白色伤害（物理）
            {
                'name': '物理伤害',
                'lower': np.array([0, 0, 200]),
                'upper': np.array([180, 30, 255])
            }
        ]
        
        # 目标检测设置
        self.target_detection_enabled = True
        self.target_indicators = [
            # 红色目标指示器
            {
                'name': '敌对目标',
                'lower': np.array([0, 150, 150]),
                'upper': np.array([10, 255, 255])
            },
            # 绿色目标指示器
            {
                'name': '友好目标',
                'lower': np.array([45, 150, 150]),
                'upper': np.array([75, 255, 255])
            },
            # 黄色目标指示器
            {
                'name': '中立目标',
                'lower': np.array([20, 150, 150]),
                'upper': np.array([40, 255, 255])
            },
            # 白色目标指示器
            {
                'name': '通用目标',
                'lower': np.array([0, 0, 200]),
                'upper': np.array([180, 30, 255])
            }
        ]
        
        # 技能冷却检测设置
        self.skill_cooldown_detection_enabled = True
        self.skill_ready_color = {
            'name': '技能就绪',
            'lower': np.array([100, 150, 150]),
            'upper': np.array([140, 255, 255])
        }
        
        # 资源文件夹
        self.resource_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'resources')
        if not os.path.exists(self.resource_folder):
            os.makedirs(self.resource_folder)
            
        # 模板文件夹
        self.template_folder = os.path.join(self.resource_folder, 'templates')
        if not os.path.exists(self.template_folder):
            os.makedirs(self.template_folder)
            
        logger.info("屏幕检测器已初始化")
        
    def capture_screen(self, region=None):
        """截取屏幕指定区域
        
        参数:
            region: 截图区域，格式为(left, top, right, bottom)，默认为None表示全屏
            
        返回:
            numpy数组格式的截图
        """
        try:
            screenshot = ImageGrab.grab(bbox=region)
            return np.array(screenshot)
        except Exception as e:
            logger.error(f"截取屏幕失败: {str(e)}")
            return None
            
    def find_color(self, color_range, region=None, confidence=0.7):
        """在屏幕上查找指定颜色
        
        参数:
            color_range: 颜色范围，包含lower和upper两个numpy数组
            region: 查找区域，格式为(left, top, right, bottom)
            confidence: 置信度，0-1之间
            
        返回:
            找到颜色返回(x, y, w, h)，否则返回None
        """
        if not self.color_detection_enabled:
            return None
            
        current_time = time.time()
        if current_time - self.last_color_detection_time < self.color_detection_cooldown:
            return None
            
        self.last_color_detection_time = current_time
        
        try:
            # 截取屏幕
            screenshot = self.capture_screen(region)
            if screenshot is None:
                return None
                
            # 转换为HSV颜色空间
            hsv = cv2.cvtColor(screenshot, cv2.COLOR_RGB2HSV)
            
            # 创建颜色掩码
            mask = cv2.inRange(hsv, color_range['lower'], color_range['upper'])
            
            # 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 如果找到轮廓
            if contours:
                # 找到最大的轮廓
                max_contour = max(contours, key=cv2.contourArea)
                area = cv2.contourArea(max_contour)
                
                # 如果面积足够大
                if area > 50:
                    x, y, w, h = cv2.boundingRect(max_contour)
                    
                    # 如果指定了区域，需要加上区域的偏移
                    if region:
                        x += region[0]
                        y += region[1]
                        
                    logger.debug(f"找到颜色 {color_range['name']}: ({x}, {y}, {w}, {h})")
                    return (x, y, w, h)
            
            return None
            
        except Exception as e:
            logger.error(f"查找颜色失败: {str(e)}")
            return None
            
    def find_template(self, template_path, region=None, confidence=0.7):
        """在屏幕上查找模板图像
        
        参数:
            template_path: 模板图像路径
            region: 查找区域，格式为(left, top, right, bottom)
            confidence: 置信度，0-1之间
            
        返回:
            找到模板返回(x, y, w, h)，否则返回None
        """
        if not self.template_detection_enabled:
            return None
            
        current_time = time.time()
        if current_time - self.last_template_detection_time < self.template_detection_cooldown:
            return None
            
        self.last_template_detection_time = current_time
        
        try:
            # 加载模板图像
            if template_path in self.template_cache:
                template = self.template_cache[template_path]
            else:
                template_full_path = os.path.join(self.template_folder, template_path)
                if not os.path.exists(template_full_path):
                    logger.error(f"模板文件不存在: {template_full_path}")
                    return None
                    
                template = cv2.imread(template_full_path, cv2.IMREAD_COLOR)
                self.template_cache[template_path] = template
                
            if template is None:
                logger.error(f"无法加载模板: {template_path}")
                return None
                
            # 截取屏幕
            screenshot = self.capture_screen(region)
            if screenshot is None:
                return None
                
            # 转换为相同的颜色格式
            screenshot_bgr = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)
            
            # 模板匹配
            result = cv2.matchTemplate(screenshot_bgr, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            # 如果匹配度高于阈值
            if max_val >= confidence:
                x, y = max_loc
                w, h = template.shape[1], template.shape[0]
                
                # 如果指定了区域，需要加上区域的偏移
                if region:
                    x += region[0]
                    y += region[1]
                    
                logger.debug(f"找到模板 {template_path}: ({x}, {y}, {w}, {h}), 置信度: {max_val:.2f}")
                return (x, y, w, h)
            
            return None
            
        except Exception as e:
            logger.error(f"查找模板失败: {str(e)}")
            return None
            
    def find_text(self, text, region=None, lang='chi_sim'):
        """在屏幕上查找文字
        
        参数:
            text: 要查找的文字
            region: 查找区域，格式为(left, top, right, bottom)
            lang: OCR语言，默认为中文简体
            
        返回:
            找到文字返回(x, y, w, h)，否则返回None
        """
        if not self.text_detection_enabled:
            return None
            
        current_time = time.time()
        if current_time - self.last_text_detection_time < self.text_detection_cooldown:
            return None
            
        self.last_text_detection_time = current_time
        
        try:
            # 尝试导入pytesseract
            import pytesseract
            
            # 截取屏幕
            screenshot = self.capture_screen(region)
            if screenshot is None:
                return None
                
            # 转换为灰度图
            gray = cv2.cvtColor(screenshot, cv2.COLOR_RGB2GRAY)
            
            # 二值化
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # OCR识别
            result = pytesseract.image_to_data(binary, lang=lang, output_type=pytesseract.Output.DICT)
            
            # 查找匹配的文字
            for i in range(len(result['text'])):
                if text in result['text'][i]:
                    x = result['left'][i]
                    y = result['top'][i]
                    w = result['width'][i]
                    h = result['height'][i]
                    
                    # 如果指定了区域，需要加上区域的偏移
                    if region:
                        x += region[0]
                        y += region[1]
                        
                    logger.debug(f"找到文字 '{text}': ({x}, {y}, {w}, {h})")
                    return (x, y, w, h)
            
            return None
            
        except ImportError:
            logger.error("未安装pytesseract，无法使用文字识别功能")
            self.text_detection_enabled = False
            return None
        except Exception as e:
            logger.error(f"查找文字失败: {str(e)}")
            return None
            
    def detect_damage_numbers(self, region=None):
        """检测屏幕上的伤害数字
        
        参数:
            region: 检测区域，格式为(left, top, right, bottom)
            
        返回:
            检测到伤害数字返回True，否则返回False
        """
        if not self.damage_detection_enabled:
            return False
            
        if region is None:
            region = self.default_detection_region
            
        try:
            # 截取屏幕
            screenshot = self.capture_screen(region)
            if screenshot is None:
                return False
                
            # 转换为HSV颜色空间
            hsv = cv2.cvtColor(screenshot, cv2.COLOR_RGB2HSV)
            
            # 检查每种伤害颜色
            for color_range in self.damage_color_ranges:
                # 创建颜色掩码
                mask = cv2.inRange(hsv, color_range['lower'], color_range['upper'])
                
                # 查找轮廓
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # 如果找到足够大的轮廓，认为检测到伤害数字
                for contour in contours:
                    if cv2.contourArea(contour) > 50:  # 面积阈值，过滤小噪点
                        logger.debug(f"检测到{color_range['name']}")
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"检测伤害数字失败: {str(e)}")
            return False
            
    def detect_target_indicator(self, region=None):
        """检测目标指示器
        
        参数:
            region: 检测区域，格式为(left, top, right, bottom)
            
        返回:
            检测到目标指示器返回True，否则返回False
        """
        if not self.target_detection_enabled:
            return False
            
        if region is None:
            # 扩大检测区域，覆盖更多可能的目标指示器位置
            region = (
                self.center_x - 200,  # 左边界扩大
                self.center_y - 200,  # 上边界扩大
                self.center_x + 200,  # 右边界扩大
                self.center_y + 50    # 下边界调整为略高于中心
            )
            
        try:
            # 截取屏幕
            screenshot = self.capture_screen(region)
            if screenshot is None:
                return False
                
            # 转换为HSV颜色空间
            hsv = cv2.cvtColor(screenshot, cv2.COLOR_RGB2HSV)
            
            # 检查每种目标指示器颜色
            for indicator in self.target_indicators:
                # 创建颜色掩码
                mask = cv2.inRange(hsv, indicator['lower'], indicator['upper'])
                
                # 查找轮廓
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # 降低面积阈值，提高检测灵敏度
                for contour in contours:
                    if cv2.contourArea(contour) > 50:  # 降低面积阈值，从100降到50
                        logger.debug(f"检测到{indicator['name']}")
                        
                        # 保存检测到的目标指示器截图（调试用）
                        try:
                            timestamp = int(time.time())
                            debug_path = os.path.join(self.resource_folder, f"target_indicator_{timestamp}.png")
                            cv2.imwrite(debug_path, screenshot)
                            logger.debug(f"目标指示器截图已保存: {debug_path}")
                        except Exception as e:
                            logger.error(f"保存目标指示器截图失败: {str(e)}")
                            
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"检测目标指示器失败: {str(e)}")
            return False
            
    def is_skill_ready(self, skill_position, region_size=30):
        """检测技能是否就绪
        
        参数:
            skill_position: 技能按钮位置，格式为(x, y)
            region_size: 检测区域大小
            
        返回:
            技能就绪返回True，否则返回False
        """
        if not self.skill_cooldown_detection_enabled:
            return True
            
        x, y = skill_position
        region = (
            x - region_size // 2,
            y - region_size // 2,
            x + region_size // 2,
            y + region_size // 2
        )
        
        try:
            # 截取屏幕
            screenshot = self.capture_screen(region)
            if screenshot is None:
                return False
                
            # 转换为HSV颜色空间
            hsv = cv2.cvtColor(screenshot, cv2.COLOR_RGB2HSV)
            
            # 创建颜色掩码
            mask = cv2.inRange(hsv, self.skill_ready_color['lower'], self.skill_ready_color['upper'])
            
            # 计算掩码中非零像素的比例
            non_zero_ratio = cv2.countNonZero(mask) / (mask.shape[0] * mask.shape[1])
            
            # 如果比例超过阈值，认为技能就绪
            if non_zero_ratio > 0.1:
                logger.debug(f"技能就绪: ({x}, {y})")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"检测技能就绪失败: {str(e)}")
            return False
            
    def save_screenshot(self, filename=None, region=None):
        """保存屏幕截图
        
        参数:
            filename: 文件名，默认为当前时间戳
            region: 截图区域，格式为(left, top, right, bottom)
            
        返回:
            保存成功返回文件路径，否则返回None
        """
        try:
            # 截取屏幕
            screenshot = self.capture_screen(region)
            if screenshot is None:
                return None
                
            # 生成文件名
            if filename is None:
                filename = f"screenshot_{int(time.time())}.png"
                
            # 保存路径
            save_path = os.path.join(self.resource_folder, filename)
            
            # 保存截图
            cv2.imwrite(save_path, cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR))
            
            logger.info(f"截图已保存: {save_path}")
            return save_path
            
        except Exception as e:
            logger.error(f"保存截图失败: {str(e)}")
            return None
            
    def create_template(self, name, region=None):
        """创建模板图像
        
        参数:
            name: 模板名称
            region: 截图区域，格式为(left, top, right, bottom)
            
        返回:
            创建成功返回模板路径，否则返回None
        """
        try:
            # 截取屏幕
            screenshot = self.capture_screen(region)
            if screenshot is None:
                return None
                
            # 生成文件名
            filename = f"{name}.png"
                
            # 保存路径
            save_path = os.path.join(self.template_folder, filename)
            
            # 保存模板
            cv2.imwrite(save_path, cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR))
            
            logger.info(f"模板已创建: {save_path}")
            return filename
            
        except Exception as e:
            logger.error(f"创建模板失败: {str(e)}")
            return None
