#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
战斗逻辑模块 - 负责管理战斗决策和技能使用
"""

import time
import logging
import random
import threading
import queue
import numpy as np
from collections import deque
from screen_detector import ScreenDetector

# 设置日志
logger = logging.getLogger('battle_manager')
logger.setLevel(logging.INFO)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class BattleManager:
    """战斗管理器，负责控制战斗逻辑"""
    
    def __init__(self, memory_reader, input_simulator, monster_selector):
        """初始化战斗管理器"""
        self.memory = memory_reader
        self.input = input_simulator
        self.monster_selector = monster_selector
        
        # 屏幕检测器
        self.screen = ScreenDetector()
        
        # 战斗状态
        self.is_active = False
        self.target_id = 0
        self.target_range = 30.0  # 默认目标范围
        self.hp_threshold = 50  # 默认血量阈值
        self.failed_targets = []  # 记录失败的目标
        self.max_failed_targets = 5  # 最多记录5个失败的目标
        
        # 游戏动作日志回调函数
        self.game_log_callback = None
        
        # 技能系统 - 极速模式设置
        self.skills = {
            "attack1": {"key": "1", "cooldown": 0.05, "type": "attack", "priority": 1, "detected": True},
            "attack2": {"key": "2", "cooldown": 0.05, "type": "attack", "priority": 2, "detected": True},
            "attack3": {"key": "3", "cooldown": 0.05, "type": "attack", "priority": 3, "detected": True},
            "attack4": {"key": "4", "cooldown": 0.05, "type": "attack", "priority": 4, "detected": True},
            "attack5": {"key": "5", "cooldown": 0.05, "type": "attack", "priority": 5, "detected": True},
            "attack6": {"key": "6", "cooldown": 0.05, "type": "attack", "priority": 6, "detected": True},
            "attack7": {"key": "7", "cooldown": 0.05, "type": "attack", "priority": 7, "detected": True},
            "attack8": {"key": "8", "cooldown": 0.05, "type": "attack", "priority": 8, "detected": True},
            "attack9": {"key": "9", "cooldown": 0.05, "type": "attack", "priority": 9, "detected": True}
        }
        
        # 攻击技能序列（按键盘顺序）
        self.attack_sequence = ["1", "2", "3", "4", "5", "6", "7", "8", "9"]
        
        # 技能使用时间记录
        self.skill_last_used = {}
        self.last_skill_time = 0
        self.global_cooldown = 0.05  # 极速全局冷却
        
        # 高级技能管理
        self.skill_queue = queue.Queue()  # 技能队列
        self.skill_thread = None  # 技能线程
        self.skill_thread_active = False  # 技能线程状态
        self.skill_usage_history = deque(maxlen=20)  # 记录最近使用的技能
        self.skill_pattern_weights = np.ones(9)  # 技能模式权重
        self.skill_pattern_learning_rate = 0.01  # 学习率
        
        # 目标检测系统
        self.target_hit_detection = False  # 禁用命中检测
        self.target_hit_confirmed = False  # 目标命中确认
        self.target_hit_time = 0  # 目标命中时间
        self.hit_confirmation_timeout = 2.0  # 命中确认超时时间（秒）
        
        # 屏幕检测设置
        self.screen_detection_enabled = True  # 启用屏幕检测
        self.screen_detection_thread = None  # 屏幕检测线程
        self.screen_detection_active = False  # 屏幕检测线程状态
        self.screen_detection_interval = 0.1  # 屏幕检测间隔（秒）
        
        # 战斗状态检测结果
        self.damage_detected = False  # 是否检测到伤害
        self.target_detected = False  # 是否检测到目标
        self.skill_ready_status = {}  # 技能就绪状态
        
        # 技能按钮位置（需要根据实际游戏界面调整）
        self.skill_positions = {}
        self.initialize_skill_positions()
        
        # 初始化技能状态
        for skill_name in self.skills:
            self.skill_last_used[skill_name] = 0
            self.skill_ready_status[skill_name] = True
        
        logger.info("战斗管理器已初始化")
        
    def initialize_skill_positions(self):
        """初始化技能按钮位置"""
        # 获取屏幕尺寸
        screen_width, screen_height = self.screen.screen_width, self.screen.screen_height
        
        # 技能栏通常在屏幕底部中央
        skill_bar_y = screen_height - 100  # 距离底部100像素
        skill_bar_start_x = screen_width // 2 - 200  # 技能栏起始X坐标
        
        # 计算每个技能按钮的位置
        for i, key in enumerate(self.attack_sequence):
            self.skill_positions[key] = (skill_bar_start_x + i * 50, skill_bar_y)
            
        logger.info("技能按钮位置已初始化")
    
    def set_log_callback(self, callback_function):
        """设置游戏动作日志回调函数
        
        参数:
            callback_function: 回调函数，接收一个字符串参数
        """
        self.game_log_callback = callback_function
    
    def get_status(self):
        """获取战斗管理器的当前状态
        
        返回:
            dict: 包含当前战斗状态的字典，包括:
                - state: 当前状态描述
                - target: 当前目标信息
        """
        status = {
            'state': '战斗中' if self.is_active else '待机',
            'target': None
        }
        
        # 如果有目标，添加目标信息
        if self.target_id > 0:
            try:
                target_info = {
                    'id': self.target_id,
                    'level': 0,  # 默认值，如果需要可以从屏幕检测获取
                    'hp': 0,     # 默认值，如果需要可以从屏幕检测获取
                    'distance': 0, # 默认值
                    'detected': self.target_detected
                }
                status['target'] = target_info
            except Exception as e:
                logger.error(f"获取目标信息时出错: {e}")
        
        return status
        
    def log_game_action(self, action_type, message):
        """记录游戏动作日志
        
        参数:
            action_type: 动作类型，如'战斗'，'移动'，'技能'，'目标'等
            message: 日志消息
        """
        if self.game_log_callback:
            try:
                self.game_log_callback(action_type, message)
            except Exception as e:
                logger.error(f"记录游戏动作日志时出错: {e}")
    
    def update(self):
        """更新战斗管理器状态
        
        此方法在主循环中每100毫秒调用一次，用于更新战斗状态和处理战斗逻辑
        """
        if not self.is_active:
            return
            
        try:
            # 寻找并攻击目标
            self.find_and_attack_target()
            
            # 检查目标命中状态
            if self.target_id > 0 and not self.target_hit_confirmed:
                if self.check_target_hit():
                    self.log_game_action("战斗", f"目标 {self.target_id} 已命中")
                elif time.time() - self.target_hit_time > self.hit_confirmation_timeout:
                    self.log_game_action("战斗", f"目标 {self.target_id} 命中超时，重置目标")
                    self.target_id = 0
                    self.reset_target_hit_detection()
        except Exception as e:
            logger.error(f"更新战斗状态时出错: {e}")
    
    def get_current_target(self):
        """获取当前目标信息
        
        返回:
            dict: 包含目标信息的字典，如果没有目标则返回None
        """
        if self.target_id <= 0:
            return None
            
        # 基于屏幕检测结果创建目标信息
        target_info = {
            'id': self.target_id,
            'detected': self.target_detected,
            'hit_confirmed': self.target_hit_confirmed
        }
        
        return target_info
        
    def start(self):
        """开始自动战斗"""
        if self.is_active:
            logger.info("自动战斗已经在运行中")
            return
            
        self.is_active = True
        logger.info("开始自动战斗")
        self.log_game_action("战斗", "开始自动战斗")
        
        # 启动技能线程
        self.skill_thread_active = True
        self.skill_thread = threading.Thread(target=self.skill_thread_function)
        self.skill_thread.daemon = True
        self.skill_thread.start()
        logger.info("技能线程已启动")
        self.log_game_action("战斗", "技能线程已启动")
        
        # 启动屏幕检测线程
        if self.screen_detection_enabled:
            self.screen_detection_active = True
            self.screen_detection_thread = threading.Thread(target=self.screen_detection_thread_function)
            self.screen_detection_thread.daemon = True
            self.screen_detection_thread.start()
            logger.info("屏幕检测线程已启动")
            self.log_game_action("战斗", "屏幕检测线程已启动")
    
    def stop(self):
        """停止自动战斗"""
        if not self.is_active:
            return
            
        self.is_active = False
        logger.info("停止自动战斗")
        self.log_game_action("战斗", "停止自动战斗")
        
        # 停止技能线程
        if self.skill_thread_active and self.skill_thread and self.skill_thread.is_alive():
            self.skill_thread_active = False
            if self.skill_thread:
                self.skill_thread.join(timeout=1.0)
            logger.info("技能线程已停止")
            self.log_game_action("战斗", "技能线程已停止")
        
        # 停止屏幕检测线程
        if self.screen_detection_active and self.screen_detection_thread and self.screen_detection_thread.is_alive():
            self.screen_detection_active = False
            if self.screen_detection_thread:
                self.screen_detection_thread.join(timeout=1.0)
            logger.info("屏幕检测线程已停止")
            self.log_game_action("战斗", "屏幕检测线程已停止")
    
    def check_target_hit(self, target=None):
        """检查目标是否被命中
        
        参数:
            target: 目标对象（可选）
            
        返回:
            True: 目标已被命中确认
            False: 目标未被命中确认
        """
        # 优先使用屏幕检测结果
        if self.screen_detection_enabled:
            # 检查是否检测到伤害数字
            if self.damage_detected:
                self.target_hit_confirmed = True
                self.target_hit_time = time.time()
                self.log_game_action("战斗", "屏幕检测：发现伤害数字")
                return True
                
            # 检查是否检测到目标
            if not self.target_detected:
                self.log_game_action("战斗", "屏幕检测：未检测到目标")
                return False
        
        # 如果已经确认命中且未超时，直接返回True
        current_time = time.time()
        if self.target_hit_confirmed and (current_time - self.target_hit_time) < self.hit_confirmation_timeout:
            return True
            
        # 如果超过确认超时时间，重置命中状态
        if self.target_hit_confirmed and (current_time - self.target_hit_time) >= self.hit_confirmation_timeout:
            self.target_hit_confirmed = False
            self.log_game_action("目标", "目标命中确认已超时")
            
        return self.target_hit_confirmed
        
    def screen_detection_thread_function(self):
        """屏幕检测线程函数
        
        持续检测屏幕状态，包括伤害数字、目标指示器、技能就绪等
        """
        logger.info("屏幕检测线程开始运行")
        
        while self.screen_detection_active:
            try:
                # 检测伤害数字
                self.damage_detected = self.screen.detect_damage_numbers()
                
                # 检测目标指示器
                self.target_detected = self.screen.detect_target_indicator()
                
                # 检测技能就绪状态
                for key in self.attack_sequence:
                    if key in self.skill_positions:
                        self.skill_ready_status[key] = self.screen.is_skill_ready(self.skill_positions[key])
                
                # 记录检测结果
                if self.damage_detected:
                    logger.debug("检测到伤害数字")
                    
                if self.target_detected:
                    logger.debug("检测到目标指示器")
                
                # 短暂休眠，减少CPU使用
                time.sleep(self.screen_detection_interval)
                
            except Exception as e:
                logger.error(f"屏幕检测错误: {str(e)}")
                time.sleep(0.5)
                
        logger.info("屏幕检测线程已结束")
    
    def skill_thread_function(self):
        """技能线程函数 - 独立线程处理技能使用"""
        logger.info("技能线程开始运行")
        
        last_key_time = time.time()
        key_press_interval = 0.01  # 极速按键间隔，仅10毫秒
        
        # 技能使用计数
        skill_counts = {key: 0 for key in self.attack_sequence}
        
        # 技能释放顺序
        skill_sequence = ["1", "2", "3", "4", "5", "6", "7", "8", "9"]
        current_skill_index = 0
        
        # 禁用复杂的图像识别，直接使用技能
        self.target_hit_detection = False
        
        # 假设已有目标
        self.target_detected = True
        self.target_id = int(time.time())
        
        while self.skill_thread_active:
            try:
                current_time = time.time()
                
                # 极速释放技能，不检查技能就绪状态
                if current_time - last_key_time >= key_press_interval:
                    # 获取当前要使用的技能
                    key = skill_sequence[current_skill_index]
                    
                    # 使用技能
                    self.input.press_key(key)
                    
                    # 记录使用
                    skill_name = f"attack{key}"
                    skill_counts[key] += 1
                    
                    # 每50次记录一次日志
                    if skill_counts[key] % 50 == 0:
                        self.log_game_action("技能", f"使用技能: {key}")
                    
                    # 更新时间
                    last_key_time = current_time
                    
                    # 切换到下一个技能
                    current_skill_index = (current_skill_index + 1) % len(skill_sequence)
                
                # 极短休眠，减少CPU使用但保持高响应
                time.sleep(0.001)
                
            except Exception as e:
                logger.error(f"技能线程错误: {str(e)}")
                time.sleep(0.1)  # 出错时短暂休眠
        
        logger.info("技能线程已结束")
    
    def update_skill_weights(self):
        """更新技能权重
        
        根据技能使用历史和效果反馈，动态调整技能权重
        """
        if len(self.skill_usage_history) < 2:
            return
        
        # 简单的权重调整策略：根据伤害检测结果增加权重
        if self.damage_detected:
            # 如果检测到伤害，增加最近使用的技能的权重
            last_skill_index = self.skill_usage_history[-1]
            self.skill_pattern_weights[last_skill_index] += self.skill_pattern_learning_rate
        
        # 确保权重不会过大或过小
        self.skill_pattern_weights = np.clip(self.skill_pattern_weights, 0.1, 10.0)
    
    def find_and_attack_target(self):
        """寻找并选择攻击目标"""
        try:
            # 使用屏幕检测寻找目标
            if self.screen_detection_enabled:
                # 检查是否已经有目标
                if self.target_detected:
                    return True
                
                # 尝试通过其他方式选择目标
                self.target_detected = True
                self.target_id = int(time.time())  # 使用时间戳作为临时ID
                self.log_game_action("目标", "选择到新目标")
                
                # 重置目标命中检测
                self.reset_target_hit_detection()
                
                # 使用1号技能尝试攻击
                self.input.press_key("1")
                self.log_game_action("战斗", "使用1号技能攻击")
                
                return True
            else:
                self.log_game_action("目标", "未找到目标")
                return False
        
        # 如果屏幕检测未启用或失败，返回失败
        except Exception as e:
            logger.error(f"寻找目标时出错: {e}")
            return False
    
    def reset_target_hit_detection(self):
        """重置目标命中检测"""
        self.target_hit_confirmed = False
        self.target_hit_time = 0
        self.damage_detected = False
        self.log_game_action("目标", "重置目标命中检测")
    
    # ...其他方法...