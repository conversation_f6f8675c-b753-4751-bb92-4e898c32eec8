#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自动战斗和拾取系统 - 基于Aion服务端AI逻辑
整合了服务端的智能战斗决策、目标选择和物品拾取算法
"""

import time
import logging
import random
import threading
import queue
import numpy as np
from collections import deque
from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
import json

# 设置日志
logger = logging.getLogger('auto_combat_system')
logger.setLevel(logging.INFO)

class AttackIntention(Enum):
    """攻击意图枚举 - 基于Aion服务端AI"""
    FINISH_ATTACK = "finish_attack"
    SWITCH_TARGET = "switch_target"
    SKILL_ATTACK = "skill_attack"
    SIMPLE_ATTACK = "simple_attack"
    DEFENSIVE_ACTION = "defensive_action"
    BUFF_SELF = "buff_self"
    HEAL_SELF = "heal_self"
    LOOT_ITEMS = "loot_items"

class BattleState(Enum):
    """战斗状态枚举"""
    IDLE = "idle"
    SEARCHING = "searching"
    ENGAGING = "engaging"
    FIGHTING = "fighting"
    LOOTING = "looting"
    RETREATING = "retreating"
    DEAD = "dead"

class LootPriority(Enum):
    """拾取优先级枚举"""
    LEGENDARY = 5
    EPIC = 4
    RARE = 3
    UNCOMMON = 2
    COMMON = 1

@dataclass
class Target:
    """目标信息"""
    id: int
    name: str = ""
    hp_percent: float = 100.0
    distance: float = 0.0
    threat_level: int = 1
    last_seen: float = 0.0
    is_alive: bool = True
    aggro_level: int = 0
    npc_type: str = "normal"  # normal, elite, boss

@dataclass
class LootItem:
    """掉落物品信息"""
    id: int
    name: str = ""
    item_id: int = 0
    quality: LootPriority = LootPriority.COMMON
    distance: float = 0.0
    drop_time: float = 0.0
    auto_pickup: bool = True

@dataclass
class Skill:
    """技能信息"""
    id: str
    key: str
    cooldown: float
    damage: int
    range: float
    mana_cost: int
    cast_time: float
    priority: int
    skill_type: str  # attack, buff, heal, debuff
    last_used: float = 0.0
    is_ready: bool = True

class AutoCombatSystem:
    """自动战斗和拾取系统 - 基于Aion服务端逻辑"""
    
    def __init__(self, memory_reader, input_simulator, monster_selector):
        """初始化自动战斗系统"""
        self.memory = memory_reader
        self.input = input_simulator
        self.monster_selector = monster_selector
        
        # 核心AI状态
        self.is_active = False
        self.battle_state = BattleState.IDLE
        self.current_target: Optional[Target] = None
        self.target_list: List[Target] = []
        self.aggro_list: Dict[int, int] = {}  # 仇恨列表
        self.loot_list: List[LootItem] = []
        
        # AI思考循环
        self.think_thread = None
        self.think_active = False
        self.think_interval = 0.1  # 100ms思考间隔
        
        # 技能系统 - 基于服务端NPC技能逻辑
        self.skills: Dict[str, Skill] = {}
        self.skill_queue = queue.Queue()
        self.global_cooldown = 0.05
        self.last_skill_time = 0.0
        
        # 目标选择算法
        self.target_selection_algorithm = "most_hated"  # most_hated, nearest, weakest
        self.target_switch_cooldown = 1.0  # 目标切换冷却
        self.last_target_switch = 0.0
        
        # 拾取系统配置
        self.auto_loot_enabled = True
        self.loot_range = 10.0
        self.loot_delay = 0.5  # 拾取延迟
        self.loot_filter = {
            LootPriority.LEGENDARY: True,
            LootPriority.EPIC: True,
            LootPriority.RARE: True,
            LootPriority.UNCOMMON: True,
            LootPriority.COMMON: False
        }
        
        # 战斗配置
        self.attack_range = 15.0
        self.search_range = 25.0
        self.retreat_hp_threshold = 20.0
        self.use_potions = True
        self.auto_buff = True
        
        # 统计信息
        self.stats = {
            "monsters_killed": 0,
            "items_looted": 0,
            "experience_gained": 0,
            "battle_time": 0.0,
            "start_time": 0.0
        }
        
        # 调试模式
        self.debug_mode = False
        
        # 初始化技能配置
        self._init_skills()
        
        # 启用内存读取器的测试模式以便获取模拟数据
        if self.memory and hasattr(self.memory, 'test_mode'):
            self.memory.test_mode = True
            logger.info("已启用内存读取器测试模式")
        
        logger.info("自动战斗系统已初始化")
    
    def _init_skills(self):
        """初始化技能配置 - 基于服务端技能数据"""
        # 基础攻击技能 (1-9)
        attack_skills = [
            Skill("attack1", "1", 0.5, 100, 25.0, 10, 0.5, 1, "attack"),
            Skill("attack2", "2", 1.0, 120, 25.0, 15, 0.6, 2, "attack"),
            Skill("attack3", "3", 1.5, 140, 25.0, 20, 0.7, 3, "attack"),
            Skill("attack4", "4", 2.0, 160, 25.0, 25, 0.8, 4, "attack"),
            Skill("attack5", "5", 2.5, 180, 25.0, 30, 0.9, 5, "attack"),
            Skill("attack6", "6", 3.0, 200, 25.0, 35, 1.0, 6, "attack"),
            Skill("attack7", "7", 3.5, 220, 25.0, 40, 1.1, 7, "attack"),
            Skill("attack8", "8", 4.0, 240, 25.0, 45, 1.2, 8, "attack"),
            Skill("attack9", "9", 4.5, 260, 25.0, 50, 1.3, 9, "attack"),
        ]
        
        # Alt组合技能 (高级技能)
        alt_skills = [
            Skill("alt_attack1", "alt+1", 5.0, 300, 30.0, 80, 1.5, 10, "skill"),
            Skill("alt_attack2", "alt+2", 6.0, 350, 30.0, 90, 1.8, 11, "skill"),
            Skill("alt_attack3", "alt+3", 7.0, 400, 30.0, 100, 2.0, 12, "skill"),
            Skill("alt_attack4", "alt+4", 8.0, 450, 30.0, 110, 2.2, 13, "skill"),
            Skill("alt_attack5", "alt+5", 9.0, 500, 30.0, 120, 2.5, 14, "skill"),
        ]
        
        # 辅助技能
        support_skills = [
            Skill("heal", "h", 8.0, 0, 0.0, 100, 2.0, 100, "heal"),
            Skill("buff1", "f1", 30.0, 0, 0.0, 80, 1.0, 70, "buff"),
            Skill("buff2", "f2", 45.0, 0, 0.0, 100, 1.5, 75, "buff"),
        ]
        
        # 合并技能列表
        all_skills = attack_skills + alt_skills + support_skills
        
        for skill in all_skills:
            self.skills[skill.id] = skill
        
        logger.info(f"已初始化 {len(self.skills)} 个技能")
    
    def start(self):
        """启动自动战斗系统"""
        if self.is_active:
            return
        
        self.is_active = True
        self.think_active = True
        self.stats["start_time"] = time.time()
        
        # 启动AI思考线程
        self.think_thread = threading.Thread(target=self._think_loop, daemon=True)
        self.think_thread.start()
        
        logger.info("自动战斗系统已启动")
    
    def stop(self):
        """停止自动战斗系统"""
        if not self.is_active:
            return
        
        self.is_active = False
        self.think_active = False
        self.battle_state = BattleState.IDLE
        
        if self.think_thread and self.think_thread.is_alive():
            self.think_thread.join(timeout=1.0)
        
        logger.info("自动战斗系统已停止")
    
    def _think_loop(self):
        """AI思考主循环 - 基于服务端AI思考逻辑"""
        while self.think_active:
            try:
                self._think()
                time.sleep(self.think_interval)
            except Exception as e:
                logger.error(f"AI思考循环出错: {e}")
                time.sleep(1.0)
    
    def _think(self):
        """AI思考逻辑 - 基于服务端ThinkEventHandler"""
        if not self.is_active:
            return
        
        # 更新玩家状态
        player_info = self._get_player_info()
        if not player_info:
            return
        
        # 检查死亡状态
        if player_info.get("hp_percent", 0) <= 0:
            self.battle_state = BattleState.DEAD
            return
        
        # 根据当前状态执行相应逻辑
        if self.battle_state == BattleState.IDLE:
            self._handle_idle_state()
        elif self.battle_state == BattleState.SEARCHING:
            self._handle_searching_state()
        elif self.battle_state == BattleState.ENGAGING:
            self._handle_engaging_state()
        elif self.battle_state == BattleState.FIGHTING:
            self._handle_fighting_state()
        elif self.battle_state == BattleState.LOOTING:
            self._handle_looting_state()
        elif self.battle_state == BattleState.RETREATING:
            self._handle_retreating_state()
    
    def _handle_idle_state(self):
        """处理空闲状态"""
        # 检查是否需要使用药品
        if self._should_use_potion():
            self._use_potion()
            return
        
        # 检查是否需要buff
        if self._should_buff():
            self._cast_buff()
            return
        
        # 搜索附近的目标
        self._update_target_list()
        
        if self.target_list:
            self.battle_state = BattleState.SEARCHING
        else:
            # 检查是否有掉落物品需要拾取
            self._update_loot_list()
            if self.loot_list and self.auto_loot_enabled:
                self.battle_state = BattleState.LOOTING
    
    def _handle_searching_state(self):
        """处理搜索状态"""
        # 选择最佳目标
        target = self._choose_best_target()
        
        if target:
            self.current_target = target
            self.battle_state = BattleState.ENGAGING
            logger.info(f"选择目标: {target.name} (距离: {target.distance:.1f}m)")
        else:
            self.battle_state = BattleState.IDLE
    
    def _handle_engaging_state(self):
        """处理接敌状态"""
        if not self.current_target or not self.current_target.is_alive:
            self.current_target = None
            self.battle_state = BattleState.IDLE
            return
        
        # 更新目标信息
        self._update_target_info(self.current_target)
        
        # 检查攻击距离
        if self.current_target.distance <= self.attack_range:
            self.battle_state = BattleState.FIGHTING
            logger.info(f"开始攻击目标: {self.current_target.name}")
        else:
            # 移动到目标附近
            self._move_to_target(self.current_target)
    
    def _handle_fighting_state(self):
        """处理战斗状态 - 基于服务端攻击逻辑"""
        if not self.current_target or not self.current_target.is_alive:
            # 目标死亡，记录击杀
            if self.current_target:
                self.stats["monsters_killed"] += 1
                logger.info(f"击杀目标: {self.current_target.name}")
            
            self.current_target = None
            self.battle_state = BattleState.IDLE
            return
        
        # 更新目标信息
        self._update_target_info(self.current_target)
        
        # 检查是否需要撤退
        player_info = self._get_player_info()
        if player_info and player_info.get("hp_percent", 100) < self.retreat_hp_threshold:
            self.battle_state = BattleState.RETREATING
            return
        
        # 选择攻击意图
        intention = self._choose_attack_intention()
        
        # 执行攻击意图
        self._execute_attack_intention(intention)
    
    def _handle_looting_state(self):
        """处理拾取状态"""
        if not self.loot_list:
            self.battle_state = BattleState.IDLE
            return
        
        # 选择最近的高价值物品
        best_loot = self._choose_best_loot()
        
        if best_loot:
            self._loot_item(best_loot)
            self.loot_list.remove(best_loot)
            self.stats["items_looted"] += 1
            logger.info(f"拾取物品: {best_loot.name}")
        else:
            self.battle_state = BattleState.IDLE
    
    def _handle_retreating_state(self):
        """处理撤退状态"""
        # 使用治疗技能或药品
        if self._should_heal():
            self._cast_heal()
        
        # 检查血量是否恢复
        player_info = self._get_player_info()
        if player_info and player_info.get("hp_percent", 0) > 50:
            self.battle_state = BattleState.IDLE
    
    def _choose_attack_intention(self) -> AttackIntention:
        """选择攻击意图 - 基于服务端chooseAttackIntention"""
        if not self.current_target:
            return AttackIntention.FINISH_ATTACK
        
        # 检查目标是否死亡
        if not self.current_target.is_alive:
            return AttackIntention.FINISH_ATTACK
        
        # 检查是否需要切换目标
        most_hated = self._get_most_hated_target()
        if most_hated and most_hated.id != self.current_target.id:
            if time.time() - self.last_target_switch > self.target_switch_cooldown:
                return AttackIntention.SWITCH_TARGET
        
        # 检查是否需要治疗
        player_info = self._get_player_info()
        if player_info and player_info.get("hp_percent", 100) < 30:
            return AttackIntention.HEAL_SELF
        
        # 检查是否需要buff
        if self._should_buff():
            return AttackIntention.BUFF_SELF
        
        # 选择技能攻击
        skill = self._choose_next_skill()
        if skill:
            return AttackIntention.SKILL_ATTACK
        
        # 默认普通攻击
        return AttackIntention.SIMPLE_ATTACK
    
    def _execute_attack_intention(self, intention: AttackIntention):
        """执行攻击意图"""
        current_time = time.time()
        
        # 检查全局冷却
        if current_time - self.last_skill_time < self.global_cooldown:
            return
        
        if intention == AttackIntention.FINISH_ATTACK:
            self.current_target = None
            self.battle_state = BattleState.IDLE
        
        elif intention == AttackIntention.SWITCH_TARGET:
            new_target = self._get_most_hated_target()
            if new_target:
                self.current_target = new_target
                self.last_target_switch = current_time
                logger.info(f"切换目标: {new_target.name}")
        
        elif intention == AttackIntention.SKILL_ATTACK:
            skill = self._choose_next_skill()
            if skill:
                self._cast_skill(skill)
        
        elif intention == AttackIntention.SIMPLE_ATTACK:
            self._simple_attack()
        
        elif intention == AttackIntention.HEAL_SELF:
            self._cast_heal()
        
        elif intention == AttackIntention.BUFF_SELF:
            self._cast_buff()
    
    def _choose_next_skill(self) -> Optional[Skill]:
        """选择下一个技能 - 基于服务端SkillAttackManager"""
        current_time = time.time()
        available_skills = []
        
        # 筛选可用的攻击技能
        for skill in self.skills.values():
            if (skill.skill_type == "attack" and 
                current_time - skill.last_used >= skill.cooldown and
                skill.is_ready):
                available_skills.append(skill)
        
        if not available_skills:
            return None
        
        # 按优先级排序
        available_skills.sort(key=lambda s: s.priority, reverse=True)
        
        # 选择最高优先级的技能
        return available_skills[0]
    
    def _choose_best_target(self) -> Optional[Target]:
        """选择最佳目标 - 基于服务端目标选择算法"""
        if not self.target_list:
            return None
        
        if self.target_selection_algorithm == "most_hated":
            return self._get_most_hated_target()
        elif self.target_selection_algorithm == "nearest":
            return min(self.target_list, key=lambda t: t.distance)
        elif self.target_selection_algorithm == "weakest":
            return min(self.target_list, key=lambda t: t.hp_percent)
        elif self.target_selection_algorithm == "smart_score":
            # 使用智能评分系统
            return max(self.target_list, key=self._calculate_target_score)
        
        return self.target_list[0]
    
    def _get_most_hated_target(self) -> Optional[Target]:
        """获取仇恨值最高的目标"""
        if not self.target_list:
            return None
        
        # 如果有仇恨列表，选择仇恨值最高的目标
        if self.aggro_list:
            most_hated_id = max(self.aggro_list.keys(), key=lambda k: self.aggro_list[k])
            
            for target in self.target_list:
                if target.id == most_hated_id and target.is_alive:
                    return target
        
        # 如果没有仇恨列表或找不到对应目标，选择第一个可用目标
        for target in self.target_list:
            if target.is_alive:
                return target
        
        return None
    
    def _calculate_target_score(self, target: Target) -> float:
        """计算目标评分 - 多因素评估算法"""
        score = 0.0
        
        # 仇恨值权重 (最重要)
        aggro = self.aggro_list.get(target.id, 0)
        score += aggro * 10.0
        
        # 距离权重（越近越好）
        if target.distance > 0:
            score += (50.0 - target.distance) * 2.0
        
        # 血量权重（优先攻击残血）
        if target.hp_percent < 30:
            score += 20.0
        elif target.hp_percent < 60:
            score += 10.0
        
        # 威胁等级权重
        score += target.threat_level * 5.0
        
        # NPC类型权重（精英和BOSS优先级更高）
        if target.npc_type == "elite":
            score += 15.0
        elif target.npc_type == "boss":
            score += 30.0
        
        # 最近攻击时间权重（优先攻击最近攻击过的目标）
        time_since_last_seen = time.time() - target.last_seen
        if time_since_last_seen < 5.0:  # 5秒内见过的目标
            score += 10.0
        
        return score
    
    def _choose_best_loot(self) -> Optional[LootItem]:
        """选择最佳拾取物品"""
        if not self.loot_list:
            return None
        
        # 按优先级和距离排序
        filtered_loot = [
            item for item in self.loot_list
            if self.loot_filter.get(item.quality, False) and item.distance <= self.loot_range
        ]
        
        if not filtered_loot:
            return None
        
        # 优先级高的优先，距离近的优先
        return max(filtered_loot, key=lambda item: (item.quality.value, -item.distance))
    
    def _update_target_list(self):
        """更新目标列表"""
        try:
            self.target_list = []
            
            # 获取附近怪物
            monsters = self._scan_nearby_monsters()
            
            for monster_data in monsters:
                # 计算血量百分比
                hp_percent = 100.0
                if 'hp' in monster_data and 'hp_max' in monster_data and monster_data['hp_max'] > 0:
                    hp_percent = (monster_data['hp'] / monster_data['hp_max']) * 100.0
                elif 'hp' in monster_data:
                    # 如果hp已经是百分比格式
                    hp_percent = monster_data['hp'] * 100.0 if monster_data['hp'] <= 1.0 else monster_data['hp']
                
                # 判断怪物是否存活
                is_alive = hp_percent > 0
                
                # 计算威胁等级
                threat_level = monster_data.get('level', 1) // 10 + 1
                if monster_data.get('type', 1) > 1:  # 精英或BOSS
                    threat_level += monster_data.get('type', 1)
                
                target = Target(
                    id=monster_data.get("id", 0),
                    name=monster_data.get("name", f"怪物_{monster_data.get('id', 0)}"),
                    hp_percent=hp_percent,
                    distance=monster_data.get("distance", 0.0),
                    threat_level=threat_level,
                    last_seen=time.time(),
                    is_alive=is_alive,
                    aggro_level=0,
                    npc_type="elite" if monster_data.get('type', 1) > 1 else "normal"
                )
                
                if target.distance <= self.search_range and target.is_alive:
                    self.target_list.append(target)
                    
                    # 更新仇恨列表
                    if target.id not in self.aggro_list:
                        self.aggro_list[target.id] = 0
            
            logger.debug(f"更新目标列表: 找到 {len(self.target_list)} 个有效目标")
        
        except Exception as e:
            logger.error(f"更新目标列表失败: {e}")
    
    def _update_loot_list(self):
        """更新掉落物品列表"""
        try:
            self.loot_list = []
            
            # 模拟获取附近掉落物品
            loot_items = self._scan_nearby_loot()
            
            for loot_data in loot_items:
                loot_item = LootItem(
                    id=loot_data.get("id", 0),
                    name=loot_data.get("name", "Unknown Item"),
                    item_id=loot_data.get("item_id", 0),
                    quality=LootPriority(loot_data.get("quality", 1)),
                    distance=loot_data.get("distance", 0.0),
                    drop_time=loot_data.get("drop_time", time.time())
                )
                
                if loot_item.distance <= self.loot_range:
                    self.loot_list.append(loot_item)
        
        except Exception as e:
            logger.error(f"更新掉落物品列表失败: {e}")
    
    def _scan_nearby_monsters(self) -> List[Dict]:
        """扫描附近怪物 - 调用内存读取器获取真实数据"""
        try:
            if self.memory and hasattr(self.memory, 'get_nearby_monsters'):
                monsters = self.memory.get_nearby_monsters(self.search_range)
                logger.info(f"扫描到 {len(monsters)} 个怪物")
                return monsters
            else:
                logger.warning("内存读取器不可用，返回空列表")
                return []
        except Exception as e:
            logger.error(f"扫描附近怪物失败: {e}")
            return []
    
    def _scan_nearby_loot(self) -> List[Dict]:
        """扫描附近掉落物品 - 需要实现内存读取"""
        # 这里应该调用内存读取器来获取真实的掉落物品数据
        # 暂时返回模拟数据
        return []
    
    def _get_player_info(self) -> Optional[Dict]:
        """获取玩家信息"""
        try:
            if self.memory and hasattr(self.memory, 'get_character_status'):
                status = self.memory.get_character_status()
                if status:
                    # 计算血量和魔法值百分比
                    hp_percent = 100.0
                    mp_percent = 100.0
                    
                    if status.get('hp_max', 0) > 0:
                        hp_percent = (status.get('hp', 0) / status['hp_max']) * 100.0
                    
                    if status.get('mp_max', 0) > 0:
                        mp_percent = (status.get('mp', 0) / status['mp_max']) * 100.0
                    
                    return {
                        "hp_percent": hp_percent,
                        "mp_percent": mp_percent,
                        "level": status.get('level', 1),
                        "combat_state": status.get('combat_state', False),
                        "position": status.get('position', [0, 0, 0])
                    }
            
            # 默认返回满血满蓝状态
            return {"hp_percent": 100, "mp_percent": 100, "level": 50}
        except Exception as e:
            logger.error(f"获取玩家信息失败: {e}")
            return {"hp_percent": 100, "mp_percent": 100, "level": 50}
    
    def _update_target_info(self, target: Target):
        """更新目标信息"""
        try:
            # 这里应该调用内存读取来更新目标的实时信息
            pass
        except Exception as e:
            logger.error(f"更新目标信息失败: {e}")
    
    def _cast_skill(self, skill: Skill):
        """释放技能"""
        try:
            current_time = time.time()
            
            # 检查技能冷却
            if current_time - skill.last_used < skill.cooldown:
                logger.debug(f"技能 {skill.id} 仍在冷却中，剩余时间: {skill.cooldown - (current_time - skill.last_used):.1f}秒")
                return False
            
            # 检查全局冷却
            if current_time - self.last_skill_time < self.global_cooldown:
                logger.debug(f"全局冷却中，剩余时间: {self.global_cooldown - (current_time - self.last_skill_time):.2f}秒")
                return False
            
            # 发送按键
            if self.input and hasattr(self.input, 'press_key'):
                success = False
                
                # 处理Alt组合键
                if skill.key.startswith("alt+"):
                    key_part = skill.key.split("+")[1]
                    if hasattr(self.input, 'press_key_combo'):
                        success = self.input.press_key_combo(['alt', key_part])
                        logger.debug(f"发送组合键: alt+{key_part}")
                    else:
                        # 如果不支持组合键，先按Alt再按其他键
                        logger.debug(f"不支持组合键，分别发送: alt, {key_part}")
                        self.input.press_key('alt')
                        time.sleep(0.05)
                        success = self.input.press_key(key_part)
                else:
                    success = self.input.press_key(skill.key)
                    logger.debug(f"发送单键: {skill.key}")
                
                if success:
                    skill.last_used = current_time
                    self.last_skill_time = current_time
                    logger.info(f"释放技能: {skill.id} (按键: {skill.key})")
                    
                    # 在调试模式下，额外等待一下确保按键被处理
                    if self.debug_mode:
                        time.sleep(0.1)
                    
                    return True
                else:
                    logger.warning(f"按键发送失败: {skill.key}")
                    return False
            else:
                logger.error("输入模拟器不可用或缺少press_key方法")
                return False
            
        except Exception as e:
            logger.error(f"释放技能失败: {e}")
            return False
    
    def _simple_attack(self):
        """普通攻击"""
        try:
            if self.input:
                self.input.press_key("space")  # 假设空格键是普通攻击
                self.last_skill_time = time.time()
        except Exception as e:
            logger.error(f"普通攻击失败: {e}")
    
    def _cast_heal(self):
        """释放治疗技能"""
        heal_skill = self.skills.get("heal")
        if heal_skill:
            self._cast_skill(heal_skill)
    
    def _cast_buff(self):
        """释放buff技能"""
        for skill_id in ["buff1", "buff2"]:
            skill = self.skills.get(skill_id)
            if skill and time.time() - skill.last_used >= skill.cooldown:
                self._cast_skill(skill)
                break
    
    def _use_potion(self):
        """使用药品"""
        try:
            if self.input:
                self.input.press_key("f5")  # 假设F5是血药
                logger.info("使用治疗药品")
        except Exception as e:
            logger.error(f"使用药品失败: {e}")
    
    def _loot_item(self, loot_item: LootItem):
        """拾取物品"""
        try:
            if self.input:
                # 移动到物品位置
                # self._move_to_position(loot_item.position)
                
                # 拾取物品
                self.input.press_key("f")  # 假设F键是拾取
                time.sleep(self.loot_delay)
                
                logger.info(f"拾取物品: {loot_item.name}")
        except Exception as e:
            logger.error(f"拾取物品失败: {e}")
    
    def _move_to_target(self, target: Target):
        """移动到目标位置"""
        try:
            # 这里应该实现移动逻辑
            pass
        except Exception as e:
            logger.error(f"移动到目标失败: {e}")
    
    def _should_use_potion(self) -> bool:
        """检查是否需要使用药品"""
        if not self.use_potions:
            return False
        
        player_info = self._get_player_info()
        if player_info:
            return player_info.get("hp_percent", 100) < 50
        
        return False
    
    def _should_heal(self) -> bool:
        """检查是否需要治疗"""
        player_info = self._get_player_info()
        if player_info:
            return player_info.get("hp_percent", 100) < 40
        
        return False
    
    def _should_buff(self) -> bool:
        """检查是否需要buff"""
        if not self.auto_buff:
            return False
        
        # 检查buff技能冷却
        current_time = time.time()
        for skill_id in ["buff1", "buff2"]:
            skill = self.skills.get(skill_id)
            if skill and current_time - skill.last_used >= skill.cooldown:
                return True
        
        return False
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        current_time = time.time()
        if self.stats["start_time"] > 0:
            self.stats["battle_time"] = current_time - self.stats["start_time"]
        
        return self.stats.copy()
    
    def set_config(self, config: Dict):
        """设置配置"""
        if "auto_loot_enabled" in config:
            self.auto_loot_enabled = config["auto_loot_enabled"]
        
        if "loot_range" in config:
            self.loot_range = config["loot_range"]
        
        if "attack_range" in config:
            self.attack_range = config["attack_range"]
        
        if "search_range" in config:
            self.search_range = config["search_range"]
        
        if "use_potions" in config:
            self.use_potions = config["use_potions"]
        
        if "auto_buff" in config:
            self.auto_buff = config["auto_buff"]
        
        if "loot_filter" in config:
            self.loot_filter.update(config["loot_filter"])
        
        logger.info("配置已更新")
    
    def enable_debug_mode(self, enabled: bool = True):
        """启用或禁用调试模式"""
        self.debug_mode = enabled
        if enabled:
            logger.setLevel(logging.DEBUG)
            logger.info("调试模式已启用 - 将使用模拟数据进行测试")
            
            # 在调试模式下启用内存读取器的测试模式
            if hasattr(self.memory, 'enable_test_mode'):
                self.memory.enable_test_mode(True)
                logger.info("已启用内存读取器测试模式")
        else:
            logger.setLevel(logging.INFO)
            logger.info("调试模式已禁用")
            
            # 禁用内存读取器的测试模式
            if hasattr(self.memory, 'enable_test_mode'):
                self.memory.enable_test_mode(False)