#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
真正能工作的输入模拟器 - 专门为永恒之塔设计
"""

import time
import logging
import ctypes
from ctypes import wintypes
import win32api
import win32con
import win32gui
import win32process
import psutil
from threading import Lock

logger = logging.getLogger('working_input_simulator')

class WorkingInputSimulator:
    """真正能工作的输入模拟器"""
    
    def __init__(self):
        self.input_lock = Lock()
        self.game_window = None
        self.game_process_id = None
        self.is_admin = self._check_admin_rights()
        
        # 虚拟键码映射
        self.vk_codes = {
            '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34, '5': 0x35,
            '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39, '0': 0x30,
            'f1': 0x70, 'f2': 0x71, 'f3': 0x72, 'f4': 0x73, 'f5': 0x74,
            'f6': 0x75, 'f7': 0x76, 'f8': 0x77, 'f9': 0x78, 'f10': 0x79,
            'space': 0x20, 'tab': 0x09, 'enter': 0x0D, 'esc': 0x1B,
            'alt': 0x12, 'ctrl': 0x11, 'shift': 0x10,
            'a': 0x41, 'b': 0x42, 'c': 0x43, 'd': 0x44, 'e': 0x45,
            'f': 0x46, 'g': 0x47, 'h': 0x48, 'i': 0x49, 'j': 0x4A,
            'k': 0x4B, 'l': 0x4C, 'm': 0x4D, 'n': 0x4E, 'o': 0x4F,
            'p': 0x50, 'q': 0x51, 'r': 0x52, 's': 0x53, 't': 0x54,
            'u': 0x55, 'v': 0x56, 'w': 0x57, 'x': 0x58, 'y': 0x59, 'z': 0x5A
        }
        
        # 按键持续时间配置 - 优化速度
        self.key_press_duration = 0.02  # 减少到20ms，提高速度
        self.combo_key_duration = 0.03  # 组合键稍长一点
        self.key_interval = 0.01        # 按键间隔
        
        logger.info(f"输入模拟器初始化 - 管理员权限: {self.is_admin}")
        
        # 自动查找游戏窗口
        self.find_aion_window()
    
    def _check_admin_rights(self):
        """检查是否有管理员权限"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def find_aion_window(self):
        """查找永恒之塔游戏窗口"""
        try:
            def enum_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    title = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    
                    # 检查窗口标题和类名
                    aion_keywords = ['aion', '永恒之塔', 'AION', 'Aion']
                    if any(keyword in title.lower() for keyword in aion_keywords) or 'aion' in class_name.lower():
                        try:
                            _, pid = win32gui.GetWindowThreadProcessId(hwnd)
                            process = psutil.Process(pid)
                            exe_name = process.name().lower()
                            
                            # 检查进程名
                            if 'aion' in exe_name or 'bin32' in exe_name or 'bin64' in exe_name:
                                windows.append((hwnd, title, pid, exe_name))
                        except:
                            pass
                return True
            
            windows = []
            win32gui.EnumWindows(enum_callback, windows)
            
            if windows:
                # 选择第一个匹配的窗口
                self.game_window, title, self.game_process_id, exe_name = windows[0]
                logger.info(f"找到游戏窗口: {title} (PID: {self.game_process_id}, EXE: {exe_name})")
                
                # 尝试将窗口置于前台
                self.focus_game_window()
                return True
            else:
                logger.warning("未找到永恒之塔游戏窗口")
                return False
                
        except Exception as e:
            logger.error(f"查找游戏窗口失败: {e}")
            return False
    
    def focus_game_window(self):
        """将游戏窗口置于前台"""
        if not self.game_window:
            return False
        
        try:
            # 检查窗口是否最小化
            if win32gui.IsIconic(self.game_window):
                win32gui.ShowWindow(self.game_window, win32con.SW_RESTORE)
            
            # 将窗口置于前台
            win32gui.SetForegroundWindow(self.game_window)
            win32gui.BringWindowToTop(self.game_window)
            
            # 给窗口焦点
            win32gui.SetActiveWindow(self.game_window)
            
            logger.info("游戏窗口已置于前台")
            return True
        except Exception as e:
            logger.error(f"无法将游戏窗口置于前台: {e}")
            return False
    
    def press_key(self, key):
        """发送按键到游戏"""
        if not self._validate_key(key):
            return False
        
        with self.input_lock:
            success = False
            
            # 确保游戏窗口有焦点
            if self.game_window:
                self.focus_game_window()
                time.sleep(0.01)  # 短暂等待窗口获得焦点
            
            # 方法1: 直接发送到游戏窗口 (最可靠)
            if self.game_window:
                success = self._send_key_to_window(key)
                if success:
                    logger.info(f"✓ 窗口方法发送成功: {key}")
                    return True
            
            # 方法2: 全局按键发送 (备选)
            success = self._send_global_key(key)
            if success:
                logger.info(f"✓ 全局方法发送成功: {key}")
                return True
            
            # 方法3: SendInput API (最后尝试)
            success = self._send_input_key(key)
            if success:
                logger.info(f"✓ SendInput方法发送成功: {key}")
                return True
            
            logger.error(f"✗ 所有方法都失败: {key}")
            return False
    
    def _validate_key(self, key):
        """验证按键是否支持"""
        if key.lower() not in self.vk_codes:
            logger.error(f"不支持的按键: {key}")
            return False
        return True
    
    def _send_key_to_window(self, key):
        """发送按键到特定窗口"""
        try:
            vk_code = self.vk_codes[key.lower()]
            
            # 使用PostMessage发送按键消息
            scan_code = win32api.MapVirtualKey(vk_code, 0)
            
            # WM_KEYDOWN
            lParam_down = (scan_code << 16) | 1
            result1 = win32gui.PostMessage(self.game_window, win32con.WM_KEYDOWN, vk_code, lParam_down)
            
            time.sleep(self.key_press_duration)  # 使用配置的按键持续时间
            
            # WM_KEYUP
            lParam_up = (scan_code << 16) | 0xC0000001
            result2 = win32gui.PostMessage(self.game_window, win32con.WM_KEYUP, vk_code, lParam_up)
            
            return result1 != 0 and result2 != 0
            
        except Exception as e:
            logger.debug(f"窗口按键发送失败: {e}")
            return False
    
    def _send_global_key(self, key):
        """发送全局按键"""
        try:
            vk_code = self.vk_codes[key.lower()]
            
            # 使用keybd_event发送全局按键
            win32api.keybd_event(vk_code, 0, 0, 0)  # 按下
            time.sleep(0.05)
            win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放
            
            return True
            
        except Exception as e:
            logger.debug(f"全局按键发送失败: {e}")
            return False
    
    def _send_input_key(self, key):
        """使用SendInput API发送按键"""
        try:
            vk_code = self.vk_codes[key.lower()]
            
            # 定义INPUT结构
            PUL = ctypes.POINTER(ctypes.c_ulong)
            
            class KeyBdInput(ctypes.Structure):
                _fields_ = [("wVk", ctypes.c_ushort),
                           ("wScan", ctypes.c_ushort),
                           ("dwFlags", ctypes.c_ulong),
                           ("time", ctypes.c_ulong),
                           ("dwExtraInfo", PUL)]
            
            class HardwareInput(ctypes.Structure):
                _fields_ = [("uMsg", ctypes.c_ulong),
                           ("wParamL", ctypes.c_short),
                           ("wParamH", ctypes.c_ushort)]
            
            class MouseInput(ctypes.Structure):
                _fields_ = [("dx", ctypes.c_long),
                           ("dy", ctypes.c_long),
                           ("mouseData", ctypes.c_ulong),
                           ("dwFlags", ctypes.c_ulong),
                           ("time", ctypes.c_ulong),
                           ("dwExtraInfo", PUL)]
            
            class Input_I(ctypes.Union):
                _fields_ = [("ki", KeyBdInput),
                           ("mi", MouseInput),
                           ("hi", HardwareInput)]
            
            class Input(ctypes.Structure):
                _fields_ = [("type", ctypes.c_ulong),
                           ("ii", Input_I)]
            
            # 按下按键
            extra = ctypes.c_ulong(0)
            ii_ = Input_I()
            ii_.ki = KeyBdInput(vk_code, 0, 0, 0, ctypes.pointer(extra))
            x = Input(ctypes.c_ulong(1), ii_)
            ctypes.windll.user32.SendInput(1, ctypes.pointer(x), ctypes.sizeof(x))
            
            time.sleep(0.05)
            
            # 释放按键
            ii_.ki = KeyBdInput(vk_code, 0, 2, 0, ctypes.pointer(extra))
            x = Input(ctypes.c_ulong(1), ii_)
            ctypes.windll.user32.SendInput(1, ctypes.pointer(x), ctypes.sizeof(x))
            
            return True
            
        except Exception as e:
            logger.debug(f"SendInput按键发送失败: {e}")
            return False
    
    def press_key_combo(self, keys):
        """发送组合键 - 优化速度"""
        if not all(self._validate_key(key) for key in keys):
            return False
        
        with self.input_lock:
            try:
                # 确保游戏窗口有焦点
                if self.game_window:
                    self.focus_game_window()
                    time.sleep(self.key_interval)
                
                vk_codes = [self.vk_codes[key.lower()] for key in keys]
                
                # 快速按下所有键
                for vk_code in vk_codes:
                    win32api.keybd_event(vk_code, 0, 0, 0)
                    time.sleep(0.005)  # 极短间隔
                
                time.sleep(self.combo_key_duration)
                
                # 快速释放所有键（按相反顺序）
                for vk_code in reversed(vk_codes):
                    win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)
                    time.sleep(0.005)  # 极短间隔
                
                logger.info(f"✓ 组合键发送成功: {'+'.join(keys)}")
                return True
                
            except Exception as e:
                logger.error(f"组合键发送失败: {e}")
                
                # 确保所有键都被释放
                for key in keys:
                    try:
                        vk_code = self.vk_codes[key.lower()]
                        win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)
                    except:
                        pass
                
                return False
    
    def press_alt_key(self, key):
        """发送Alt+按键组合 - 专门优化的方法"""
        return self.press_key_combo(['alt', key])
    
    def send_skill_sequence(self, skills, interval=0.1):
        """快速发送技能序列"""
        success_count = 0
        
        with self.input_lock:
            # 确保游戏窗口有焦点
            if self.game_window:
                self.focus_game_window()
                time.sleep(self.key_interval)
            
            for skill in skills:
                try:
                    if skill.startswith('alt+') or skill.startswith('a+'):
                        # Alt组合键
                        key_part = skill.split('+')[1]
                        if self.press_alt_key(key_part):
                            success_count += 1
                            logger.debug(f"✓ Alt+{key_part}")
                    else:
                        # 普通按键
                        if self.press_key(skill):
                            success_count += 1
                            logger.debug(f"✓ {skill}")
                    
                    time.sleep(interval)  # 技能间隔
                    
                except Exception as e:
                    logger.error(f"技能 {skill} 发送失败: {e}")
        
        logger.info(f"技能序列完成: {success_count}/{len(skills)} 成功")
        return success_count
    
    def click(self, x=None, y=None, button='left'):
        """模拟鼠标点击"""
        try:
            with self.input_lock:
                # 如果坐标未指定，使用当前位置
                if x is None or y is None:
                    current_pos = win32gui.GetCursorPos()
                    x = x if x is not None else current_pos[0]
                    y = y if y is not None else current_pos[1]
                
                # 移动鼠标
                win32api.SetCursorPos((int(x), int(y)))
                time.sleep(0.01)
                
                # 确定按键事件
                if button == 'left':
                    down_event = win32con.MOUSEEVENTF_LEFTDOWN
                    up_event = win32con.MOUSEEVENTF_LEFTUP
                elif button == 'right':
                    down_event = win32con.MOUSEEVENTF_RIGHTDOWN
                    up_event = win32con.MOUSEEVENTF_RIGHTUP
                else:
                    raise ValueError(f"不支持的按钮类型: {button}")
                
                # 发送鼠标事件
                win32api.mouse_event(down_event, 0, 0, 0, 0)
                time.sleep(0.05)
                win32api.mouse_event(up_event, 0, 0, 0, 0)
                
                logger.info(f"✓ 鼠标点击: {button} 按钮，位置: ({int(x)}, {int(y)})")
                return True
                
        except Exception as e:
            logger.error(f"鼠标点击失败: {e}")
            return False
    
    def test_input(self):
        """测试输入功能"""
        logger.info("开始测试输入功能...")
        
        if not self.is_admin:
            logger.warning("⚠ 程序未以管理员权限运行，可能影响按键发送效果")
        
        if not self.game_window:
            logger.warning("⚠ 未找到游戏窗口，将使用全局按键发送")
        else:
            logger.info(f"✓ 找到游戏窗口，将发送按键到窗口")
        
        # 测试基本按键
        test_keys = ['1', '2', '3']
        success_count = 0
        
        logger.info("开始发送测试按键...")
        for key in test_keys:
            if self.press_key(key):
                success_count += 1
            time.sleep(1)  # 给用户时间观察效果
        
        logger.info(f"测试完成: {success_count}/{len(test_keys)} 按键发送成功")
        
        if success_count == 0:
            logger.error("所有按键发送都失败了！")
            logger.info("建议:")
            logger.info("1. 以管理员权限运行程序")
            logger.info("2. 确保游戏窗口在前台")
            logger.info("3. 检查游戏是否有反外挂保护")
            logger.info("4. 尝试在游戏中手动按键测试")
        
        return success_count > 0
    
    def get_status(self):
        """获取输入模拟器状态"""
        return {
            "admin_rights": self.is_admin,
            "game_window_found": self.game_window is not None,
            "game_process_id": self.game_process_id,
            "window_handle": self.game_window
        }