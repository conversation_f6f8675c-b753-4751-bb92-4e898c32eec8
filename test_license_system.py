"""
安全许可证系统测试脚本
用于测试安全许可证系统的各项功能
"""

import os
import sys
import time
import logging
import argparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_license.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('test_license')

# 导入安全许可证系统
try:
    from secure_license_system import SecureLicenseSystem
except ImportError as e:
    logger.error(f"无法导入安全许可证系统: {str(e)}")
    sys.exit(1)


def test_activation(license_system, activation_key=None):
    """测试激活功能"""
    print("\n=== 测试激活功能 ===")
    
    if not activation_key:
        activation_key = input("请输入激活码: ")
    
    print(f"正在使用激活码 '{activation_key}' 激活...")
    is_success, message = license_system.activate(activation_key)
    
    if is_success:
        print(f"激活成功: {message}")
    else:
        print(f"激活失败: {message}")
    
    return is_success


def test_check_activation(license_system):
    """测试激活状态检查功能"""
    print("\n=== 测试激活状态检查 ===")
    
    is_activated, message, expiry_date = license_system.check_activation()
    
    print(f"激活状态: {'已激活' if is_activated else '未激活'}")
    print(f"消息: {message}")
    
    if expiry_date:
        print(f"过期时间: {expiry_date.strftime('%Y-%m-%d')}")
    
    return is_activated


def test_periodic_check(license_system):
    """测试定期检查功能"""
    print("\n=== 测试定期检查功能 ===")
    
    is_activated, message, expiry_date = license_system.periodic_check()
    
    print(f"定期检查结果: {'通过' if is_activated else '失败'}")
    print(f"消息: {message}")
    
    if expiry_date:
        print(f"过期时间: {expiry_date.strftime('%Y-%m-%d')}")
    
    return is_activated


def test_encryption(license_system):
    """测试加密功能"""
    print("\n=== 测试加密功能 ===")
    
    # 测试数据
    test_data = "这是一段测试数据，用于验证加密系统的功能。"
    print(f"原始数据: {test_data}")
    
    # 加密
    try:
        encrypted_data = license_system.encrypt_data(test_data)
        print(f"加密后数据: {encrypted_data.hex()[:50]}...")
        
        # 解密
        decrypted_data = license_system.decrypt_data(encrypted_data)
        print(f"解密后数据: {decrypted_data}")
        
        # 验证
        if decrypted_data == test_data:
            print("加密/解密测试通过!")
            return True
        else:
            print("加密/解密测试失败: 解密后数据与原始数据不匹配")
            return False
    except Exception as e:
        print(f"加密/解密测试出错: {str(e)}")
        return False


def test_anti_debug(license_system):
    """测试反调试功能"""
    print("\n=== 测试反调试功能 ===")
    
    result = license_system.anti_debug.run_all_checks()
    
    if result:
        print("警告: 检测到调试尝试!")
    else:
        print("未检测到调试尝试")
    
    return not result  # 如果没有检测到调试，则测试通过


def test_anti_tampering(license_system):
    """测试反篡改功能"""
    print("\n=== 测试反篡改功能 ===")
    
    result = license_system.anti_tampering.check_file_integrity()
    
    if result:
        print("文件完整性检查通过")
    else:
        print("警告: 检测到文件篡改!")
    
    return result


def run_all_tests(activation_key=None):
    """运行所有测试"""
    print("=" * 50)
    print("安全许可证系统测试")
    print("=" * 50)
    
    # 创建安全许可证系统
    license_system = SecureLicenseSystem()
    
    # 启动保护机制
    license_system.start_protection()
    print("安全保护系统已启动")
    
    # 测试激活状态
    is_activated = test_check_activation(license_system)
    
    # 如果未激活，尝试激活
    if not is_activated and activation_key:
        test_activation(license_system, activation_key)
        # 重新检查激活状态
        is_activated = test_check_activation(license_system)
    
    # 如果已激活，测试其他功能
    if is_activated:
        test_periodic_check(license_system)
        test_encryption(license_system)
        test_anti_debug(license_system)
        test_anti_tampering(license_system)
    
    print("\n=" * 25)
    print("测试完成")
    print("=" * 50)


if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="安全许可证系统测试工具")
    parser.add_argument("-k", "--key", help="激活码")
    parser.add_argument("-a", "--activate", action="store_true", help="仅测试激活功能")
    parser.add_argument("-c", "--check", action="store_true", help="仅测试激活状态检查")
    parser.add_argument("-p", "--periodic", action="store_true", help="仅测试定期检查")
    parser.add_argument("-e", "--encryption", action="store_true", help="仅测试加密功能")
    parser.add_argument("-d", "--debug", action="store_true", help="仅测试反调试功能")
    parser.add_argument("-t", "--tampering", action="store_true", help="仅测试反篡改功能")
    
    args = parser.parse_args()
    
    # 创建安全许可证系统
    license_system = SecureLicenseSystem()
    
    # 启动保护机制
    license_system.start_protection()
    print("安全保护系统已启动")
    
    # 根据参数运行测试
    if args.activate:
        test_activation(license_system, args.key)
    elif args.check:
        test_check_activation(license_system)
    elif args.periodic:
        test_periodic_check(license_system)
    elif args.encryption:
        test_encryption(license_system)
    elif args.debug:
        test_anti_debug(license_system)
    elif args.tampering:
        test_anti_tampering(license_system)
    else:
        # 运行所有测试
        run_all_tests(args.key)
    
    # 保持程序运行一段时间，以便观察
    try:
        print("\n程序将在10秒后自动退出，按Ctrl+C可立即退出...")
        time.sleep(10)
    except KeyboardInterrupt:
        print("\n用户中断，程序退出")
