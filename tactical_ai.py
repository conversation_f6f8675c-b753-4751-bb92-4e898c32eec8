#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
战术AI系统 - 专注击杀 + 智能目标管理
"""

import sys
import os
import time
import logging
import threading
from collections import deque

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('tactical_ai.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('tactical_ai')

class TacticalAI:
    """战术AI系统 - 专注击杀策略"""
    
    def __init__(self, input_simulator):
        self.input = input_simulator
        self.logger = logging.getLogger('tactical_ai')
        
        # 导入智能目标管理器
        from smart_target_manager import SmartTargetManager
        self.target_manager = SmartTargetManager(input_simulator)
        
        # AI状态
        self.is_active = False
        self.ai_thread = None
        
        # 技能配置
        self.basic_skills = ['1', '2', '3', '4', '5', '6', '7', '8', '9']
        self.alt_skills = ['a+1', 'a+2', 'a+3', 'a+4', 'a+5', 'a+6', 'a+7', 'a+8', 'a+9', 'a+0']
        self.all_skills = self.basic_skills + self.alt_skills
        self.current_skill_index = 0
        
        # 战术配置
        self.focus_kill_mode = True  # 专注击杀模式
        self.skill_interval = 0.2    # 技能间隔
        self.target_check_interval = 0.5  # 目标检查间隔
        
        # 战术策略
        self.strategies = {
            'focus_kill': {
                'description': '专注击杀 - 一个一个击杀',
                'target_switch_threshold': 0,  # 不主动切换目标
                'skill_rotation': 'mixed',
                'aggressive': True
            },
            'aoe_clear': {
                'description': 'AOE清理 - 快速切换目标',
                'target_switch_threshold': 3,  # 3秒后切换目标
                'skill_rotation': 'alt_heavy',
                'aggressive': True
            },
            'conservative': {
                'description': '保守策略 - 安全击杀',
                'target_switch_threshold': 8,  # 8秒后切换目标
                'skill_rotation': 'basic_only',
                'aggressive': False
            }
        }
        self.current_strategy = 'focus_kill'
        
        # 统计信息
        self.combat_stats = {
            'combat_sessions': 0,
            'total_combat_time': 0,
            'skills_per_minute': 0,
            'efficiency_score': 0
        }
        
        self.logger.info("战术AI系统已初始化")
    
    def set_strategy(self, strategy_name):
        """设置战术策略"""
        if strategy_name in self.strategies:
            self.current_strategy = strategy_name
            strategy = self.strategies[strategy_name]
            self.logger.info(f"战术策略已设置: {strategy['description']}")
        else:
            self.logger.error(f"无效的战术策略: {strategy_name}")
    
    def start_combat(self):
        """启动战术AI"""
        if self.is_active:
            self.logger.warning("战术AI已在运行中")
            return
        
        self.is_active = True
        self.combat_stats['combat_sessions'] += 1
        self.ai_thread = threading.Thread(target=self._tactical_loop, daemon=True)
        self.ai_thread.start()
        
        strategy = self.strategies[self.current_strategy]
        self.logger.info(f"🧠 战术AI启动 - 策略: {strategy['description']}")
    
    def stop_combat(self):
        """停止战术AI"""
        if not self.is_active:
            return
        
        self.is_active = False
        if self.ai_thread and self.ai_thread.is_alive():
            self.ai_thread.join(timeout=2.0)
        
        self.logger.info("🛑 战术AI已停止")
        self._print_combat_summary()
    
    def _tactical_loop(self):
        """战术AI主循环"""
        self.logger.info("进入战术AI循环...")
        combat_start_time = time.time()
        skills_cast = 0
        
        while self.is_active:
            try:
                loop_start = time.time()
                
                # 1. 智能目标管理
                if self.target_manager.should_select_new_target():
                    self._handle_target_selection()
                    time.sleep(0.1)  # 给目标选择一点时间
                
                # 2. 执行攻击
                if self.target_manager.current_target:
                    if self._execute_attack():
                        skills_cast += 1
                
                # 3. 显示战斗状态
                self._display_combat_status()
                
                # 4. 等待下一循环
                elapsed = time.time() - loop_start
                sleep_time = max(0, self.skill_interval - elapsed)
                time.sleep(sleep_time)
                
            except Exception as e:
                self.logger.error(f"战术循环错误: {e}")
                time.sleep(0.5)
        
        # 记录战斗统计
        combat_time = time.time() - combat_start_time
        self.combat_stats['total_combat_time'] += combat_time
        if combat_time > 0:
            self.combat_stats['skills_per_minute'] = (skills_cast / combat_time) * 60
    
    def _handle_target_selection(self):
        """处理目标选择"""
        try:
            success = self.target_manager.select_target()
            
            if success:
                target_info = self.target_manager.get_current_target_info()
                if target_info:
                    self.logger.info(f"🎯 锁定目标: {target_info['name']} - 开始专注击杀")
            else:
                self.logger.warning("⚠ 目标选择失败，等待重试...")
                time.sleep(1.0)  # 失败时等待更长时间
                
        except Exception as e:
            self.logger.error(f"目标选择处理错误: {e}")
    
    def _execute_attack(self) -> bool:
        """执行攻击"""
        try:
            # 获取下一个技能
            skill = self._get_next_skill()
            if not skill:
                return False
            
            # 发送技能
            success = self._send_skill(skill)
            
            if success:
                target_info = self.target_manager.get_current_target_info()
                if target_info:
                    self.logger.debug(f"⚔️ 攻击 {target_info['name']}: {skill} (血量: {target_info['hp_percent']:.1f}%)")
                return True
            else:
                self.logger.warning(f"⚠ 技能发送失败: {skill}")
                return False
                
        except Exception as e:
            self.logger.error(f"攻击执行错误: {e}")
            return False
    
    def _get_next_skill(self) -> str:
        """获取下一个技能"""
        strategy = self.strategies[self.current_strategy]
        rotation = strategy['skill_rotation']
        
        if rotation == 'basic_only':
            skills = self.basic_skills
        elif rotation == 'alt_heavy':
            # Alt技能为主，基础技能为辅
            skills = self.alt_skills + self.basic_skills[::2]  # 每隔一个基础技能
        else:  # mixed
            skills = self.all_skills
        
        skill = skills[self.current_skill_index % len(skills)]
        self.current_skill_index += 1
        
        return skill
    
    def _send_skill(self, skill) -> bool:
        """发送技能"""
        try:
            if skill.startswith('a+'):
                # Alt组合键
                key_part = skill.split('+')[1]
                return self.input.press_alt_key(key_part)
            else:
                # 普通按键
                return self.input.press_key(skill)
        except Exception as e:
            self.logger.error(f"技能发送错误: {e}")
            return False
    
    def _display_combat_status(self):
        """显示战斗状态"""
        target_info = self.target_manager.get_current_target_info()
        
        if target_info:
            # 每5秒显示一次详细状态
            if int(time.time()) % 5 == 0:
                self.logger.info(f"🎯 当前目标: {target_info['name']}")
                self.logger.info(f"   血量: {target_info['hp_percent']:.1f}%")
                self.logger.info(f"   攻击时长: {target_info['attack_duration']:.1f}秒")
                self.logger.info(f"   状态: {target_info['state']}")
    
    def _print_combat_summary(self):
        """打印战斗总结"""
        target_stats = self.target_manager.get_stats()
        
        self.logger.info("📊 战斗总结:")
        self.logger.info(f"   击杀数量: {target_stats['targets_killed']}")
        self.logger.info(f"   丢失目标: {target_stats['targets_lost']}")
        self.logger.info(f"   平均击杀时间: {target_stats['average_kill_time']:.1f}秒")
        self.logger.info(f"   技能释放速度: {self.combat_stats['skills_per_minute']:.1f} 技能/分钟")
        
        # 计算效率评分
        if target_stats['targets_killed'] > 0:
            efficiency = target_stats['targets_killed'] / (target_stats['targets_killed'] + target_stats['targets_lost'])
            self.logger.info(f"   击杀效率: {efficiency*100:.1f}%")
    
    def test_focus_kill_strategy(self, duration=30):
        """测试专注击杀策略"""
        self.logger.info(f"🧪 测试专注击杀策略 ({duration}秒)...")
        
        self.set_strategy('focus_kill')
        self.start_combat()
        
        try:
            time.sleep(duration)
        except KeyboardInterrupt:
            self.logger.info("用户中断测试")
        finally:
            self.stop_combat()
    
    def get_status(self):
        """获取AI状态"""
        target_info = self.target_manager.get_current_target_info()
        target_stats = self.target_manager.get_stats()
        
        return {
            'is_active': self.is_active,
            'strategy': self.current_strategy,
            'current_target': target_info,
            'target_stats': target_stats,
            'combat_stats': self.combat_stats
        }

def main():
    """主函数"""
    logger = setup_logging()
    
    print("=" * 60)
    print("🧠 永恒之塔战术AI系统 - 专注击杀")
    print("=" * 60)
    
    try:
        # 1. 创建输入模拟器
        logger.info("创建输入模拟器...")
        from working_input_simulator import WorkingInputSimulator
        input_sim = WorkingInputSimulator()
        
        # 2. 创建战术AI
        logger.info("创建战术AI系统...")
        tactical_ai = TacticalAI(input_sim)
        
        # 3. 显示系统状态
        status = input_sim.get_status()
        print("\\n📊 系统状态:")
        print(f"  管理员权限: {'✓' if status['admin_rights'] else '✗'}")
        print(f"  找到游戏窗口: {'✓' if status['game_window_found'] else '✗'}")
        print(f"  游戏进程ID: {status['game_process_id'] or '未找到'}")
        
        # 4. 显示战术策略
        print("\\n🎯 可用战术策略:")
        for name, strategy in tactical_ai.strategies.items():
            print(f"  {name}: {strategy['description']}")
        
        # 5. 用户选择
        while True:
            print("\\n🎮 请选择操作:")
            print("1. 测试专注击杀策略 (30秒)")
            print("2. 设置战术策略")
            print("3. 启动战术AI")
            print("4. 查看AI状态")
            print("5. 退出程序")
            
            try:
                choice = input("\\n请输入选择 (1-5): ").strip()
                
                if choice == '1':
                    print("\\n🧪 测试专注击杀策略...")
                    print("系统将专注击杀单个目标，不会随意切换")
                    tactical_ai.test_focus_kill_strategy(30)
                    
                elif choice == '2':
                    print("\\n可用策略:")
                    for name, strategy in tactical_ai.strategies.items():
                        print(f"  {name}: {strategy['description']}")
                    
                    strategy_name = input("请输入策略名称: ").strip()
                    tactical_ai.set_strategy(strategy_name)
                    
                elif choice == '3':
                    print("\\n🧠 启动战术AI...")
                    print(f"当前策略: {tactical_ai.current_strategy}")
                    print("系统将智能管理目标，专注击杀")
                    print("按 Ctrl+C 停止")
                    
                    tactical_ai.start_combat()
                    
                    try:
                        while tactical_ai.is_active:
                            time.sleep(1)
                    except KeyboardInterrupt:
                        print("\\n⏹ 用户中断，停止战术AI...")
                        tactical_ai.stop_combat()
                    
                elif choice == '4':
                    print("\\n📊 AI状态:")
                    status = tactical_ai.get_status()
                    print(f"  运行状态: {'运行中' if status['is_active'] else '已停止'}")
                    print(f"  当前策略: {status['strategy']}")
                    
                    if status['current_target']:
                        target = status['current_target']
                        print(f"  当前目标: {target['name']}")
                        print(f"  目标血量: {target['hp_percent']:.1f}%")
                        print(f"  攻击时长: {target['attack_duration']:.1f}秒")
                    else:
                        print("  当前目标: 无")
                    
                    stats = status['target_stats']
                    print(f"  击杀数量: {stats['targets_killed']}")
                    print(f"  平均击杀时间: {stats['average_kill_time']:.1f}秒")
                    
                elif choice == '5':
                    print("\\n👋 程序退出")
                    break
                    
                else:
                    print("❌ 无效选择，请输入 1-5")
                    
            except KeyboardInterrupt:
                print("\\n👋 程序退出")
                break
            except Exception as e:
                logger.error(f"用户交互错误: {e}")
        
        return True
        
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        print(f"\\n❌ 导入失败: {e}")
        return False
        
    except Exception as e:
        logger.error(f"程序运行错误: {e}")
        print(f"\\n❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\\n👋 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\\n💥 程序崩溃: {e}")
        sys.exit(1)