"""
安全许可证系统演示程序
展示安全许可证系统的主要功能
"""

import os
import sys
import time
import tkinter as tk
from tkinter import messagebox, simpledialog
from datetime import datetime

# 导入安全许可证系统
try:
    from secure_license_system import SecureLicenseSystem
except ImportError as e:
    print(f"错误：无法导入安全许可证系统: {str(e)}")
    sys.exit(1)


class LicenseDemoApp:
    """许可证系统演示应用"""
    
    def __init__(self, root):
        """初始化演示应用"""
        self.root = root
        self.root.title("安全许可证系统演示")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 创建安全许可证系统
        self.license_system = SecureLicenseSystem()
        
        # 启动保护机制
        self.license_system.start_protection()
        
        # 创建界面
        self._create_ui()
        
        # 检查激活状态
        self._check_activation()
    
    def _create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(main_frame, text="安全许可证系统演示", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 激活状态框架
        status_frame = tk.LabelFrame(main_frame, text="激活状态", padx=10, pady=10)
        status_frame.pack(fill=tk.X, pady=10)
        
        # 激活状态标签
        self.status_var = tk.StringVar(value="正在检查激活状态...")
        status_label = tk.Label(status_frame, textvariable=self.status_var, font=("Arial", 10))
        status_label.pack(fill=tk.X)
        
        # 过期时间标签
        self.expiry_var = tk.StringVar(value="")
        expiry_label = tk.Label(status_frame, textvariable=self.expiry_var, font=("Arial", 10))
        expiry_label.pack(fill=tk.X)
        
        # 按钮框架
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 激活按钮
        activate_button = tk.Button(button_frame, text="激活软件", command=self._activate_software)
        activate_button.pack(side=tk.LEFT, padx=5)
        
        # 检查激活状态按钮
        check_button = tk.Button(button_frame, text="检查激活状态", command=self._check_activation)
        check_button.pack(side=tk.LEFT, padx=5)
        
        # 测试功能框架
        test_frame = tk.LabelFrame(main_frame, text="测试功能", padx=10, pady=10)
        test_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 测试按钮
        test_encryption_button = tk.Button(test_frame, text="测试加密功能", command=self._test_encryption)
        test_encryption_button.pack(fill=tk.X, pady=5)
        
        test_debug_button = tk.Button(test_frame, text="测试反调试功能", command=self._test_anti_debug)
        test_debug_button.pack(fill=tk.X, pady=5)
        
        test_tampering_button = tk.Button(test_frame, text="测试反篡改功能", command=self._test_anti_tampering)
        test_tampering_button.pack(fill=tk.X, pady=5)
        
        # 日志框架
        log_frame = tk.LabelFrame(main_frame, text="日志", padx=10, pady=10)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 日志文本框
        self.log_text = tk.Text(log_frame, height=10, width=50)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 滚动条
        scrollbar = tk.Scrollbar(self.log_text)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 配置滚动条
        self.log_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.log_text.yview)
    
    def _log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
    
    def _check_activation(self):
        """检查激活状态"""
        self._log("正在检查激活状态...")
        
        is_activated, message, expiry_date = self.license_system.check_activation()
        
        if is_activated:
            self.status_var.set(f"激活状态: 已激活 - {message}")
            if expiry_date:
                self.expiry_var.set(f"过期时间: {expiry_date.strftime('%Y-%m-%d')}")
            else:
                self.expiry_var.set("过期时间: 永久")
            self._log(f"激活检查成功: {message}")
        else:
            self.status_var.set(f"激活状态: 未激活 - {message}")
            self.expiry_var.set("")
            self._log(f"激活检查失败: {message}")
            
            # 询问是否激活
            if messagebox.askyesno("激活提示", "软件未激活，是否现在激活？"):
                self._activate_software()
    
    def _activate_software(self):
        """激活软件"""
        self._log("正在激活软件...")
        
        # 获取激活码
        activation_key = simpledialog.askstring("激活", "请输入激活码:", parent=self.root)
        
        if not activation_key:
            self._log("激活取消")
            return
        
        # 尝试激活
        is_success, message = self.license_system.activate(activation_key)
        
        if is_success:
            messagebox.showinfo("激活成功", f"软件已成功激活: {message}")
            self._log(f"激活成功: {message}")
            # 更新激活状态
            self._check_activation()
        else:
            messagebox.showerror("激活失败", f"激活失败: {message}")
            self._log(f"激活失败: {message}")
    
    def _test_encryption(self):
        """测试加密功能"""
        self._log("正在测试加密功能...")
        
        # 测试数据
        test_data = "这是一段测试数据，用于验证加密系统的功能。"
        self._log(f"原始数据: {test_data}")
        
        # 加密
        try:
            encrypted_data = self.license_system.encrypt_data(test_data)
            self._log(f"加密后数据: {encrypted_data.hex()[:50]}...")
            
            # 解密
            decrypted_data = self.license_system.decrypt_data(encrypted_data)
            self._log(f"解密后数据: {decrypted_data}")
            
            # 验证
            if decrypted_data == test_data:
                self._log("加密/解密测试通过!")
                messagebox.showinfo("测试结果", "加密/解密测试通过!")
            else:
                self._log("加密/解密测试失败: 解密后数据与原始数据不匹配")
                messagebox.showerror("测试结果", "加密/解密测试失败: 解密后数据与原始数据不匹配")
        except Exception as e:
            self._log(f"加密/解密测试出错: {str(e)}")
            messagebox.showerror("测试结果", f"加密/解密测试出错: {str(e)}")
    
    def _test_anti_debug(self):
        """测试反调试功能"""
        self._log("正在测试反调试功能...")
        
        result = self.license_system.anti_debug.run_all_checks()
        
        if result:
            self._log("警告: 检测到调试尝试!")
            messagebox.showwarning("测试结果", "警告: 检测到调试尝试!")
        else:
            self._log("未检测到调试尝试")
            messagebox.showinfo("测试结果", "未检测到调试尝试")
    
    def _test_anti_tampering(self):
        """测试反篡改功能"""
        self._log("正在测试反篡改功能...")
        
        result = self.license_system.anti_tampering.check_file_integrity()
        
        if result:
            self._log("文件完整性检查通过")
            messagebox.showinfo("测试结果", "文件完整性检查通过")
        else:
            self._log("警告: 检测到文件篡改!")
            messagebox.showwarning("测试结果", "警告: 检测到文件篡改!")


def main():
    """主函数"""
    root = tk.Tk()
    app = LicenseDemoApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
