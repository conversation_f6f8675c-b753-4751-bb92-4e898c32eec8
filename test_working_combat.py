123 #!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试真正能工作的自动战斗系统
"""

import sys
import os
import time
import logging
import threading

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('working_combat_test.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('test_working_combat')

class SimpleAutoCombat:
    """简化的自动战斗系统 - 专注于实际按键发送"""
    
    def __init__(self, input_simulator):
        self.input = input_simulator
        self.is_active = False
        self.combat_thread = None
        self.logger = logging.getLogger('simple_auto_combat')
        
        # 技能按键序列 - 包含Alt组合键
        self.skill_keys = [
            '1', '2', '3', '4', '5', '6', '7', '8', '9',  # 基础技能
            'a+1', 'a+2', 'a+3', 'a+4', 'a+5', 'a+6', 'a+7', 'a+8', 'a+9', 'a+0'  # Alt组合技能
        ]
        self.current_skill_index = 0
        
        # 战斗配置 - 优化速度
        self.skill_interval = 0.3   # 技能间隔（秒）- 提高到每秒3个技能
        self.target_interval = 2.0  # 选择目标间隔（秒）
        self.target_key = 'tab'     # 选择目标按键
        self.last_target_time = 0   # 上次选择目标时间
        
        self.logger.info("简化自动战斗系统已初始化")
    
    def start(self):
        """启动自动战斗"""
        if self.is_active:
            self.logger.warning("自动战斗已经在运行中")
            return
        
        self.is_active = True
        self.combat_thread = threading.Thread(target=self._combat_loop, daemon=True)
        self.combat_thread.start()
        self.logger.info("🚀 自动战斗已启动")
    
    def stop(self):
        """停止自动战斗"""
        if not self.is_active:
            return
        
        self.is_active = False
        if self.combat_thread and self.combat_thread.is_alive():
            self.combat_thread.join(timeout=2.0)
        
        self.logger.info("⏹ 自动战斗已停止")
    
    def _combat_loop(self):
        """战斗主循环 - 优化版"""
        self.logger.info("进入战斗循环...")
        
        while self.is_active:
            try:
                current_time = time.time()
                
                # 1. 定期选择目标（每2秒一次）
                if current_time - self.last_target_time >= self.target_interval:
                    self._select_target()
                    self.last_target_time = current_time
                    time.sleep(0.1)  # 短暂等待目标选择完成
                
                # 2. 快速释放技能
                self._cast_next_skill()
                
                # 3. 短暂等待下一次循环
                time.sleep(self.skill_interval)
                
            except Exception as e:
                self.logger.error(f"战斗循环错误: {e}")
                time.sleep(0.5)  # 减少错误等待时间
    
    def _select_target(self):
        """选择目标"""
        try:
            success = self.input.press_key(self.target_key)
            if success:
                self.logger.info(f"🎯 选择目标: {self.target_key}")
            else:
                self.logger.warning(f"⚠ 选择目标失败: {self.target_key}")
        except Exception as e:
            self.logger.error(f"选择目标错误: {e}")
    
    def _cast_next_skill(self):
        """释放下一个技能"""
        try:
            # 获取当前技能
            skill_key = self.skill_keys[self.current_skill_index]
            
            # 处理Alt组合键
            if skill_key.startswith('a+'):
                key_part = skill_key.split('+')[1]
                success = self.input.press_alt_key(key_part)
                self.logger.info(f"⚔️ 释放Alt技能: Alt+{key_part}")
            else:
                success = self.input.press_key(skill_key)
                self.logger.info(f"⚔️ 释放技能: {skill_key}")
            
            if not success:
                self.logger.warning(f"⚠ 技能释放失败: {skill_key}")
            
            # 切换到下一个技能
            self.current_skill_index = (self.current_skill_index + 1) % len(self.skill_keys)
            
        except Exception as e:
            self.logger.error(f"释放技能错误: {e}")
    
    def test_all_skills(self):
        """测试所有技能按键"""
        self.logger.info("开始测试所有技能按键...")
        
        for i, skill_key in enumerate(self.skill_keys):
            self.logger.info(f"测试技能 {i+1}/{len(self.skill_keys)}: {skill_key}")
            
            # 处理Alt组合键
            if skill_key.startswith('a+'):
                key_part = skill_key.split('+')[1]
                success = self.input.press_alt_key(key_part)
                self.logger.info(f"  发送Alt组合键: Alt+{key_part}")
            else:
                success = self.input.press_key(skill_key)
                self.logger.info(f"  发送普通按键: {skill_key}")
            
            if success:
                self.logger.info(f"  ✓ {skill_key} 发送成功")
            else:
                self.logger.error(f"  ✗ {skill_key} 发送失败")
            
            time.sleep(0.8)  # 给用户更多时间观察效果
        
        self.logger.info("技能按键测试完成")

def main():
    """主函数"""
    logger = setup_logging()
    
    print("=" * 60)
    print("🎮 永恒之塔自动战斗测试程序")
    print("=" * 60)
    
    try:
        # 1. 创建输入模拟器
        logger.info("创建输入模拟器...")
        from working_input_simulator import WorkingInputSimulator
        input_sim = WorkingInputSimulator()
        
        # 2. 测试输入功能
        logger.info("测试输入功能...")
        if not input_sim.test_input():
            logger.error("输入测试失败，程序可能无法正常工作")
            print("\\n❌ 输入测试失败！")
            print("建议:")
            print("1. 以管理员权限运行程序")
            print("2. 确保游戏窗口在前台")
            print("3. 检查游戏是否正在运行")
            return False
        
        # 3. 创建自动战斗系统
        logger.info("创建自动战斗系统...")
        auto_combat = SimpleAutoCombat(input_sim)
        
        # 4. 显示状态信息
        status = input_sim.get_status()
        print("\\n📊 系统状态:")
        print(f"  管理员权限: {'✓' if status['admin_rights'] else '✗'}")
        print(f"  找到游戏窗口: {'✓' if status['game_window_found'] else '✗'}")
        print(f"  游戏进程ID: {status['game_process_id'] or '未找到'}")
        
        # 5. 用户选择
        print("\\n🎯 请选择操作:")
        print("1. 测试所有技能按键 (1-9)")
        print("2. 启动自动战斗 (持续释放技能)")
        print("3. 退出程序")
        
        while True:
            try:
                choice = input("\\n请输入选择 (1-3): ").strip()
                
                if choice == '1':
                    print("\\n🧪 开始测试技能按键...")
                    print("请观察游戏中是否有技能释放效果")
                    auto_combat.test_all_skills()
                    
                elif choice == '2':
                    print("\\n🚀 启动自动战斗...")
                    print("程序将持续选择目标并释放技能")
                    print("按 Ctrl+C 停止")
                    
                    auto_combat.start()
                    
                    try:
                        while auto_combat.is_active:
                            time.sleep(1)
                    except KeyboardInterrupt:
                        print("\\n⏹ 用户中断，停止自动战斗...")
                        auto_combat.stop()
                    
                elif choice == '3':
                    print("\\n👋 程序退出")
                    break
                    
                else:
                    print("❌ 无效选择，请输入 1-3")
                    
            except KeyboardInterrupt:
                print("\\n👋 程序退出")
                break
            except Exception as e:
                logger.error(f"用户交互错误: {e}")
        
        return True
        
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        print(f"\\n❌ 导入失败: {e}")
        print("请确保所有必要的文件都存在")
        return False
        
    except Exception as e:
        logger.error(f"程序运行错误: {e}")
        print(f"\\n❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\\n👋 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\\n💥 程序崩溃: {e}")
        sys.exit(1)