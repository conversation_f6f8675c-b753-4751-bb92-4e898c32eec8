#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试enable_debug_mode方法是否已正确添加
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enable_debug_mode():
    """测试enable_debug_mode方法"""
    print("=" * 50)
    print("测试enable_debug_mode方法")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from auto_combat_system import AutoCombatSystem
        from memory_reader import MemoryReader
        from input_simulator import InputSimulator
        from monster_select import MonsterSelector
        
        print("✓ 模块导入成功")
        
        # 创建组件
        memory = MemoryReader()
        input_sim = InputSimulator()
        monster_selector = MonsterSelector(memory)
        
        # 创建自动战斗系统
        auto_combat = AutoCombatSystem(memory, input_sim, monster_selector)
        print("✓ AutoCombatSystem创建成功")
        
        # 测试enable_debug_mode方法是否存在
        if hasattr(auto_combat, 'enable_debug_mode'):
            print("✓ enable_debug_mode方法存在")
            
            # 测试调用方法
            auto_combat.enable_debug_mode(True)
            print("✓ enable_debug_mode(True)调用成功")
            
            auto_combat.enable_debug_mode(False)
            print("✓ enable_debug_mode(False)调用成功")
            
            return True
        else:
            print("❌ enable_debug_mode方法不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试enable_debug_mode方法修复...")
    
    if test_enable_debug_mode():
        print("\\n🎉 测试成功!")
        print("enable_debug_mode方法已正确添加到AutoCombatSystem类中")
        print("\\n现在main.py应该能够正常启动了")
        return True
    else:
        print("\\n❌ 测试失败!")
        print("需要进一步检查AutoCombatSystem类的实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)