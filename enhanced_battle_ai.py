#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强战斗AI系统 - 基于Aion服务端AI逻辑的完善副本辅助
整合了服务端的智能战斗决策算法
"""

import time
import logging
import random
import threading
import queue
import numpy as np
from collections import deque
from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
import json

# 设置日志
logger = logging.getLogger('enhanced_battle_ai')
logger.setLevel(logging.INFO)

class AttackIntention(Enum):
    """攻击意图枚举 - 基于Aion服务端AI"""
    FINISH_ATTACK = "finish_attack"
    SWITCH_TARGET = "switch_target"
    SKILL_ATTACK = "skill_attack"
    SIMPLE_ATTACK = "simple_attack"
    DEFENSIVE_ACTION = "defensive_action"
    BUFF_SELF = "buff_self"
    HEAL_SELF = "heal_self"

class BattleState(Enum):
    """战斗状态枚举"""
    IDLE = "idle"
    SEARCHING = "searching"
    ENGAGING = "engaging"
    FIGHTING = "fighting"
    RETREATING = "retreating"
    DEAD = "dead"

@dataclass
class Target:
    """目标信息"""
    id: int
    name: str = ""
    hp_percent: float = 100.0
    distance: float = 0.0
    threat_level: int = 1
    last_seen: float = 0.0
    is_alive: bool = True
    aggro_level: int = 0

@dataclass
class Skill:
    """技能信息"""
    id: str
    key: str
    cooldown: float
    damage: int
    range: float
    mana_cost: int
    cast_time: float
    priority: int
    skill_type: str  # attack, buff, heal, debuff
    last_used: float = 0.0
    is_ready: bool = True

class EnhancedBattleAI:
    """增强战斗AI系统 - 基于Aion服务端逻辑"""
    
    def __init__(self, memory_reader, input_simulator, monster_selector):
        """初始化增强战斗AI"""
        self.memory = memory_reader
        self.input = input_simulator
        self.monster_selector = monster_selector
        
        # 核心AI状态
        self.is_active = False
        self.battle_state = BattleState.IDLE
        self.current_target: Optional[Target] = None
        self.target_list: List[Target] = []
        self.aggro_list: Dict[int, int] = {}  # 仇恨列表
        
        # AI思考循环
        self.think_thread = None
        self.think_active = False
        self.think_interval = 0.1  # 100ms思考间隔
        
        # 技能系统 - 基于服务端NPC技能逻辑
        self.skills: Dict[str, Skill] = {}
        self.skill_queue = queue.Queue()
        self.global_cooldown = 0.05
        self.last_skill_time = 0.0
        
        # 目标选择算法
        self.target_selection_algorithm = "most_hated"  # most_hated, nearest, weakest
        self.target_switch_cooldown = 1.0  # 目标切换冷却
        self.last_target_switch = 0.0
        
        # 战斗参数
        self.attack_range = 30.0
        self.retreat_hp_threshold = 20.0
        self.heal_hp_threshold = 50.0
        self.buff_duration = 300.0  # 5分钟buff持续时间
        
        # 智能决策系统
        self.decision_weights = {
            "attack": 1.0,
            "skill": 1.5,
            "heal": 2.0,
            "buff": 0.8,
            "retreat": 3.0
        }
        
        # 学习系统
        self.battle_history = deque(maxlen=100)
        self.success_patterns = {}
        self.failure_patterns = {}
        
        # 初始化技能系统
        self.initialize_skills()
        
        # 游戏日志回调
        self.game_log_callback = None
        
        logger.info("增强战斗AI系统初始化完成")

    def initialize_skills(self):
        """初始化技能系统 - 基于服务端技能配置"""
        # 基础攻击技能
        attack_skills = [
            Skill("attack1", "1", 0.05, 100, 25.0, 10, 0.5, 1, "attack"),
            Skill("attack2", "2", 0.05, 120, 25.0, 15, 0.6, 2, "attack"),
            Skill("attack3", "3", 0.05, 140, 25.0, 20, 0.7, 3, "attack"),
            Skill("attack4", "4", 0.05, 160, 25.0, 25, 0.8, 4, "attack"),
            Skill("attack5", "5", 0.05, 180, 25.0, 30, 0.9, 5, "attack"),
            Skill("attack6", "6", 0.05, 200, 25.0, 35, 1.0, 6, "attack"),
            Skill("attack7", "7", 0.05, 220, 25.0, 40, 1.1, 7, "attack"),
            Skill("attack8", "8", 0.05, 240, 25.0, 45, 1.2, 8, "attack"),
            Skill("attack9", "9", 0.05, 260, 25.0, 50, 1.3, 9, "attack"),
        ]
        
        # Alt组合技能 (高级技能)
        alt_skills = [
            Skill("alt_attack1", "alt+1", 2.0, 300, 30.0, 80, 1.5, 10, "skill"),
            Skill("alt_attack2", "alt+2", 2.5, 350, 30.0, 90, 1.8, 11, "skill"),
            Skill("alt_attack3", "alt+3", 3.0, 400, 30.0, 100, 2.0, 12, "skill"),
            Skill("alt_attack4", "alt+4", 3.5, 450, 30.0, 110, 2.2, 13, "skill"),
            Skill("alt_attack5", "alt+5", 4.0, 500, 30.0, 120, 2.5, 14, "skill"),
            Skill("alt_attack6", "alt+6", 4.5, 550, 30.0, 130, 2.8, 15, "skill"),
            Skill("alt_attack7", "alt+7", 5.0, 600, 30.0, 140, 3.0, 16, "skill"),
            Skill("alt_attack8", "alt+8", 5.5, 650, 30.0, 150, 3.2, 17, "skill"),
            Skill("alt_attack9", "alt+9", 6.0, 700, 30.0, 160, 3.5, 18, "skill"),
        ]
        
        # 辅助技能
        support_skills = [
            Skill("heal", "h", 5.0, 0, 0.0, 100, 2.0, 100, "heal"),
            Skill("buff", "b", 10.0, 0, 0.0, 50, 1.0, 90, "buff"),
        ]
        
        # 合并所有技能
        all_skills = attack_skills + alt_skills + support_skills
        
        for skill in all_skills:
            self.skills[skill.id] = skill
        
        logger.info(f"已初始化 {len(self.skills)} 个技能")

    def start(self):
        """启动增强战斗AI"""
        if self.is_active:
            return
        
        self.is_active = True
        self.battle_state = BattleState.SEARCHING
        
        # 启动AI思考线程
        self.think_active = True
        self.think_thread = threading.Thread(target=self._think_loop, daemon=True)
        self.think_thread.start()
        
        self._log("增强战斗AI已启动")

    def stop(self):
        """停止增强战斗AI"""
        if not self.is_active:
            return
        
        self.is_active = False
        self.think_active = False
        self.battle_state = BattleState.IDLE
        
        if self.think_thread and self.think_thread.is_alive():
            self.think_thread.join(timeout=1.0)
        
        self._log("增强战斗AI已停止")

    def _think_loop(self):
        """AI思考循环 - 核心决策引擎"""
        while self.think_active:
            try:
                # 执行AI思考
                self._think()
                time.sleep(self.think_interval)
            except Exception as e:
                logger.error(f"AI思考循环错误: {e}")
                time.sleep(0.5)

    def _think(self):
        """AI思考方法 - 基于Aion服务端AI逻辑"""
        if not self.is_active:
            return
        
        # 1. 更新目标列表
        self._update_targets()
        
        # 2. 选择攻击意图
        intention = self._choose_attack_intention()
        
        # 3. 执行攻击意图
        self._execute_intention(intention)
        
        # 4. 更新战斗状态
        self._update_battle_state()

    def _update_targets(self):
        """更新目标列表"""
        try:
            # 从怪物选择器获取目标
            monsters = self.monster_selector.get_nearby_monsters(self.attack_range)
            
            # 更新目标列表
            self.target_list.clear()
            current_time = time.time()
            
            for monster in monsters:
                target = Target(
                    id=monster.get('id', 0),
                    name=monster.get('name', '未知怪物'),
                    hp_percent=monster.get('hp_percent', 100.0),
                    distance=monster.get('distance', 999.0),
                    threat_level=self._calculate_threat_level(monster),
                    last_seen=current_time,
                    is_alive=monster.get('hp_percent', 0) > 0
                )
                self.target_list.append(target)
            
        except Exception as e:
            logger.error(f"更新目标列表错误: {e}")

    def _choose_attack_intention(self) -> AttackIntention:
        """选择攻击意图 - 基于Aion服务端chooseAttackIntention逻辑"""
        current_time = time.time()
        
        # 检查当前目标
        most_hated = self._get_most_hated_target()
        
        # 1. 如果没有目标或目标已死亡，结束攻击
        if not most_hated or not most_hated.is_alive:
            return AttackIntention.FINISH_ATTACK
        
        # 2. 如果当前目标不是最仇恨目标，切换目标
        if (self.current_target is None or 
            self.current_target.id != most_hated.id):
            if current_time - self.last_target_switch > self.target_switch_cooldown:
                return AttackIntention.SWITCH_TARGET
        
        # 3. 检查是否需要治疗
        player_hp = self._get_player_hp_percent()
        if player_hp < self.heal_hp_threshold:
            return AttackIntention.HEAL_SELF
        
        # 4. 检查是否需要buff
        if self._should_buff():
            return AttackIntention.BUFF_SELF
        
        # 5. 选择技能攻击
        best_skill = self._choose_best_skill(most_hated)
        if best_skill:
            return AttackIntention.SKILL_ATTACK
        
        # 6. 默认普通攻击
        return AttackIntention.SIMPLE_ATTACK

    def _execute_intention(self, intention: AttackIntention):
        """执行攻击意图"""
        current_time = time.time()
        
        if intention == AttackIntention.FINISH_ATTACK:
            self._finish_attack()
        
        elif intention == AttackIntention.SWITCH_TARGET:
            self._switch_target()
        
        elif intention == AttackIntention.SKILL_ATTACK:
            self._execute_skill_attack()
        
        elif intention == AttackIntention.SIMPLE_ATTACK:
            self._execute_simple_attack()
        
        elif intention == AttackIntention.HEAL_SELF:
            self._execute_heal()
        
        elif intention == AttackIntention.BUFF_SELF:
            self._execute_buff()

    def _get_most_hated_target(self) -> Optional[Target]:
        """获取最仇恨目标 - 基于服务端getAggroList().getMostHated()"""
        if not self.target_list:
            return None
        
        # 根据仇恨值、距离、血量等因素选择目标
        best_target = None
        best_score = -1
        
        for target in self.target_list:
            if not target.is_alive:
                continue
            
            # 计算目标评分
            score = self._calculate_target_score(target)
            
            if score > best_score:
                best_score = score
                best_target = target
        
        return best_target

    def _calculate_target_score(self, target: Target) -> float:
        """计算目标评分"""
        score = 0.0
        
        # 仇恨值权重
        aggro = self.aggro_list.get(target.id, 0)
        score += aggro * 10.0
        
        # 距离权重（越近越好）
        if target.distance > 0:
            score += (50.0 - target.distance) * 2.0
        
        # 血量权重（优先攻击残血）
        if target.hp_percent < 30:
            score += 20.0
        elif target.hp_percent < 60:
            score += 10.0
        
        # 威胁等级权重
        score += target.threat_level * 5.0
        
        return score

    def _choose_best_skill(self, target: Target) -> Optional[Skill]:
        """选择最佳技能 - 基于服务端SkillAttackManager.chooseNextSkill"""
        if not target:
            return None
        
        current_time = time.time()
        available_skills = []
        
        # 筛选可用技能
        for skill in self.skills.values():
            if (skill.skill_type in ["attack", "skill"] and
                current_time - skill.last_used >= skill.cooldown and
                target.distance <= skill.range):
                available_skills.append(skill)
        
        if not available_skills:
            return None
        
        # 根据优先级和伤害选择最佳技能
        best_skill = max(available_skills, 
                        key=lambda s: s.damage * (1.0 / s.priority))
        
        return best_skill

    def _execute_skill_attack(self):
        """执行技能攻击"""
        if not self.current_target:
            return
        
        skill = self._choose_best_skill(self.current_target)
        if not skill:
            return
        
        current_time = time.time()
        
        # 检查全局冷却
        if current_time - self.last_skill_time < self.global_cooldown:
            return
        
        # 执行技能
        try:
            self.input.press_key(skill.key)
            skill.last_used = current_time
            self.last_skill_time = current_time
            
            # 更新仇恨值
            if self.current_target.id in self.aggro_list:
                self.aggro_list[self.current_target.id] += skill.damage // 10
            else:
                self.aggro_list[self.current_target.id] = skill.damage // 10
            
            self._log(f"使用技能: {skill.id} -> {self.current_target.name}")
            
        except Exception as e:
            logger.error(f"执行技能攻击错误: {e}")

    def _execute_simple_attack(self):
        """执行普通攻击"""
        if not self.current_target:
            return
        
        try:
            # 使用基础攻击技能
            basic_skill = self.skills.get("attack1")
            if basic_skill:
                current_time = time.time()
                if current_time - basic_skill.last_used >= basic_skill.cooldown:
                    self.input.press_key(basic_skill.key)
                    basic_skill.last_used = current_time
                    self.last_skill_time = current_time
                    
                    self._log(f"普通攻击: {self.current_target.name}")
            
        except Exception as e:
            logger.error(f"执行普通攻击错误: {e}")

    def _switch_target(self):
        """切换目标"""
        new_target = self._get_most_hated_target()
        if new_target and new_target != self.current_target:
            old_target_name = self.current_target.name if self.current_target else "无"
            self.current_target = new_target
            self.last_target_switch = time.time()
            
            # 选择目标
            try:
                self.monster_selector.select_monster_by_id(new_target.id)
                self._log(f"切换目标: {old_target_name} -> {new_target.name}")
            except Exception as e:
                logger.error(f"切换目标错误: {e}")

    def _finish_attack(self):
        """结束攻击"""
        self.current_target = None
        self.battle_state = BattleState.SEARCHING
        self._log("结束攻击，搜索新目标")

    def _execute_heal(self):
        """执行治疗"""
        heal_skill = self.skills.get("heal")
        if heal_skill:
            current_time = time.time()
            if current_time - heal_skill.last_used >= heal_skill.cooldown:
                try:
                    self.input.press_key(heal_skill.key)
                    heal_skill.last_used = current_time
                    self._log("使用治疗技能")
                except Exception as e:
                    logger.error(f"执行治疗错误: {e}")

    def _execute_buff(self):
        """执行buff"""
        buff_skill = self.skills.get("buff")
        if buff_skill:
            current_time = time.time()
            if current_time - buff_skill.last_used >= buff_skill.cooldown:
                try:
                    self.input.press_key(buff_skill.key)
                    buff_skill.last_used = current_time
                    self._log("使用buff技能")
                except Exception as e:
                    logger.error(f"执行buff错误: {e}")

    def _update_battle_state(self):
        """更新战斗状态"""
        if not self.target_list:
            self.battle_state = BattleState.SEARCHING
        elif self.current_target:
            self.battle_state = BattleState.FIGHTING
        else:
            self.battle_state = BattleState.ENGAGING

    def _calculate_threat_level(self, monster) -> int:
        """计算威胁等级"""
        # 基于怪物等级、血量、距离等计算威胁等级
        level = monster.get('level', 1)
        hp = monster.get('hp_percent', 100)
        distance = monster.get('distance', 999)
        
        threat = level
        if hp > 80:
            threat += 2
        if distance < 10:
            threat += 1
        
        return min(threat, 10)  # 最大威胁等级为10

    def _get_player_hp_percent(self) -> float:
        """获取玩家血量百分比"""
        try:
            # 从内存读取器获取玩家血量
            return self.memory.get_player_hp_percent()
        except:
            return 100.0  # 默认满血

    def _should_buff(self) -> bool:
        """判断是否需要buff"""
        # 简单的buff判断逻辑
        buff_skill = self.skills.get("buff")
        if not buff_skill:
            return False
        
        current_time = time.time()
        return current_time - buff_skill.last_used >= self.buff_duration

    def _log(self, message: str):
        """记录日志"""
        logger.info(message)
        if self.game_log_callback:
            self.game_log_callback(message)

    def set_log_callback(self, callback):
        """设置游戏日志回调"""
        self.game_log_callback = callback

    def get_status(self) -> Dict:
        """获取AI状态"""
        return {
            "is_active": self.is_active,
            "battle_state": self.battle_state.value,
            "current_target": self.current_target.name if self.current_target else "无",
            "target_count": len(self.target_list),
            "skills_ready": sum(1 for s in self.skills.values() 
                              if time.time() - s.last_used >= s.cooldown)
        }

    def update_settings(self, settings: Dict):
        """更新AI设置"""
        if "attack_range" in settings:
            self.attack_range = settings["attack_range"]
        if "heal_threshold" in settings:
            self.heal_hp_threshold = settings["heal_threshold"]
        if "target_algorithm" in settings:
            self.target_selection_algorithm = settings["target_algorithm"]
        
        self._log("AI设置已更新")