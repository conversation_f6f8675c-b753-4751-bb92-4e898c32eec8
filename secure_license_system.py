"""
安全许可证系统
集成服务器端验证、反调试、动态加密和代码保护
"""

import os
import sys
import time
import json
import random
import logging
import threading
import requests
from datetime import datetime, timedelta

# 导入必要的模块
try:
    from license_system import HardwareInfo, EnhancedLicenseChecker
    from anti_debug import AntiDebug, AntiTampering
    from dynamic_encryption import DynamicEncryptionSystem
except ImportError as e:
    print(f"错误：无法导入必要的模块: {str(e)}")
    print("请确保license_system.py, anti_debug.py和dynamic_encryption.py文件存在")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('secure_license.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('secure_license')


class SecureLicenseSystem:
    """集成安全许可证系统"""
    
    def __init__(self, server_url=None, update_interval=86400):
        """
        初始化安全许可证系统
        
        参数:
            server_url: 许可证服务器URL
            update_interval: 加密算法更新间隔（秒）
        """
        self.server_url = server_url or "https://api.yourdomain.com/validate"
        self.backup_urls = [
            "https://backup1.yourdomain.com/validate",
            "https://backup2.yourdomain.com/validate"
        ]
        
        # 初始化组件
        self.hardware_info = HardwareInfo()
        self.license_checker = EnhancedLicenseChecker()
        self.anti_debug = AntiDebug()
        self.anti_tampering = AntiTampering()
        self.encryption_system = DynamicEncryptionSystem(update_interval=update_interval)
        
        # 设置回调函数
        self.anti_debug.set_debug_callback(self._debug_detected)
        self.anti_tampering.set_tampering_callback(self._tampering_detected)
        
        # 状态变量
        self.is_activated = False
        self.activation_message = "未激活"
        self.expiry_date = None
        self.last_online_check = None
        self.shutdown_requested = False
        
        # 保护关键文件
        self._protect_critical_files()
    
    def _debug_detected(self, technique, details):
        """调试检测回调"""
        logger.warning(f"检测到调试尝试! 技术: {technique}, 详情: {details}")
        
        # 记录调试尝试
        try:
            self._report_security_event("debug_detected", {
                "technique": technique,
                "details": details,
                "timestamp": datetime.now().isoformat()
            })
        except Exception as e:
            logger.error(f"报告安全事件出错: {str(e)}")
        
        # 根据调试检测次数采取不同措施
        if hasattr(self.anti_debug, 'debug_counter') and self.anti_debug.debug_counter >= 3:
            logger.critical("检测到多次调试尝试，正在关闭程序...")
            self._emergency_shutdown("检测到多次调试尝试")
    
    def _tampering_detected(self, component, details):
        """篡改检测回调"""
        logger.warning(f"检测到篡改尝试! 组件: {component}, 详情: {details}")
        
        # 记录篡改尝试
        try:
            self._report_security_event("tampering_detected", {
                "component": component,
                "details": details,
                "timestamp": datetime.now().isoformat()
            })
        except Exception as e:
            logger.error(f"报告安全事件出错: {str(e)}")
        
        # 篡改检测立即关闭程序
        logger.critical("检测到篡改尝试，正在关闭程序...")
        self._emergency_shutdown("检测到篡改尝试")
    
    def _protect_critical_files(self):
        """保护关键文件"""
        try:
            critical_files = [
                os.path.abspath(__file__),
                os.path.abspath(sys.modules.get("license_system", {}).__file__),
                os.path.abspath(sys.modules.get("anti_debug", {}).__file__),
                os.path.abspath(sys.modules.get("dynamic_encryption", {}).__file__)
            ]
            
            for file_path in critical_files:
                if file_path and os.path.exists(file_path):
                    self.anti_tampering.protect_file(file_path)
                    logger.info(f"已保护文件: {file_path}")
        except Exception as e:
            logger.error(f"保护关键文件出错: {str(e)}")
    
    def _report_security_event(self, event_type, event_data):
        """向服务器报告安全事件"""
        if not self.server_url:
            return
        
        try:
            # 准备事件数据
            machine_id = self.hardware_info.generate_machine_id()
            report_data = {
                "event_type": event_type,
                "machine_id": machine_id,
                "event_data": event_data,
                "app_version": "1.0.0"  # 替换为实际版本号
            }
            
            # 加密事件数据
            try:
                encrypted_data = self.encryption_system.encrypt(json.dumps(report_data))
                
                # 发送到服务器
                response = requests.post(
                    f"{self.server_url}/report_event",
                    data={"encrypted_data": encrypted_data.hex()},
                    timeout=5
                )
                
                if response.status_code == 200:
                    logger.info(f"安全事件已报告: {event_type}")
                else:
                    logger.warning(f"报告安全事件失败: {response.status_code}")
            except Exception as e:
                logger.error(f"加密或发送安全事件数据出错: {str(e)}")
        except Exception as e:
            logger.error(f"报告安全事件出错: {str(e)}")
    
    def _emergency_shutdown(self, reason):
        """紧急关闭程序"""
        if self.shutdown_requested:
            return
        
        self.shutdown_requested = True
        logger.critical(f"紧急关闭: {reason}")
        
        # 清除敏感数据
        try:
            # 清除内存中的敏感信息
            self.encryption_system = None
            self.license_checker = None
            
            # 强制垃圾回收
            import gc
            gc.collect()
            
            # 延迟一小段时间后退出
            time.sleep(1)
            
            # 退出程序
            os._exit(1)
        except Exception as e:
            logger.error(f"紧急关闭出错: {str(e)}")
            os._exit(1)
    
    def check_activation(self):
        """检查激活状态"""
        try:
            # 首先检查是否有调试或篡改迹象
            if self.anti_debug.run_all_checks():
                return False, "检测到调试尝试", None
            
            if not self.anti_tampering.check_file_integrity():
                return False, "检测到文件篡改", None
            
            # 检查激活状态
            is_activated, message, expiry_date = self.license_checker.check_activation()
            
            self.is_activated = is_activated
            self.activation_message = message
            self.expiry_date = expiry_date
            
            return is_activated, message, expiry_date
        except Exception as e:
            logger.error(f"检查激活状态出错: {str(e)}")
            return False, f"检查激活状态出错: {str(e)}", None
    
    def activate(self, activation_key):
        """激活软件"""
        try:
            # 检查是否有调试或篡改迹象
            if self.anti_debug.run_all_checks():
                return False, "检测到调试尝试，激活失败"
            
            if not self.anti_tampering.check_file_integrity():
                return False, "检测到文件篡改，激活失败"
            
            # 获取机器ID
            machine_id = self.hardware_info.generate_machine_id()
            
            # 激活软件
            is_success, message = self.license_checker.activate(activation_key, machine_id)
            
            if is_success:
                # 更新状态
                self.is_activated = True
                self.activation_message = message
                
                # 重新检查以获取过期日期
                _, _, self.expiry_date = self.license_checker.check_activation()
                
                # 报告激活事件
                self._report_security_event("activation_success", {
                    "activation_key": activation_key,
                    "machine_id": machine_id,
                    "expiry_date": self.expiry_date.isoformat() if self.expiry_date else None
                })
            else:
                # 报告激活失败事件
                self._report_security_event("activation_failure", {
                    "activation_key": activation_key,
                    "machine_id": machine_id,
                    "message": message
                })
            
            return is_success, message
        except Exception as e:
            logger.error(f"激活软件出错: {str(e)}")
            return False, f"激活软件出错: {str(e)}"
    
    def periodic_check(self):
        """定期检查激活状态"""
        try:
            # 检查是否有调试或篡改迹象
            if self.anti_debug.run_all_checks():
                return False, "检测到调试尝试", None
            
            if not self.anti_tampering.check_file_integrity():
                return False, "检测到文件篡改", None
            
            # 检查激活状态
            is_activated, message, expiry_date = self.license_checker.periodic_check()
            
            # 更新状态
            self.is_activated = is_activated
            self.activation_message = message
            self.expiry_date = expiry_date
            
            # 更新上次在线检查时间
            if is_activated:
                self.last_online_check = datetime.now()
            
            return is_activated, message, expiry_date
        except Exception as e:
            logger.error(f"定期检查出错: {str(e)}")
            return False, f"定期检查出错: {str(e)}", None
    
    def start_protection(self):
        """启动所有保护机制"""
        try:
            # 启动反调试监控
            self.anti_debug.start_monitoring()
            logger.info("反调试监控已启动")
            
            # 启动反篡改监控
            self.anti_tampering.start_monitoring()
            logger.info("反篡改监控已启动")
            
            # 启动动态加密更新
            self.encryption_system.start_auto_update()
            logger.info("动态加密系统已启动")
            
            # 启动定期激活检查
            self._start_periodic_activation_check()
            logger.info("定期激活检查已启动")
            
            return True
        except Exception as e:
            logger.error(f"启动保护机制出错: {str(e)}")
            return False
    
    def _start_periodic_activation_check(self, interval_minutes=30):
        """启动定期激活检查线程"""
        def check_thread():
            while not self.shutdown_requested:
                try:
                    # 执行定期检查
                    is_activated, message, expiry_date = self.periodic_check()
                    
                    if not is_activated:
                        logger.warning(f"激活检查失败: {message}")
                        
                        # 如果连续多次检查失败，可以采取措施
                        if self.last_online_check and (datetime.now() - self.last_online_check).days > 7:
                            logger.critical("超过7天未能验证激活状态，正在关闭程序...")
                            self._emergency_shutdown("激活验证失败")
                    
                    # 随机睡眠时间，使检查更难预测
                    sleep_time = interval_minutes * 60 + random.randint(-300, 300)
                    time.sleep(max(60, sleep_time))  # 至少60秒
                except Exception as e:
                    logger.error(f"定期检查线程出错: {str(e)}")
                    time.sleep(60)  # 出错后短暂暂停
        
        # 创建并启动检查线程
        thread = threading.Thread(target=check_thread, daemon=True)
        thread.start()
        
        return thread
    
    def encrypt_data(self, data):
        """使用动态加密系统加密数据"""
        try:
            return self.encryption_system.encrypt(data)
        except Exception as e:
            logger.error(f"加密数据出错: {str(e)}")
            # 返回一个空的加密结果，避免程序崩溃
            return b''
    
    def decrypt_data(self, encrypted_data):
        """使用动态加密系统解密数据"""
        try:
            return self.encryption_system.decrypt(encrypted_data)
        except Exception as e:
            logger.error(f"解密数据出错: {str(e)}")
            # 返回原始数据，避免程序崩溃
            return ""


# 示例：如何使用安全许可证系统
if __name__ == "__main__":
    # 创建安全许可证系统
    license_system = SecureLicenseSystem()
    
    # 启动保护机制
    license_system.start_protection()
    
    # 检查激活状态
    is_activated, message, expiry_date = license_system.check_activation()
    
    print(f"激活状态: {'已激活' if is_activated else '未激活'}")
    print(f"消息: {message}")
    
    if expiry_date:
        print(f"过期时间: {expiry_date.strftime('%Y-%m-%d')}")
    
    # 保持程序运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("程序已退出")
