#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试输入模拟器的按键发送功能
"""

import sys
import os
import time
import logging

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_keyboard_input():
    """测试键盘输入功能"""
    print("=" * 50)
    print("测试键盘输入功能")
    print("=" * 50)
    
    try:
        from input_simulator import InputSimulator
        
        # 创建输入模拟器
        input_sim = InputSimulator()
        print("✓ InputSimulator创建成功")
        
        print("\\n准备测试按键发送...")
        print("请在5秒内切换到一个文本编辑器（如记事本）来观察按键效果")
        
        # 倒计时
        for i in range(5, 0, -1):
            print(f"倒计时: {i}秒")
            time.sleep(1)
        
        print("\\n开始发送测试按键...")
        
        # 测试基本按键
        test_keys = ['1', '2', '3', 'a', 'b', 'c']
        
        for key in test_keys:
            print(f"发送按键: {key}")
            success = input_sim.press_key(key)
            if success:
                print(f"  ✓ {key} 发送成功")
            else:
                print(f"  ❌ {key} 发送失败")
            time.sleep(0.5)
        
        print("\\n测试组合键...")
        success = input_sim.press_key_combo(['alt', '1'])
        if success:
            print("  ✓ Alt+1 发送成功")
        else:
            print("  ❌ Alt+1 发送失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_win32_input():
    """测试使用Win32 API发送按键"""
    print("\\n" + "=" * 50)
    print("测试Win32 API按键发送")
    print("=" * 50)
    
    try:
        import win32api
        import win32con
        import time
        
        print("准备使用Win32 API发送按键...")
        print("请在3秒内切换到目标窗口")
        
        for i in range(3, 0, -1):
            print(f"倒计时: {i}秒")
            time.sleep(1)
        
        print("\\n发送按键...")
        
        # 测试发送数字键1
        # VK_1 = 0x31
        win32api.keybd_event(0x31, 0, 0, 0)  # 按下
        time.sleep(0.05)
        win32api.keybd_event(0x31, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放
        print("✓ 发送数字键1")
        
        time.sleep(0.5)
        
        # 测试发送数字键2
        win32api.keybd_event(0x32, 0, 0, 0)  # 按下
        time.sleep(0.05)
        win32api.keybd_event(0x32, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放
        print("✓ 发送数字键2")
        
        return True
        
    except Exception as e:
        print(f"❌ Win32 API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试输入模拟器功能...")
    print("\\n注意: 请确保有一个文本编辑器（如记事本）打开以观察按键效果")
    
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING)
    
    success_count = 0
    total_tests = 2
    
    # 测试1: keyboard库输入
    if test_keyboard_input():
        success_count += 1
    
    # 测试2: Win32 API输入
    if test_win32_input():
        success_count += 1
    
    # 总结
    print("\\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"通过测试: {success_count}/{total_tests}")
    
    if success_count > 0:
        print("\\n建议:")
        if success_count == 1:
            print("- 部分输入方法工作正常")
            print("- 可能需要切换到更可靠的输入方法")
        else:
            print("- 输入模拟器基本功能正常")
            print("- 如果游戏中仍无响应，可能是游戏窗口焦点或权限问题")
        
        print("\\n可能的解决方案:")
        print("1. 确保游戏窗口处于前台并有焦点")
        print("2. 以管理员权限运行程序")
        print("3. 检查游戏是否有输入保护机制")
        print("4. 尝试使用不同的输入方法（Win32 API vs keyboard库）")
        
        return True
    else:
        print("❌ 所有输入测试都失败了")
        print("需要检查系统配置和权限设置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)