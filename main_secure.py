"""
游戏增强工具 - 安全增强版
集成了增强的许可证系统、在线验证、反调试、动态加密和代码保护
"""

import os
import sys
import time
import logging
import threading
from PyQt6.QtWidgets import QApplication, QMessageBox, QInputDialog, QLineEdit
from PyQt6.QtCore import QTimer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('main_secure.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('main_secure')

# 导入安全许可证系统
try:
    from secure_license_system import SecureLicenseSystem
except ImportError as e:
    logger.error(f"无法导入安全许可证系统: {str(e)}")
    if QApplication.instance() is None:
        app = QApplication(sys.argv)
    QMessageBox.critical(None, "错误", "无法加载安全许可证系统，程序将退出。")
    sys.exit(1)

# 导入主程序
try:
    from 最新 import GameMemoryTool
except ImportError as e:
    logger.error(f"无法导入主程序模块: {str(e)}")
    if QApplication.instance() is None:
        app = QApplication(sys.argv)
    QMessageBox.critical(None, "错误", "无法加载主程序，程序将退出。")
    sys.exit(1)


class SecureGameLauncher:
    """安全游戏启动器"""
    
    def __init__(self):
        """初始化安全游戏启动器"""
        self.app = QApplication(sys.argv) if QApplication.instance() is None else QApplication.instance()
        self.license_system = SecureLicenseSystem()
        self.main_window = None
        self.check_timer = None
        
        # 启动安全保护
        self.license_system.start_protection()
        logger.info("安全保护系统已启动")
    
    def check_activation(self):
        """检查激活状态"""
        is_activated, message, expiry_date = self.license_system.check_activation()
        
        if not is_activated:
            logger.warning(f"许可证验证失败: {message}")
            
            # 询问用户是否要激活
            result = QMessageBox.question(
                None, 
                "许可证验证", 
                f"许可证验证失败: {message}\n\n是否现在激活软件？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if result == QMessageBox.StandardButton.Yes:
                self.activate_software()
                # 重新检查激活状态
                return self.license_system.check_activation()
            else:
                return False, message, None
        
        # 显示激活成功信息
        if expiry_date:
            logger.info(f"许可证验证成功，有效期至: {expiry_date.strftime('%Y-%m-%d')}")
        else:
            logger.info("许可证验证成功")
        
        return is_activated, message, expiry_date
    
    def activate_software(self):
        """激活软件"""
        # 获取激活码
        activation_key, ok = QInputDialog.getText(
            None, 
            "软件激活", 
            "请输入激活码:", 
            QLineEdit.EchoMode.Normal
        )
        
        if not ok or not activation_key:
            logger.warning("用户取消了激活")
            return False, "用户取消了激活"
        
        # 尝试激活
        is_success, message = self.license_system.activate(activation_key)
        
        if is_success:
            QMessageBox.information(None, "激活成功", f"软件已成功激活: {message}")
            logger.info(f"软件激活成功: {message}")
        else:
            QMessageBox.critical(None, "激活失败", f"激活失败: {message}")
            logger.error(f"软件激活失败: {message}")
        
        return is_success, message
    
    def setup_periodic_check(self, interval_minutes=30):
        """设置定期许可证检查"""
        def check_license():
            try:
                is_activated, message, expiry_date = self.license_system.periodic_check()
                
                if not is_activated and self.main_window:
                    logger.warning(f"定期许可证检查失败: {message}")
                    QMessageBox.critical(
                        self.main_window, 
                        "许可证错误", 
                        f"许可证验证失败: {message}\n程序将退出。"
                    )
                    self.shutdown()
            except Exception as e:
                logger.error(f"定期许可证检查出错: {str(e)}")
        
        # 创建定时器
        self.check_timer = QTimer()
        self.check_timer.timeout.connect(check_license)
        
        # 启动定时器（毫秒）
        check_interval = interval_minutes * 60 * 1000
        self.check_timer.start(check_interval)
        
        logger.info(f"已设置定期许可证检查，间隔: {interval_minutes}分钟")
        return self.check_timer
    
    def launch(self):
        """启动游戏增强工具"""
        try:
            # 检查激活状态
            is_activated, message, expiry_date = self.check_activation()
            
            if not is_activated:
                logger.error(f"许可证验证失败，无法启动: {message}")
                QMessageBox.critical(None, "许可证错误", f"许可证验证失败: {message}\n程序将退出。")
                return 1
            
            # 创建主窗口
            self.main_window = GameMemoryTool()
            
            # 设置定期许可证检查
            self.setup_periodic_check(interval_minutes=30)
            
            # 显示主窗口
            self.main_window.show()
            
            # 显示激活信息
            if expiry_date:
                status_message = f"软件已激活，有效期至: {expiry_date.strftime('%Y-%m-%d')}"
            else:
                status_message = "软件已激活"
            
            # 如果主窗口有状态栏，显示激活信息
            if hasattr(self.main_window, 'statusBar'):
                self.main_window.statusBar().showMessage(status_message)
            
            # 运行应用
            return self.app.exec()
            
        except Exception as e:
            logger.error(f"启动主程序时出错: {str(e)}")
            QMessageBox.critical(None, "启动错误", f"启动主程序时出错: {str(e)}\n程序将退出。")
            return 1
    
    def shutdown(self):
        """安全关闭程序"""
        try:
            # 停止定时器
            if self.check_timer:
                self.check_timer.stop()
            
            # 关闭主窗口
            if self.main_window:
                self.main_window.close()
            
            # 退出应用
            self.app.quit()
        except Exception as e:
            logger.error(f"关闭程序时出错: {str(e)}")
            sys.exit(1)


def main():
    """安全增强版主函数"""
    try:
        # 创建安全启动器
        launcher = SecureGameLauncher()
        
        # 启动程序
        return launcher.launch()
        
    except Exception as e:
        logger.error(f"程序启动出错: {str(e)}")
        if QApplication.instance() is None:
            app = QApplication(sys.argv)
        QMessageBox.critical(None, "启动错误", f"程序启动出错: {str(e)}\n程序将退出。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
