#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI主窗口模块 - 创建并管理自动战斗工具的图形界面
"""

import os
import sys
import logging
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QLineEdit, QSlider, QGroupBox, QCheckBox, QTableWidget,
    QTableWidgetItem, QTabWidget, QSpinBox, QTextEdit, QMessageBox,
    QGridLayout, QSplitter, QFrame, QDialog, QDialogButtonBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, pyqtSlot
from PyQt6.QtGui import QFont, QColor, QPixmap, QIcon

# 设置日志
logger = logging.getLogger('aion_ui')

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self, memory_reader, battle_manager, auto_combat=None):
        """初始化主窗口"""
        super().__init__()
        
        self.memory = memory_reader
        self.battle_manager = battle_manager
        self.auto_combat = auto_combat
        
        # 设置窗口标题和大小
        self.setWindowTitle("Aion自动战斗工具")
        self.setMinimumSize(800, 600)
        
        # 创建UI组件
        self.init_ui()
        
        # 创建状态更新定时器
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self.update_status_display)
        self.status_timer.start(1000)  # 每1秒更新一次
        
        # 创建进程列表更新定时器
        self.process_timer = QTimer(self)
        self.process_timer.timeout.connect(self.refresh_process_list)
        self.process_timer.start(5000)  # 每5秒更新一次
        
        # 初始化状态
        self.refresh_process_list()
        
        logger.info("主窗口已初始化")
        
        # 应用现代化高科技风格的UI样式
        self.apply_dark_theme()
    
    def apply_dark_theme(self):
        """应用暗色主题和高科技风格"""
        # 设置应用程序样式表
        self.setStyleSheet("""
            QMainWindow, QDialog {
                background-color: #1e1e2e;
                color: #cdd6f4;
            }
            QWidget {
                background-color: #1e1e2e;
                color: #cdd6f4;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QTabWidget::pane {
                border: 1px solid #313244;
                background-color: #1e1e2e;
                border-radius: 4px;
            }
            QTabBar::tab {
                background-color: #313244;
                color: #cdd6f4;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #45475a;
                color: #f5e0dc;
                border-bottom: 2px solid #89b4fa;
            }
            QTabBar::tab:hover:!selected {
                background-color: #45475a;
            }
            QPushButton {
                background-color: #45475a;
                color: #cdd6f4;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #585b70;
            }
            QPushButton:pressed {
                background-color: #313244;
            }
            QPushButton:disabled {
                background-color: #313244;
                color: #6c7086;
            }
            QLineEdit, QComboBox {
                background-color: #313244;
                color: #cdd6f4;
                border: 1px solid #45475a;
                border-radius: 4px;
                padding: 5px;
            }
            QLineEdit:focus, QComboBox:focus {
                border: 1px solid #89b4fa;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QCheckBox {
                color: #cdd6f4;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 1px solid #45475a;
            }
            QCheckBox::indicator:unchecked {
                background-color: #313244;
            }
            QCheckBox::indicator:checked {
                background-color: #89b4fa;
            }
            QGroupBox {
                border: 1px solid #313244;
                border-radius: 4px;
                margin-top: 1.5ex;
                padding-top: 1ex;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                color: #89b4fa;
            }
            QTextEdit {
                background-color: #313244;
                color: #cdd6f4;
                border: 1px solid #45475a;
                border-radius: 4px;
            }
        """)
        
        # 设置窗口标题和图标
        self.setWindowTitle("Aion 4.5 4.6 5.8 7.7通用")
        
        # 设置应用图标
        icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "icon.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
    def init_ui(self):
        """初始化UI组件"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建顶部控制区域
        top_control_layout = QHBoxLayout()
        
        # 创建进程选择组
        process_group = QGroupBox("游戏进程")
        process_layout = QVBoxLayout(process_group)
        
        # 进程下拉框和刷新按钮
        process_row = QHBoxLayout()
        self.process_combo = QComboBox()
        self.process_combo.setMinimumWidth(200)
        process_row.addWidget(self.process_combo)
        
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.clicked.connect(self.refresh_process_list)
        process_row.addWidget(self.refresh_button)
        
        # 添加自动搜索按钮
        auto_search_button = QPushButton("自动搜索")
        auto_search_button.setToolTip("自动搜索并连接到Aion游戏进程")
        auto_search_button.clicked.connect(self.auto_search_game)
        process_row.addWidget(auto_search_button)
        
        process_layout.addLayout(process_row)
        
        # 连接按钮
        self.connect_button = QPushButton("连接到游戏")
        self.connect_button.clicked.connect(self.connect_to_game)
        process_layout.addWidget(self.connect_button)
        
        top_control_layout.addWidget(process_group)
        
        # 创建副本专用组
        battle_group = QGroupBox("副本专用")
        battle_layout = QVBoxLayout(battle_group)
        
        # 开始/停止按钮
        self.start_button = QPushButton("开始副本辅助")
        self.start_button.setEnabled(False)  # 初始时禁用
        self.start_button.clicked.connect(self.toggle_auto_battle)
        self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
        battle_layout.addWidget(self.start_button)
        
        top_control_layout.addWidget(battle_group)
        
        # 添加顶部控制区域到主布局
        main_layout.addLayout(top_control_layout)
        
        # 创建功能选项卡
        self.tabs = QTabWidget()
        
        # 创建基础功能选项卡
        self.create_basic_tab()
        
        # 创建攻击功能选项卡
        self.create_attack_tab()
        
        # 创建移动功能选项卡
        self.create_movement_tab()
        
        # 创建视觉功能选项卡
        self.create_visual_tab()
        
        # 创建战斗配置选项卡
        self.create_combat_config_tab()
        
        # 添加选项卡到主布局
        main_layout.addWidget(self.tabs)
        
        # 创建日志区域
        self.create_log_area(main_layout)
        
        # 创建状态栏
        self.statusBar().showMessage("准备就绪")
        
        logger.info("UI组件已初始化")
    
    def create_basic_tab(self):
        """创建基础功能选项卡"""
        basic_tab = QWidget()
        basic_layout = QVBoxLayout(basic_tab)
        
        # 创建技能加速控制组
        attack_speed_group = QGroupBox("技能加速")
        attack_speed_layout = QHBoxLayout(attack_speed_group)
        
        self.attack_speed_check = QCheckBox("启用技能加速")
        self.attack_speed_check.setEnabled(False)
        self.attack_speed_check.stateChanged.connect(self.toggle_attack_speed)
        attack_speed_layout.addWidget(self.attack_speed_check)
        
        self.attack_speed_value = QLineEdit("200")
        self.attack_speed_value.setEnabled(False)
        self.attack_speed_value.setMaximumWidth(60)
        attack_speed_layout.addWidget(self.attack_speed_value)
        
        basic_layout.addWidget(attack_speed_group)
        
        # 创建移动加速控制组
        move_speed_group = QGroupBox("移动加速")
        move_speed_layout = QHBoxLayout(move_speed_group)
        
        self.move_speed_check = QCheckBox("启用移动加速")
        self.move_speed_check.setEnabled(False)
        self.move_speed_check.stateChanged.connect(self.toggle_move_speed)
        move_speed_layout.addWidget(self.move_speed_check)
        
        self.move_speed_value = QLineEdit("2.0")
        self.move_speed_value.setEnabled(False)
        self.move_speed_value.setMaximumWidth(60)
        move_speed_layout.addWidget(self.move_speed_value)
        
        basic_layout.addWidget(move_speed_group)
        
        # 添加基础功能选项卡
        self.tabs.addTab(basic_tab, "基础功能")
    
    def create_attack_tab(self):
        """创建攻击功能选项卡"""
        attack_tab = QWidget()
        attack_layout = QVBoxLayout(attack_tab)
        
        # 创建攻击距离控制组
        attack_range_group = QGroupBox("攻击距离")
        attack_range_layout = QHBoxLayout(attack_range_group)
        
        self.attack_range_check = QCheckBox("启用攻击距离修改")
        self.attack_range_check.setEnabled(False)
        self.attack_range_check.stateChanged.connect(self.toggle_attack_range)
        attack_range_layout.addWidget(self.attack_range_check)
        
        self.attack_range_value = QLineEdit("10.0")
        self.attack_range_value.setEnabled(False)
        self.attack_range_value.setMaximumWidth(60)
        attack_range_layout.addWidget(self.attack_range_value)
        
        attack_layout.addWidget(attack_range_group)
        
        # 创建反隐控制组
        stealth_group = QGroupBox("反隐功能")
        stealth_layout = QHBoxLayout(stealth_group)
        
        self.stealth_check = QCheckBox("启用反隐")
        self.stealth_check.setEnabled(False)
        self.stealth_check.stateChanged.connect(self.toggle_stealth)
        stealth_layout.addWidget(self.stealth_check)
        
        attack_layout.addWidget(stealth_group)
        
        # 添加攻击相关功能选项卡
        self.tabs.addTab(attack_tab, "攻击功能")
    
    def create_movement_tab(self):
        """创建移动功能选项卡"""
        movement_tab = QWidget()
        movement_layout = QVBoxLayout(movement_tab)
        
        # 创建锁空控制组
        air_lock_group = QGroupBox("锁空功能")
        air_lock_layout = QHBoxLayout(air_lock_group)
        
        self.air_lock_check = QCheckBox("启用锁空")
        self.air_lock_check.setEnabled(False)
        self.air_lock_check.stateChanged.connect(self.toggle_air_lock)
        air_lock_layout.addWidget(self.air_lock_check)
        
        movement_layout.addWidget(air_lock_group)
        
        # 创建飞天控制组
        fly_group = QGroupBox("飞天功能")
        fly_layout = QVBoxLayout(fly_group)
        
        self.fly_check = QCheckBox("启用飞天")
        self.fly_check.setEnabled(False)
        self.fly_check.stateChanged.connect(self.toggle_fly)
        fly_layout.addWidget(self.fly_check)
        
        fly_controls = QHBoxLayout()
        
        self.fly_value = QLineEdit("10.0")
        self.fly_value.setEnabled(False)
        self.fly_value.setMaximumWidth(60)
        fly_controls.addWidget(self.fly_value)
        
        self.fly_up_btn = QPushButton("上升")
        self.fly_up_btn.setEnabled(False)
        self.fly_up_btn.clicked.connect(self.fly_up)
        fly_controls.addWidget(self.fly_up_btn)
        
        self.fly_down_btn = QPushButton("下降")
        self.fly_down_btn.setEnabled(False)
        self.fly_down_btn.clicked.connect(self.fly_down)
        fly_controls.addWidget(self.fly_down_btn)
        
        fly_layout.addLayout(fly_controls)
        
        movement_layout.addWidget(fly_group)
        
        # 添加移动相关功能选项卡
        self.tabs.addTab(movement_tab, "移动功能")
    
    def create_visual_tab(self):
        """创建视觉功能选项卡"""
        visual_tab = QWidget()
        visual_layout = QVBoxLayout(visual_tab)
        
        # 创建视野扩展控制组
        view_range_group = QGroupBox("视野扩展")
        view_range_layout = QHBoxLayout(view_range_group)
        
        self.view_range_check = QCheckBox("启用视野扩展")
        self.view_range_check.setEnabled(False)
        self.view_range_check.stateChanged.connect(self.toggle_view_range)
        view_range_layout.addWidget(self.view_range_check)
        
        self.view_range_value = QLineEdit("100.0")
        self.view_range_value.setEnabled(False)
        self.view_range_value.setMaximumWidth(60)
        view_range_layout.addWidget(self.view_range_value)
        
        visual_layout.addWidget(view_range_group)
        
        # 创建显血控制组
        show_hp_group = QGroupBox("显血功能")
        show_hp_layout = QHBoxLayout(show_hp_group)
        
        self.show_hp_check = QCheckBox("启用显血")
        self.show_hp_check.setEnabled(False)
        self.show_hp_check.stateChanged.connect(self.toggle_show_hp)
        show_hp_layout.addWidget(self.show_hp_check)
        
        visual_layout.addWidget(show_hp_group)
        
        # 创建地图扩展控制组
        map_extend_group = QGroupBox("地图扩展")
        map_extend_layout = QHBoxLayout(map_extend_group)
        
        self.map_extend_check = QCheckBox("启用地图扩展")
        self.map_extend_check.setEnabled(False)
        self.map_extend_check.stateChanged.connect(self.toggle_map_extend)
        map_extend_layout.addWidget(self.map_extend_check)
        
        self.map_extend_value = QLineEdit("200.0")
        self.map_extend_value.setEnabled(False)
        self.map_extend_value.setMaximumWidth(60)
        map_extend_layout.addWidget(self.map_extend_value)
        
        visual_layout.addWidget(map_extend_group)
        
        # 添加视觉相关功能选项卡
        self.tabs.addTab(visual_tab, "视觉功能")
    
    def create_combat_config_tab(self):
        """创建战斗配置选项卡"""
        combat_config_tab = QWidget()
        combat_config_layout = QVBoxLayout(combat_config_tab)
        
        # 战斗系统配置组
        battle_system_group = QGroupBox("战斗系统配置")
        battle_system_layout = QGridLayout(battle_system_group)
        
        # 攻击范围设置
        battle_system_layout.addWidget(QLabel("攻击范围:"), 0, 0)
        self.combat_attack_range = QLineEdit("15.0")
        self.combat_attack_range.setMaximumWidth(80)
        battle_system_layout.addWidget(self.combat_attack_range, 0, 1)
        battle_system_layout.addWidget(QLabel("米"), 0, 2)
        
        # 搜索范围设置
        battle_system_layout.addWidget(QLabel("搜索范围:"), 1, 0)
        self.combat_search_range = QLineEdit("25.0")
        self.combat_search_range.setMaximumWidth(80)
        battle_system_layout.addWidget(self.combat_search_range, 1, 1)
        battle_system_layout.addWidget(QLabel("米"), 1, 2)
        
        # 撤退血量阈值
        battle_system_layout.addWidget(QLabel("撤退血量:"), 2, 0)
        self.combat_retreat_hp = QLineEdit("20.0")
        self.combat_retreat_hp.setMaximumWidth(80)
        battle_system_layout.addWidget(self.combat_retreat_hp, 2, 1)
        battle_system_layout.addWidget(QLabel("%"), 2, 2)
        
        # 目标选择算法
        battle_system_layout.addWidget(QLabel("目标选择:"), 3, 0)
        self.target_algorithm = QComboBox()
        self.target_algorithm.addItems(["仇恨最高", "距离最近", "血量最少"])
        battle_system_layout.addWidget(self.target_algorithm, 3, 1)
        
        combat_config_layout.addWidget(battle_system_group)
        
        # 拾取系统配置组
        loot_system_group = QGroupBox("拾取系统配置")
        loot_system_layout = QGridLayout(loot_system_group)
        
        # 自动拾取开关
        self.auto_loot_enabled = QCheckBox("启用自动拾取")
        self.auto_loot_enabled.setChecked(True)
        loot_system_layout.addWidget(self.auto_loot_enabled, 0, 0, 1, 3)
        
        # 拾取范围设置
        loot_system_layout.addWidget(QLabel("拾取范围:"), 1, 0)
        self.loot_range = QLineEdit("10.0")
        self.loot_range.setMaximumWidth(80)
        loot_system_layout.addWidget(self.loot_range, 1, 1)
        loot_system_layout.addWidget(QLabel("米"), 1, 2)
        
        # 拾取延迟设置
        loot_system_layout.addWidget(QLabel("拾取延迟:"), 2, 0)
        self.loot_delay = QLineEdit("0.5")
        self.loot_delay.setMaximumWidth(80)
        loot_system_layout.addWidget(self.loot_delay, 2, 1)
        loot_system_layout.addWidget(QLabel("秒"), 2, 2)
        
        # 拾取品质过滤
        loot_filter_group = QGroupBox("拾取品质过滤")
        loot_filter_layout = QVBoxLayout(loot_filter_group)
        
        self.loot_legendary = QCheckBox("传说物品")
        self.loot_legendary.setChecked(True)
        loot_filter_layout.addWidget(self.loot_legendary)
        
        self.loot_epic = QCheckBox("史诗物品")
        self.loot_epic.setChecked(True)
        loot_filter_layout.addWidget(self.loot_epic)
        
        self.loot_rare = QCheckBox("稀有物品")
        self.loot_rare.setChecked(True)
        loot_filter_layout.addWidget(self.loot_rare)
        
        self.loot_uncommon = QCheckBox("优秀物品")
        self.loot_uncommon.setChecked(True)
        loot_filter_layout.addWidget(self.loot_uncommon)
        
        self.loot_common = QCheckBox("普通物品")
        self.loot_common.setChecked(False)
        loot_filter_layout.addWidget(self.loot_common)
        
        loot_system_layout.addWidget(loot_filter_group, 3, 0, 1, 3)
        
        combat_config_layout.addWidget(loot_system_group)
        
        # 辅助功能配置组
        assist_group = QGroupBox("辅助功能配置")
        assist_layout = QVBoxLayout(assist_group)
        
        self.use_potions = QCheckBox("自动使用药品")
        self.use_potions.setChecked(True)
        assist_layout.addWidget(self.use_potions)
        
        self.auto_buff = QCheckBox("自动释放Buff")
        self.auto_buff.setChecked(True)
        assist_layout.addWidget(self.auto_buff)
        
        combat_config_layout.addWidget(assist_group)
        
        # 配置应用按钮
        apply_config_btn = QPushButton("应用配置")
        apply_config_btn.clicked.connect(self.apply_combat_config)
        combat_config_layout.addWidget(apply_config_btn)
        
        # 添加自动战斗配置选项卡
        self.tabs.addTab(combat_config_tab, "战斗配置")
    
    def create_log_area(self, main_layout):
        """创建日志区域"""
        log_group = QGroupBox("游戏动作日志")
        log_layout = QVBoxLayout(log_group)
        
        # 创建游戏动作日志文本框
        self.game_action_text = QTextEdit()
        self.game_action_text.setReadOnly(True)
        self.game_action_text.setMinimumHeight(200)
        self.game_action_text.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #FFFFFF;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 10pt;
            }
        """)
        log_layout.addWidget(self.game_action_text)
        
        # 日志控制按钮
        log_buttons_layout = QHBoxLayout()
        
        # 清空日志按钮
        clear_game_log_button = QPushButton("清空日志")
        clear_game_log_button.clicked.connect(self.clear_game_log)
        log_buttons_layout.addWidget(clear_game_log_button)
        
        # 添加日志控制按钮到日志布局
        log_layout.addLayout(log_buttons_layout)
        
        # 添加日志区域到主布局
        main_layout.addWidget(log_group)
    
    def refresh_process_list(self):
        """刷新进程列表"""
        # 记住当前选择
        current_text = self.process_combo.currentText()
        current_pid = self.process_combo.currentData() if self.process_combo.currentIndex() >= 0 else None
        
        # 清空下拉框
        self.process_combo.clear()
        
        # 获取进程列表
        processes = self.memory.get_processes_list()
        
        if not processes:
            self.process_combo.addItem("未找到游戏进程")
            return
            
        # 添加进程到下拉框
        aion_index = -1  # 记录第一个Aion进程的索引
        
        for i, proc in enumerate(processes):
            text = f"{proc['name']} (PID: {proc['pid']})"
            self.process_combo.addItem(text, proc['pid'])
            
            # 记录第一个Aion进程
            if aion_index == -1 and proc['is_aion']:
                aion_index = i
        
        # 自动选择逻辑：
        selected_index = -1
        
        # 1. 如果之前有选择的进程仍然存在，保持选择
        if current_pid:
            for i in range(self.process_combo.count()):
                if self.process_combo.itemData(i) == current_pid:
                    selected_index = i
                    break
        
        # 2. 如果没有之前的选择或进程已不存在，优先选择第一个Aion进程
        if selected_index == -1 and aion_index != -1:
            selected_index = aion_index
            
        # 3. 如果没有Aion进程，不改变默认选择（即第一个）
        if selected_index != -1:
            self.process_combo.setCurrentIndex(selected_index)
    
    def auto_search_game(self):
        """自动搜索并连接到Aion游戏进程"""
        # 获取进程列表
        processes = self.memory.get_processes_list()
        
        # 查找第一个Aion进程
        aion_index = -1
        
        for i, proc in enumerate(processes):
            if proc['is_aion']:
                aion_index = i
                break
        
        # 如果找到Aion进程，连接到它
        if aion_index != -1:
            process_id = processes[aion_index]['pid']
            self.memory.open_process(process_id)
            self.connect_button.setText("断开连接")
            self.connect_button.clicked.disconnect()
            self.connect_button.clicked.connect(self.disconnect_from_game)
            self.statusBar().showMessage(f"已连接到进程 {process_id}")
            self.add_game_log("连接", f"已连接到进程 {process_id}")
            self.start_button.setEnabled(True)
        else:
            QMessageBox.warning(self, "错误", "未找到Aion游戏进程")
    
    def connect_to_game(self):
        """连接到选定的游戏进程"""
        # 获取选定的进程ID
        index = self.process_combo.currentIndex()
        if index < 0:
            QMessageBox.warning(self, "错误", "请先选择一个游戏进程")
            return
            
        process_id = self.process_combo.itemData(index)
        
        if not process_id:
            QMessageBox.warning(self, "错误", "无效的进程ID")
            return
            
        # 尝试连接
        if self.memory.open_process(process_id):
            # 导入游戏版本选择功能
            import sys
            sys.path.append('..')
            from main import update_offsets, game_versions
            
            # 显示游戏版本选择对话框
            version_dialog = QDialog(self)
            version_dialog.setWindowTitle("选择游戏版本")
            version_dialog.setMinimumWidth(300)
            
            version_layout = QVBoxLayout(version_dialog)
            version_label = QLabel("请选择游戏版本:")
            version_layout.addWidget(version_label)
            
            version_combo = QComboBox()
            for version in game_versions.keys():
                version_combo.addItem(version)
            version_layout.addWidget(version_combo)
            
            button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
            button_box.accepted.connect(version_dialog.accept)
            button_box.rejected.connect(version_dialog.reject)
            version_layout.addWidget(button_box)
            
            if version_dialog.exec() == QDialog.DialogCode.Accepted:
                selected_version = version_combo.currentText()
                update_offsets(selected_version)
                
                QMessageBox.information(self, "成功", f"已成功连接到进程 {process_id}，游戏版本: {selected_version}")
                
                # 启用开始按钮和功能控件
                self.start_button.setEnabled(True)
                self.enable_feature_controls(True)
                
                # 更新UI
                self.connect_button.setText("断开连接")
                self.connect_button.clicked.disconnect()
                self.connect_button.clicked.connect(self.disconnect_from_game)
                
                # 更新状态栏
                self.statusBar().showMessage(f"已连接到进程 {process_id}，游戏版本: {selected_version}")
                
                # 添加日志
                self.add_game_log("连接", f"已连接到进程 {process_id}，游戏版本: {selected_version}")
                
                # 立即更新状态显示
                self.update_status_display()
            else:
                # 用户取消了版本选择，断开连接
                self.memory.close_process()
                QMessageBox.warning(self, "取消", "已取消连接")
        else:
            QMessageBox.critical(self, "错误", f"无法连接到进程 {process_id}")
    
    def disconnect_from_game(self):
        """断开与游戏的连接"""
        # 停止自动战斗
        if self.battle_manager.is_active:
            self.toggle_auto_battle()
            
        # 关闭进程
        self.memory.close_process()
        
        # 更新UI
        self.start_button.setEnabled(False)
        self.enable_feature_controls(False)
        self.connect_button.setText("连接到游戏")
        self.connect_button.clicked.disconnect()
        self.connect_button.clicked.connect(self.connect_to_game)
        
        # 更新状态栏
        self.statusBar().showMessage("已断开连接")
        
        # 添加日志
        self.add_game_log("断开", "已断开与游戏的连接")

    def toggle_auto_battle(self):
        """切换自动战斗状态"""
        # 优先使用增强的自动战斗系统
        combat_system = self.auto_combat if self.auto_combat else self.battle_manager
        
        if not combat_system:
            return
            
        if combat_system.is_active:
            # 停止自动战斗
            combat_system.stop()
            if self.battle_manager and self.battle_manager.is_active:
                self.battle_manager.stop()
            
            self.start_button.setText("开始副本辅助")
            self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
            self.add_game_log("战斗", "已停止增强副本辅助")
        else:
            # 开始自动战斗
            combat_system.start()
            
            self.start_button.setText("停止副本辅助")
            self.start_button.setStyleSheet("QPushButton { background-color: #F44336; color: white; }")
            self.add_game_log("战斗", "已开始增强副本辅助 (包含智能战斗和自动拾取)")
    
    def update_status_display(self):
        """更新状态显示"""
        # 简化状态更新，不再更新状态页面的内容
        pass
    
    def add_game_log(self, action_type, message):
        """添加游戏动作日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.game_action_text.append(f"[{timestamp}][{action_type}] {message}")
    
    def clear_game_log(self):
        """清空游戏动作日志区域"""
        self.game_action_text.clear()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止自动战斗
        if self.battle_manager.is_active:
            self.battle_manager.stop()
            
        # 断开与游戏的连接
        if self.memory.process_handle:
            self.memory.close_process()
            
        # 接受关闭事件
        event.accept()
        
    def enable_feature_controls(self, enabled):
        """启用或禁用功能控件"""
        # 基础功能
        self.attack_speed_check.setEnabled(enabled)
        self.attack_speed_value.setEnabled(enabled)
        self.move_speed_check.setEnabled(enabled)
        self.move_speed_value.setEnabled(enabled)
        
        # 高级功能
        self.attack_range_check.setEnabled(enabled)
        self.stealth_check.setEnabled(enabled)
        self.air_lock_check.setEnabled(enabled)
        self.fly_check.setEnabled(enabled)
        self.fly_value.setEnabled(enabled)
        self.fly_up_btn.setEnabled(enabled)
        self.fly_down_btn.setEnabled(enabled)
        self.view_range_check.setEnabled(enabled)
        self.view_range_value.setEnabled(enabled)
        self.show_hp_check.setEnabled(enabled)
        self.map_extend_check.setEnabled(enabled)
        self.map_extend_value.setEnabled(enabled)
        
        # 重置复选框状态
        if not enabled:
            self.attack_speed_check.setChecked(False)
            self.move_speed_check.setChecked(False)
            self.attack_range_check.setChecked(False)
            self.stealth_check.setChecked(False)
            self.air_lock_check.setChecked(False)
            self.fly_check.setChecked(False)
            self.view_range_check.setChecked(False)
            self.show_hp_check.setChecked(False)
            self.map_extend_check.setChecked(False)
    
    def apply_combat_config(self):
        """应用战斗配置到自动战斗系统"""
        if not self.auto_combat:
            QMessageBox.warning(self, "警告", "自动战斗系统未初始化")
            return
        
        try:
            # 构建配置字典
            config = {
                "attack_range": float(self.combat_attack_range.text()),
                "search_range": float(self.combat_search_range.text()),
                "retreat_hp_threshold": float(self.combat_retreat_hp.text()),
                "auto_loot_enabled": self.auto_loot_enabled.isChecked(),
                "loot_range": float(self.loot_range.text()),
                "loot_delay": float(self.loot_delay.text()),
                "use_potions": self.use_potions.isChecked(),
                "auto_buff": self.auto_buff.isChecked(),
                "loot_filter": {
                    "LEGENDARY": self.loot_legendary.isChecked(),
                    "EPIC": self.loot_epic.isChecked(),
                    "RARE": self.loot_rare.isChecked(),
                    "UNCOMMON": self.loot_uncommon.isChecked(),
                    "COMMON": self.loot_common.isChecked()
                }
            }
            
            # 设置目标选择算法
            algorithm_map = {
                "仇恨最高": "most_hated",
                "距离最近": "nearest",
                "血量最少": "weakest"
            }
            algorithm = algorithm_map.get(self.target_algorithm.currentText(), "most_hated")
            self.auto_combat.target_selection_algorithm = algorithm
            
            # 应用配置
            self.auto_combat.set_config(config)
            
            QMessageBox.information(self, "成功", "战斗配置已应用")
            self.add_game_log("配置", "战斗配置已更新")
            
        except ValueError as e:
            QMessageBox.critical(self, "错误", f"配置值无效: {e}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用配置失败: {e}")
    
    # 功能切换方法
    def toggle_attack_speed(self, checked):
        """切换技能加速"""
        try:
            speed_value = int(self.attack_speed_value.text())
            # 这里调用main.py中的功能函数
            import main
            if hasattr(main, 'memory_helper') and main.memory_helper:
                main.toggle_attack_speed(main.memory_helper, checked, speed_value)
                self.add_game_log("功能", f"技能加速: {'开启' if checked else '关闭'}")
        except Exception as e:
            self.add_game_log("错误", f"技能加速切换失败: {e}")
    
    def toggle_move_speed(self, checked):
        """切换移动加速"""
        try:
            speed_value = float(self.move_speed_value.text())
            import main
            if hasattr(main, 'memory_helper') and main.memory_helper:
                main.toggle_move_speed(main.memory_helper, checked, speed_value)
                self.add_game_log("功能", f"移动加速: {'开启' if checked else '关闭'}")
        except Exception as e:
            self.add_game_log("错误", f"移动加速切换失败: {e}")
    
    def toggle_attack_range(self, checked):
        """切换攻击距离"""
        try:
            range_value = float(self.attack_range_value.text())
            import main
            if hasattr(main, 'memory_helper') and main.memory_helper:
                main.toggle_attack_range(main.memory_helper, checked, range_value)
                self.add_game_log("功能", f"攻击距离: {'开启' if checked else '关闭'}")
        except Exception as e:
            self.add_game_log("错误", f"攻击距离切换失败: {e}")
    
    def toggle_stealth(self, checked):
        """切换反隐功能"""
        try:
            import main
            if hasattr(main, 'memory_helper') and main.memory_helper:
                main.toggle_stealth(main.memory_helper, checked)
                self.add_game_log("功能", f"反隐功能: {'开启' if checked else '关闭'}")
        except Exception as e:
            self.add_game_log("错误", f"反隐功能切换失败: {e}")
    
    def toggle_air_lock(self, checked):
        """切换锁空功能"""
        try:
            import main
            if hasattr(main, 'memory_helper') and main.memory_helper:
                main.toggle_air_lock(main.memory_helper, checked)
                self.add_game_log("功能", f"锁空功能: {'开启' if checked else '关闭'}")
        except Exception as e:
            self.add_game_log("错误", f"锁空功能切换失败: {e}")
    
    def toggle_fly(self, checked):
        """切换飞天功能"""
        try:
            height_value = float(self.fly_value.text())
            import main
            if hasattr(main, 'memory_helper') and main.memory_helper:
                main.toggle_fly(main.memory_helper, checked, height_value)
                self.add_game_log("功能", f"飞天功能: {'开启' if checked else '关闭'}")
                
                # 启用/禁用飞天控制按钮
                self.fly_up_btn.setEnabled(checked)
                self.fly_down_btn.setEnabled(checked)
        except Exception as e:
            self.add_game_log("错误", f"飞天功能切换失败: {e}")
    
    def fly_up(self):
        """上升"""
        try:
            height_value = float(self.fly_value.text())
            import main
            if hasattr(main, 'memory_helper') and main.memory_helper:
                main.toggle_fly(main.memory_helper, True, height_value, "up")
                self.add_game_log("飞天", f"上升 {height_value} 米")
        except Exception as e:
            self.add_game_log("错误", f"上升失败: {e}")
    
    def fly_down(self):
        """下降"""
        try:
            height_value = float(self.fly_value.text())
            import main
            if hasattr(main, 'memory_helper') and main.memory_helper:
                main.toggle_fly(main.memory_helper, True, height_value, "down")
                self.add_game_log("飞天", f"下降 {height_value} 米")
        except Exception as e:
            self.add_game_log("错误", f"下降失败: {e}")
    
    def toggle_view_range(self, checked):
        """切换视野扩展"""
        try:
            view_value = float(self.view_range_value.text())
            import main
            if hasattr(main, 'memory_helper') and main.memory_helper:
                main.toggle_view_range(main.memory_helper, checked, view_value)
                self.add_game_log("功能", f"视野扩展: {'开启' if checked else '关闭'}")
        except Exception as e:
            self.add_game_log("错误", f"视野扩展切换失败: {e}")
    
    def toggle_show_hp(self, checked):
        """切换显血功能"""
        try:
            import main
            if hasattr(main, 'memory_helper') and main.memory_helper:
                main.toggle_show_hp(main.memory_helper, checked)
                self.add_game_log("功能", f"显血功能: {'开启' if checked else '关闭'}")
        except Exception as e:
            self.add_game_log("错误", f"显血功能切换失败: {e}")
    
    def toggle_map_extend(self, checked):
        """切换地图扩展"""
        try:
            map_value = float(self.map_extend_value.text())
            import main
            if hasattr(main, 'memory_helper') and main.memory_helper:
                main.toggle_map_extend(main.memory_helper, checked, map_value)
                self.add_game_log("功能", f"地图扩展: {'开启' if checked else '关闭'}")
        except Exception as e:
            self.add_game_log("错误", f"地图扩展切换失败: {e}")