#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复版输入模拟器 - 使用Win32 API确保按键能够发送到游戏
"""

import time
import random
import logging
import win32api
import win32con
import win32gui
import ctypes
from ctypes import wintypes
from threading import Lock

# 设置日志
logger = logging.getLogger('aion_input_simulator')

class InputSimulatorFixed:
    """修复版输入模拟器，使用Win32 API确保按键发送到游戏"""
    
    def __init__(self):
        """初始化输入模拟器"""
        self.input_lock = Lock()
        
        # 配置
        self.key_press_duration = 0.05  # 按键持续时间(秒)
        self.human_factor = True  # 是否添加人类因素
        
        # 游戏窗口句柄
        self.game_window_handle = None
        self.game_process_id = None
        
        # 虚拟键码映射
        self.vk_codes = {
            '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34, '5': 0x35,
            '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39, '0': 0x30,
            'a': 0x41, 'b': 0x42, 'c': 0x43, 'd': 0x44, 'e': 0x45,
            'f': 0x46, 'g': 0x47, 'h': 0x48, 'i': 0x49, 'j': 0x4A,
            'k': 0x4B, 'l': 0x4C, 'm': 0x4D, 'n': 0x4E, 'o': 0x4F,
            'p': 0x50, 'q': 0x51, 'r': 0x52, 's': 0x53, 't': 0x54,
            'u': 0x55, 'v': 0x56, 'w': 0x57, 'x': 0x58, 'y': 0x59,
            'z': 0x5A,
            'f1': 0x70, 'f2': 0x71, 'f3': 0x72, 'f4': 0x73, 'f5': 0x74,
            'f6': 0x75, 'f7': 0x76, 'f8': 0x77, 'f9': 0x78, 'f10': 0x79,
            'f11': 0x7A, 'f12': 0x7B,
            'space': 0x20, 'enter': 0x0D, 'tab': 0x09, 'esc': 0x1B,
            'ctrl': 0x11, 'alt': 0x12, 'shift': 0x10,
            'left': 0x25, 'up': 0x26, 'right': 0x27, 'down': 0x28
        }
        
        logger.info("修复版输入模拟器已初始化")
    
    def find_game_window(self, window_title_keywords=None):
        """查找游戏窗口"""
        try:
            if window_title_keywords is None:
                # 常见的永恒之塔窗口标题关键词
                window_title_keywords = ["aion", "永恒之塔", "AION", "Aion"]
            
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    if window_title:
                        for keyword in window_title_keywords:
                            if keyword.lower() in window_title.lower():
                                windows.append((hwnd, window_title))
                return True
            
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            if windows:
                # 选择第一个匹配的窗口
                self.game_window_handle, window_title = windows[0]
                
                # 获取进程ID
                _, self.game_process_id = win32gui.GetWindowThreadProcessId(self.game_window_handle)
                
                logger.info(f"找到游戏窗口: {window_title} (句柄: {self.game_window_handle})")
                return True
            else:
                logger.warning("未找到游戏窗口")
                return False
                
        except Exception as e:
            logger.error(f"查找游戏窗口失败: {e}")
            return False
    
    def press_key(self, key):
        """
        发送按键到游戏窗口
        
        参数:
            key: 要按下的键 (例如: '1', '2', 'f1', 等)
        """
        try:
            with self.input_lock:
                # 确保有游戏窗口句柄
                if not self.game_window_handle:
                    if not self.find_game_window():
                        # 如果找不到游戏窗口，使用全局按键发送
                        return self._send_global_key(key)
                
                # 获取虚拟键码
                vk_code = self.vk_codes.get(key.lower())
                if vk_code is None:
                    logger.error(f"不支持的按键: {key}")
                    return False
                
                # 添加人类因素延迟
                if self.human_factor:
                    time.sleep(random.uniform(0.01, 0.03))
                
                # 方法1: 使用PostMessage发送到特定窗口
                success = self._send_key_to_window(vk_code, key)
                
                if not success:
                    # 方法2: 使用全局按键发送作为备选
                    logger.debug(f"窗口发送失败，尝试全局发送: {key}")
                    success = self._send_global_key(key)
                
                if success:
                    logger.debug(f"✓ 按键发送成功: {key}")
                else:
                    logger.warning(f"✗ 按键发送失败: {key}")
                
                return success
                
        except Exception as e:
            logger.error(f"按键操作失败: {key}, 错误: {e}")
            return False
    
    def _send_key_to_window(self, vk_code, key_name):
        """发送按键到特定窗口"""
        try:
            if not self.game_window_handle:
                return False
            
            # 计算lParam (扫描码和其他标志)
            scan_code = win32api.MapVirtualKey(vk_code, 0)
            lParam = (scan_code << 16) | 1
            
            # 发送WM_KEYDOWN消息
            result1 = win32gui.PostMessage(self.game_window_handle, win32con.WM_KEYDOWN, vk_code, lParam)
            
            # 短暂延迟
            time.sleep(self.key_press_duration)
            
            # 发送WM_KEYUP消息
            lParam |= 0xC0000000  # 设置释放标志
            result2 = win32gui.PostMessage(self.game_window_handle, win32con.WM_KEYUP, vk_code, lParam)
            
            return result1 != 0 and result2 != 0
            
        except Exception as e:
            logger.error(f"窗口按键发送失败: {e}")
            return False
    
    def _send_global_key(self, key):
        """发送全局按键"""
        try:
            vk_code = self.vk_codes.get(key.lower())
            if vk_code is None:
                return False
            
            # 使用keybd_event发送全局按键
            win32api.keybd_event(vk_code, 0, 0, 0)  # 按下
            time.sleep(self.key_press_duration)
            win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放
            
            return True
            
        except Exception as e:
            logger.error(f"全局按键发送失败: {e}")
            return False
    
    def press_key_combo(self, keys):
        """
        发送组合键
        
        参数:
            keys: 要同时按下的键列表 (例如: ['alt', '1'])
        """
        try:
            with self.input_lock:
                if self.human_factor:
                    time.sleep(random.uniform(0.01, 0.03))
                
                # 获取所有虚拟键码
                vk_codes = []
                for key in keys:
                    vk_code = self.vk_codes.get(key.lower())
                    if vk_code is None:
                        logger.error(f"组合键中包含不支持的按键: {key}")
                        return False
                    vk_codes.append(vk_code)
                
                # 按下所有键
                for vk_code in vk_codes:
                    if self.game_window_handle:
                        scan_code = win32api.MapVirtualKey(vk_code, 0)
                        lParam = (scan_code << 16) | 1
                        win32gui.PostMessage(self.game_window_handle, win32con.WM_KEYDOWN, vk_code, lParam)
                    else:
                        win32api.keybd_event(vk_code, 0, 0, 0)
                
                time.sleep(self.key_press_duration)
                
                # 释放所有键（按相反顺序）
                for vk_code in reversed(vk_codes):
                    if self.game_window_handle:
                        scan_code = win32api.MapVirtualKey(vk_code, 0)
                        lParam = (scan_code << 16) | 1 | 0xC0000000
                        win32gui.PostMessage(self.game_window_handle, win32con.WM_KEYUP, vk_code, lParam)
                    else:
                        win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)
                
                logger.debug(f"✓ 组合键发送成功: {'+'.join(keys)}")
                return True
                
        except Exception as e:
            logger.error(f"组合键操作失败: {keys}, 错误: {e}")
            
            # 确保所有键都被释放
            for key in keys:
                vk_code = self.vk_codes.get(key.lower())
                if vk_code:
                    try:
                        win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)
                    except:
                        pass
            
            return False
    
    def click(self, x=None, y=None, button='left'):
        """模拟鼠标点击"""
        try:
            with self.input_lock:
                # 如果坐标未指定，使用当前位置
                if x is None or y is None:
                    current_pos = win32gui.GetCursorPos()
                    x = x if x is not None else current_pos[0]
                    y = y if y is not None else current_pos[1]
                
                # 移动鼠标
                win32api.SetCursorPos((int(x), int(y)))
                
                # 确定按键事件
                if button == 'left':
                    down_event = win32con.MOUSEEVENTF_LEFTDOWN
                    up_event = win32con.MOUSEEVENTF_LEFTUP
                elif button == 'right':
                    down_event = win32con.MOUSEEVENTF_RIGHTDOWN
                    up_event = win32con.MOUSEEVENTF_RIGHTUP
                elif button == 'middle':
                    down_event = win32con.MOUSEEVENTF_MIDDLEDOWN
                    up_event = win32con.MOUSEEVENTF_MIDDLEUP
                else:
                    raise ValueError(f"不支持的按钮类型: {button}")
                
                # 发送鼠标事件
                win32api.mouse_event(down_event, 0, 0, 0, 0)
                time.sleep(0.05)
                win32api.mouse_event(up_event, 0, 0, 0, 0)
                
                logger.debug(f"✓ 鼠标点击: {button} 按钮，位置: ({int(x)}, {int(y)})")
                return True
                
        except Exception as e:
            logger.error(f"鼠标点击失败: {e}")
            return False
    
    def set_game_window_handle(self, handle):
        """手动设置游戏窗口句柄"""
        self.game_window_handle = handle
        if handle:
            _, self.game_process_id = win32gui.GetWindowThreadProcessId(handle)
            logger.info(f"手动设置游戏窗口句柄: {handle}")
    
    def get_game_window_info(self):
        """获取游戏窗口信息"""
        if self.game_window_handle:
            try:
                title = win32gui.GetWindowText(self.game_window_handle)
                rect = win32gui.GetWindowRect(self.game_window_handle)
                return {
                    'handle': self.game_window_handle,
                    'title': title,
                    'rect': rect,
                    'process_id': self.game_process_id
                }
            except:
                pass
        return None
    
    def focus_game_window(self):
        """将游戏窗口置于前台"""
        try:
            if self.game_window_handle:
                win32gui.SetForegroundWindow(self.game_window_handle)
                win32gui.ShowWindow(self.game_window_handle, win32con.SW_RESTORE)
                logger.info("游戏窗口已置于前台")
                return True
        except Exception as e:
            logger.error(f"无法将游戏窗口置于前台: {e}")
        return False
    
    def test_key_sending(self):
        """测试按键发送功能"""
        logger.info("开始测试按键发送功能...")
        
        # 查找游戏窗口
        if self.find_game_window():
            logger.info("找到游戏窗口，将使用窗口特定发送")
        else:
            logger.info("未找到游戏窗口，将使用全局发送")
        
        # 测试基本按键
        test_keys = ['1', '2', '3']
        success_count = 0
        
        for key in test_keys:
            if self.press_key(key):
                success_count += 1
            time.sleep(0.5)
        
        logger.info(f"按键测试完成: {success_count}/{len(test_keys)} 成功")
        return success_count > 0