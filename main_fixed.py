#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
永恒之塔游戏增强工具 - 修复版
"""

import os
import sys
import ctypes
import psutil
import time
import threading
import logging
import win32api
import win32con
import win32gui
from datetime import datetime
from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

# PyQt6 导入
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QIcon, QFont
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QLabel, QPushButton, QVBoxLayout,
    QHBoxLayout, QGroupBox, QTabWidget, QComboBox, QCheckBox,
    QMessageBox, QStatusBar, QFormLayout, QSlider, QGridLayout
)

# 核心组件导入
try:
    from memory_reader import MemoryReader
    from battle_logic import BattleManager
    from monster_select import MonsterSelector
except ImportError as e:
    print(f"警告: 无法导入某些模块: {e}")
    # 创建模拟类
    class MemoryReader:
        def __init__(self): pass
    class BattleManager:
        def __init__(self, *args): 
            self.is_active = False
        def set_log_callback(self, callback): pass
        def update(self): pass
    class MonsterSelector:
        def __init__(self, *args): pass

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('main_fixed.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)#
 ==================== 增强输入模拟器 ====================

class EnhancedInputSimulator:
    """增强版输入模拟器"""
    
    def __init__(self):
        self.input_lock = threading.Lock()
        self.game_window = None
        self.game_process_id = None
        self.is_admin = self._check_admin_rights()
        
        # 虚拟键码映射
        self.vk_codes = {
            '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34, '5': 0x35,
            '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39, '0': 0x30,
            'f1': 0x70, 'f2': 0x71, 'f3': 0x72, 'f4': 0x73, 'f5': 0x74,
            'space': 0x20, 'tab': 0x09, 'alt': 0x12, 'ctrl': 0x11
        }
        
        # 按键配置
        self.key_press_duration = 0.02
        self.combo_key_duration = 0.03
        self.key_interval = 0.01
        
        # 查找游戏窗口
        self.find_aion_window()
        
        logger.info(f"增强输入模拟器初始化 - 管理员权限: {self.is_admin}")
    
    def _check_admin_rights(self):
        """检查管理员权限"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def find_aion_window(self):
        """查找永恒之塔游戏窗口"""
        try:
            def enum_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    title = win32gui.GetWindowText(hwnd)
                    aion_keywords = ['aion', '永恒之塔', 'AION', 'Aion']
                    if any(keyword in title.lower() for keyword in aion_keywords):
                        try:
                            _, pid = win32gui.GetWindowThreadProcessId(hwnd)
                            process = psutil.Process(pid)
                            exe_name = process.name().lower()
                            if 'aion' in exe_name or 'bin32' in exe_name or 'bin64' in exe_name:
                                windows.append((hwnd, title, pid, exe_name))
                        except:
                            pass
                return True
            
            windows = []
            win32gui.EnumWindows(enum_callback, windows)
            
            if windows:
                self.game_window, title, self.game_process_id, exe_name = windows[0]
                logger.info(f"找到游戏窗口: {title} (PID: {self.game_process_id})")
                self.focus_game_window()
                return True
            else:
                logger.warning("未找到永恒之塔游戏窗口")
                return False
                
        except Exception as e:
            logger.error(f"查找游戏窗口失败: {e}")
            return False
    
    def focus_game_window(self):
        """将游戏窗口置于前台"""
        if not self.game_window:
            return False
        
        try:
            if win32gui.IsIconic(self.game_window):
                win32gui.ShowWindow(self.game_window, win32con.SW_RESTORE)
            
            win32gui.SetForegroundWindow(self.game_window)
            win32gui.BringWindowToTop(self.game_window)
            return True
        except Exception as e:
            logger.error(f"无法将游戏窗口置于前台: {e}")
            return False
    
    def press_key(self, key):
        """发送按键到游戏"""
        if not self._validate_key(key):
            return False
        
        with self.input_lock:
            # 确保游戏窗口有焦点
            if self.game_window:
                self.focus_game_window()
                time.sleep(self.key_interval)
            
            # 尝试多种方法发送按键
            success = (self._send_key_to_window(key) or 
                      self._send_global_key(key) or 
                      self._send_input_key(key))
            
            return success
    
    def _validate_key(self, key):
        """验证按键是否支持"""
        return key.lower() in self.vk_codes
    
    def _send_key_to_window(self, key):
        """发送按键到特定窗口"""
        try:
            vk_code = self.vk_codes[key.lower()]
            scan_code = win32api.MapVirtualKey(vk_code, 0)
            
            # WM_KEYDOWN
            lParam_down = (scan_code << 16) | 1
            result1 = win32gui.PostMessage(self.game_window, win32con.WM_KEYDOWN, vk_code, lParam_down)
            
            time.sleep(self.key_press_duration)
            
            # WM_KEYUP
            lParam_up = (scan_code << 16) | 0xC0000001
            result2 = win32gui.PostMessage(self.game_window, win32con.WM_KEYUP, vk_code, lParam_up)
            
            return result1 != 0 and result2 != 0
        except:
            return False
    
    def _send_global_key(self, key):
        """发送全局按键"""
        try:
            vk_code = self.vk_codes[key.lower()]
            win32api.keybd_event(vk_code, 0, 0, 0)
            time.sleep(self.key_press_duration)
            win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)
            return True
        except:
            return False
    
    def _send_input_key(self, key):
        """使用SendInput API发送按键"""
        try:
            vk_code = self.vk_codes[key.lower()]
            ctypes.windll.user32.keybd_event(vk_code, 0, 0, 0)
            time.sleep(self.key_press_duration)
            ctypes.windll.user32.keybd_event(vk_code, 0, 2, 0)
            return True
        except:
            return False
    
    def press_alt_key(self, key):
        """发送Alt+按键组合"""
        return self.press_key_combo(['alt', key])
    
    def press_key_combo(self, keys):
        """发送组合键"""
        if not all(self._validate_key(key) for key in keys):
            return False
        
        with self.input_lock:
            try:
                if self.game_window:
                    self.focus_game_window()
                    time.sleep(self.key_interval)
                
                vk_codes = [self.vk_codes[key.lower()] for key in keys]
                
                # 按下所有键
                for vk_code in vk_codes:
                    win32api.keybd_event(vk_code, 0, 0, 0)
                    time.sleep(0.005)
                
                time.sleep(self.combo_key_duration)
                
                # 释放所有键
                for vk_code in reversed(vk_codes):
                    win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)
                    time.sleep(0.005)
                
                return True
            except Exception as e:
                # 确保所有键都被释放
                for key in keys:
                    try:
                        vk_code = self.vk_codes[key.lower()]
                        win32api.keybd_event(vk_code, 0, win32con.KEYEVENTF_KEYUP, 0)
                    except:
                        pass
                return False# ====
================ 智能目标管理系统 ====================

class TargetState(Enum):
    """目标状态"""
    UNKNOWN = "unknown"
    ALIVE = "alive"
    DEAD = "dead"
    LOST = "lost"

@dataclass
class TargetInfo:
    """目标信息"""
    id: str
    name: str
    hp_percent: float = 100.0
    last_seen: float = 0.0
    state: TargetState = TargetState.UNKNOWN
    attack_start_time: float = 0.0
    estimated_hp: float = 100.0

class SmartTargetManager:
    """智能目标管理器"""
    
    def __init__(self, input_simulator):
        self.input = input_simulator
        self.current_target: Optional[TargetInfo] = None
        self.target_locked = False
        
        # 配置
        self.target_timeout = 10.0
        self.hp_check_interval = 2.0
        self.last_hp_check = 0.0
        self.max_retarget_attempts = 3
        self.retarget_attempts = 0
        
        # 统计
        self.stats = {
            'targets_killed': 0,
            'targets_lost': 0,
            'average_kill_time': 0.0,
            'kill_times': []
        }
    
    def should_select_new_target(self) -> bool:
        """判断是否应该选择新目标"""
        current_time = time.time()
        
        if not self.current_target:
            return True
        
        if self.current_target.state == TargetState.DEAD:
            logger.info(f"目标 {self.current_target.name} 已死亡，选择新目标")
            self._record_kill()
            self.current_target = None
            self.target_locked = False
            return True
        
        if (current_time - self.current_target.last_seen) > self.target_timeout:
            logger.warning(f"目标 {self.current_target.name} 丢失超时")
            self.stats['targets_lost'] += 1
            self.current_target = None
            self.target_locked = False
            return True
        
        if (current_time - self.last_hp_check) > self.hp_check_interval:
            self._check_target_hp()
            self.last_hp_check = current_time
        
        return False
    
    def select_target(self) -> bool:
        """选择新目标"""
        try:
            success = self.input.press_key('tab')
            
            if success:
                current_time = time.time()
                target_id = f"target_{int(current_time)}"
                
                self.current_target = TargetInfo(
                    id=target_id,
                    name=f"怪物_{target_id[-4:]}",
                    hp_percent=100.0,
                    last_seen=current_time,
                    state=TargetState.ALIVE,
                    attack_start_time=current_time
                )
                
                self.target_locked = True
                self.retarget_attempts = 0
                
                logger.info(f"🎯 选择新目标: {self.current_target.name}")
                return True
            else:
                self.retarget_attempts += 1
                return self.retarget_attempts < self.max_retarget_attempts
                
        except Exception as e:
            logger.error(f"选择目标时出错: {e}")
            return False
    
    def _check_target_hp(self):
        """检查目标血量（模拟）"""
        if not self.current_target:
            return
        
        current_time = time.time()
        attack_duration = current_time - self.current_target.attack_start_time
        
        # 模拟血量下降
        damage_per_second = 10.0
        estimated_damage = attack_duration * damage_per_second
        self.current_target.estimated_hp = max(0, 100.0 - estimated_damage)
        
        if self.current_target.estimated_hp <= 0:
            self.current_target.state = TargetState.DEAD
        else:
            self.current_target.state = TargetState.ALIVE
            self.current_target.last_seen = current_time
    
    def _record_kill(self):
        """记录击杀"""
        if not self.current_target:
            return
        
        kill_time = time.time() - self.current_target.attack_start_time
        self.stats['targets_killed'] += 1
        self.stats['kill_times'].append(kill_time)
        
        if self.stats['kill_times']:
            self.stats['average_kill_time'] = sum(self.stats['kill_times']) / len(self.stats['kill_times'])
    
    def get_current_target_info(self) -> Optional[Dict]:
        """获取当前目标信息"""
        if not self.current_target:
            return None
        
        current_time = time.time()
        attack_duration = current_time - self.current_target.attack_start_time
        
        return {
            'name': self.current_target.name,
            'hp_percent': self.current_target.estimated_hp,
            'state': self.current_target.state.value,
            'attack_duration': attack_duration,
            'is_locked': self.target_locked
        }
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        return self.stats.copy()# ======
============== 增强自动战斗系统 ====================

class EnhancedAutoCombatSystem:
    """增强版自动战斗系统"""
    
    def __init__(self, memory_reader, input_simulator, monster_selector):
        self.memory = memory_reader
        self.input = input_simulator
        self.monster_selector = monster_selector
        
        # 智能目标管理
        self.target_manager = SmartTargetManager(input_simulator)
        
        # AI状态
        self.is_active = False
        self.combat_thread = None
        
        # 技能配置
        self.basic_skills = ['1', '2', '3', '4', '5', '6', '7', '8', '9']
        self.alt_skills = ['a+1', 'a+2', 'a+3', 'a+4', 'a+5', 'a+6', 'a+7', 'a+8', 'a+9', 'a+0']
        self.all_skills = self.basic_skills + self.alt_skills
        self.current_skill_index = 0
        
        # 战斗配置
        self.combat_modes = {
            'fast': {'skill_interval': 0.15, 'target_interval': 3.0},
            'normal': {'skill_interval': 0.3, 'target_interval': 2.0},
            'slow': {'skill_interval': 0.5, 'target_interval': 1.5}
        }
        self.current_mode = 'fast'
        self.skill_interval = self.combat_modes[self.current_mode]['skill_interval']
        self.target_interval = self.combat_modes[self.current_mode]['target_interval']
        
        # 技能策略
        self.skill_strategy = 'mixed'  # 'basic_only', 'alt_only', 'mixed'
        
        # 统计信息
        self.combat_stats = {
            'combat_sessions': 0,
            'total_combat_time': 0,
            'skills_cast': 0,
            'skills_per_minute': 0,
            'start_time': 0
        }
        
        # 调试模式
        self.debug_mode = False
        
        logger.info("增强自动战斗系统已初始化")
    
    def enable_debug_mode(self, enabled: bool = True):
        """启用调试模式"""
        self.debug_mode = enabled
        if enabled:
            logging.getLogger().setLevel(logging.DEBUG)
            logger.info("调试模式已启用")
        else:
            logging.getLogger().setLevel(logging.INFO)
    
    def set_combat_mode(self, mode):
        """设置战斗模式"""
        if mode in self.combat_modes:
            self.current_mode = mode
            config = self.combat_modes[mode]
            self.skill_interval = config['skill_interval']
            self.target_interval = config['target_interval']
            logger.info(f"战斗模式设置为: {mode}")
    
    def set_skill_strategy(self, strategy):
        """设置技能策略"""
        valid_strategies = ['basic_only', 'alt_only', 'mixed']
        if strategy in valid_strategies:
            self.skill_strategy = strategy
            logger.info(f"技能策略设置为: {strategy}")
    
    def start(self):
        """启动自动战斗"""
        if self.is_active:
            return
        
        self.is_active = True
        self.combat_stats['combat_sessions'] += 1
        self.combat_stats['start_time'] = time.time()
        
        self.combat_thread = threading.Thread(target=self._combat_loop, daemon=True)
        self.combat_thread.start()
        
        logger.info(f"🚀 增强自动战斗已启动 (模式: {self.current_mode}, 策略: {self.skill_strategy})")
    
    def stop(self):
        """停止自动战斗"""
        if not self.is_active:
            return
        
        self.is_active = False
        if self.combat_thread and self.combat_thread.is_alive():
            self.combat_thread.join(timeout=2.0)
        
        # 计算统计信息
        if self.combat_stats['start_time'] > 0:
            combat_time = time.time() - self.combat_stats['start_time']
            self.combat_stats['total_combat_time'] += combat_time
            if combat_time > 0:
                self.combat_stats['skills_per_minute'] = (self.combat_stats['skills_cast'] / combat_time) * 60
        
        logger.info("⏹ 增强自动战斗已停止")
        self._print_combat_summary()
    
    def _combat_loop(self):
        """战斗主循环"""
        logger.info("进入增强战斗循环...")
        
        while self.is_active:
            try:
                # 智能目标管理
                if self.target_manager.should_select_new_target():
                    self._handle_target_selection()
                    time.sleep(0.1)
                
                # 执行攻击
                if self.target_manager.current_target:
                    if self._execute_attack():
                        self.combat_stats['skills_cast'] += 1
                
                # 显示战斗状态
                if self.debug_mode:
                    self._display_combat_status()
                
                # 等待下一循环
                time.sleep(self.skill_interval)
                
            except Exception as e:
                logger.error(f"战斗循环错误: {e}")
                time.sleep(0.5)
    
    def _handle_target_selection(self):
        """处理目标选择"""
        try:
            success = self.target_manager.select_target()
            if success:
                target_info = self.target_manager.get_current_target_info()
                if target_info:
                    logger.info(f"🎯 锁定目标: {target_info['name']} - 开始专注击杀")
            else:
                logger.warning("⚠ 目标选择失败，等待重试...")
                time.sleep(1.0)
        except Exception as e:
            logger.error(f"目标选择处理错误: {e}")
    
    def _execute_attack(self) -> bool:
        """执行攻击"""
        try:
            skill = self._get_next_skill()
            if not skill:
                return False
            
            success = self._send_skill(skill)
            
            if success:
                target_info = self.target_manager.get_current_target_info()
                if target_info and self.debug_mode:
                    logger.debug(f"⚔️ 攻击 {target_info['name']}: {skill} (血量: {target_info['hp_percent']:.1f}%)")
                return True
            else:
                logger.warning(f"⚠ 技能发送失败: {skill}")
                return False
                
        except Exception as e:
            logger.error(f"攻击执行错误: {e}")
            return False
    
    def _get_next_skill(self) -> str:
        """获取下一个技能"""
        if self.skill_strategy == 'basic_only':
            skills = self.basic_skills
        elif self.skill_strategy == 'alt_only':
            skills = self.alt_skills
        else:  # mixed
            skills = self.all_skills
        
        skill = skills[self.current_skill_index % len(skills)]
        self.current_skill_index += 1
        
        return skill
    
    def _send_skill(self, skill) -> bool:
        """发送技能"""
        try:
            if skill.startswith('a+'):
                # Alt组合键
                key_part = skill.split('+')[1]
                return self.input.press_alt_key(key_part)
            else:
                # 普通按键
                return self.input.press_key(skill)
        except Exception as e:
            logger.error(f"技能发送错误: {e}")
            return False
    
    def _display_combat_status(self):
        """显示战斗状态"""
        target_info = self.target_manager.get_current_target_info()
        
        if target_info and int(time.time()) % 5 == 0:
            logger.info(f"🎯 当前目标: {target_info['name']} (血量: {target_info['hp_percent']:.1f}%, 攻击时长: {target_info['attack_duration']:.1f}秒)")
    
    def _print_combat_summary(self):
        """打印战斗总结"""
        target_stats = self.target_manager.get_stats()
        
        logger.info("📊 战斗总结:")
        logger.info(f"   击杀数量: {target_stats['targets_killed']}")
        logger.info(f"   丢失目标: {target_stats['targets_lost']}")
        logger.info(f"   平均击杀时间: {target_stats['average_kill_time']:.1f}秒")
        logger.info(f"   技能释放数: {self.combat_stats['skills_cast']}")
        logger.info(f"   技能速度: {self.combat_stats['skills_per_minute']:.1f} 技能/分钟")
        
        if target_stats['targets_killed'] > 0:
            efficiency = target_stats['targets_killed'] / (target_stats['targets_killed'] + target_stats['targets_lost'])
            logger.info(f"   击杀效率: {efficiency*100:.1f}%")
    
    def get_status(self):
        """获取系统状态"""
        target_info = self.target_manager.get_current_target_info()
        target_stats = self.target_manager.get_stats()
        
        return {
            'is_active': self.is_active,
            'mode': self.current_mode,
            'strategy': self.skill_strategy,
            'current_target': target_info,
            'target_stats': target_stats,
            'combat_stats': self.combat_stats
        }# ========
============ 增强主窗口 ====================

class EnhancedMainWindow(QMainWindow):
    """增强版主窗口"""
    
    def __init__(self, memory_reader, battle_manager, auto_combat):
        super().__init__()
        self.memory = memory_reader
        self.battle_manager = battle_manager
        self.auto_combat = auto_combat
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("永恒之塔游戏增强工具 - 修复版")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)
        
        # 战斗控制选项卡
        combat_tab = self.create_combat_tab()
        tab_widget.addTab(combat_tab, "战斗控制")
        
        # 系统状态选项卡
        status_tab = self.create_status_tab()
        tab_widget.addTab(status_tab, "系统状态")
        
        # 状态栏
        self.statusBar().showMessage("系统就绪")
    
    def create_combat_tab(self):
        """创建战斗控制选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 战斗控制组
        combat_group = QGroupBox("战斗控制")
        combat_layout = QGridLayout(combat_group)
        
        # 开始/停止按钮
        self.start_combat_btn = QPushButton("开始自动战斗")
        self.start_combat_btn.clicked.connect(self.toggle_combat)
        self.start_combat_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        combat_layout.addWidget(self.start_combat_btn, 0, 0, 1, 2)
        
        # 战斗模式选择
        combat_layout.addWidget(QLabel("战斗模式:"), 1, 0)
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["fast", "normal", "slow"])
        self.mode_combo.currentTextChanged.connect(self.on_mode_changed)
        combat_layout.addWidget(self.mode_combo, 1, 1)
        
        # 技能策略选择
        combat_layout.addWidget(QLabel("技能策略:"), 2, 0)
        self.strategy_combo = QComboBox()
        self.strategy_combo.addItems(["mixed", "basic_only", "alt_only"])
        self.strategy_combo.currentTextChanged.connect(self.on_strategy_changed)
        combat_layout.addWidget(self.strategy_combo, 2, 1)
        
        # 调试模式
        self.debug_checkbox = QCheckBox("调试模式")
        self.debug_checkbox.toggled.connect(self.on_debug_toggled)
        combat_layout.addWidget(self.debug_checkbox, 3, 0, 1, 2)
        
        layout.addWidget(combat_group)
        
        # 快速测试组
        test_group = QGroupBox("快速测试")
        test_layout = QGridLayout(test_group)
        
        # 测试按键按钮
        test_basic_btn = QPushButton("测试基础技能 (1-9)")
        test_basic_btn.clicked.connect(self.test_basic_skills)
        test_layout.addWidget(test_basic_btn, 0, 0)
        
        test_alt_btn = QPushButton("测试Alt技能 (Alt+1-9,0)")
        test_alt_btn.clicked.connect(self.test_alt_skills)
        test_layout.addWidget(test_alt_btn, 0, 1)
        
        test_target_btn = QPushButton("测试目标选择 (Tab)")
        test_target_btn.clicked.connect(self.test_target_selection)
        test_layout.addWidget(test_target_btn, 1, 0)
        
        layout.addWidget(test_group)
        
        # 日志显示
        log_group = QGroupBox("战斗日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QLabel("等待战斗开始...")
        self.log_text.setWordWrap(True)
        self.log_text.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 10px; }")
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        return widget
    
    def create_status_tab(self):
        """创建系统状态选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 系统状态组
        system_group = QGroupBox("系统状态")
        system_layout = QFormLayout(system_group)
        
        self.admin_status = QLabel("检查中...")
        system_layout.addRow("管理员权限:", self.admin_status)
        
        self.game_status = QLabel("检查中...")
        system_layout.addRow("游戏连接:", self.game_status)
        
        self.input_status = QLabel("检查中...")
        system_layout.addRow("输入模拟器:", self.input_status)
        
        layout.addWidget(system_group)
        
        # 战斗统计组
        stats_group = QGroupBox("战斗统计")
        stats_layout = QFormLayout(stats_group)
        
        self.targets_killed = QLabel("0")
        stats_layout.addRow("击杀数量:", self.targets_killed)
        
        self.targets_lost = QLabel("0")
        stats_layout.addRow("丢失目标:", self.targets_lost)
        
        self.skills_cast = QLabel("0")
        stats_layout.addRow("技能释放:", self.skills_cast)
        
        self.skills_per_minute = QLabel("0")
        stats_layout.addRow("技能速度:", self.skills_per_minute)
        
        self.current_target = QLabel("无")
        stats_layout.addRow("当前目标:", self.current_target)
        
        layout.addWidget(stats_group)
        
        return widget
    
    def toggle_combat(self):
        """切换战斗状态"""
        if self.auto_combat.is_active:
            self.auto_combat.stop()
            self.start_combat_btn.setText("开始自动战斗")
            self.start_combat_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
            self.statusBar().showMessage("自动战斗已停止")
        else:
            self.auto_combat.start()
            self.start_combat_btn.setText("停止自动战斗")
            self.start_combat_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
            self.statusBar().showMessage("自动战斗已启动")
    
    def on_mode_changed(self, mode):
        """战斗模式改变"""
        self.auto_combat.set_combat_mode(mode)
        self.statusBar().showMessage(f"战斗模式已设置为: {mode}")
    
    def on_strategy_changed(self, strategy):
        """技能策略改变"""
        self.auto_combat.set_skill_strategy(strategy)
        self.statusBar().showMessage(f"技能策略已设置为: {strategy}")
    
    def on_debug_toggled(self, checked):
        """调试模式切换"""
        self.auto_combat.enable_debug_mode(checked)
        self.statusBar().showMessage(f"调试模式: {'开启' if checked else '关闭'}")
    
    def test_basic_skills(self):
        """测试基础技能"""
        self.statusBar().showMessage("测试基础技能中...")
        
        def test_thread():
            for skill in self.auto_combat.basic_skills:
                success = self.auto_combat.input.press_key(skill)
                logger.info(f"测试技能 {skill}: {'成功' if success else '失败'}")
                time.sleep(0.5)
            
            self.statusBar().showMessage("基础技能测试完成")
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def test_alt_skills(self):
        """测试Alt技能"""
        self.statusBar().showMessage("测试Alt技能中...")
        
        def test_thread():
            for skill in self.auto_combat.alt_skills:
                key_part = skill.split('+')[1]
                success = self.auto_combat.input.press_alt_key(key_part)
                logger.info(f"测试Alt技能 {skill}: {'成功' if success else '失败'}")
                time.sleep(0.5)
            
            self.statusBar().showMessage("Alt技能测试完成")
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def test_target_selection(self):
        """测试目标选择"""
        success = self.auto_combat.input.press_key('tab')
        self.statusBar().showMessage(f"目标选择测试: {'成功' if success else '失败'}")
    
    def update_status(self):
        """更新状态显示"""
        try:
            # 更新系统状态
            self.admin_status.setText("✓ 是" if self.auto_combat.input.is_admin else "✗ 否")
            self.game_status.setText("✓ 已连接" if self.auto_combat.input.game_window else "✗ 未连接")
            self.input_status.setText("✓ 正常" if self.auto_combat.input else "✗ 异常")
            
            # 更新战斗统计
            status = self.auto_combat.get_status()
            
            if status['target_stats']:
                stats = status['target_stats']
                self.targets_killed.setText(str(stats['targets_killed']))
                self.targets_lost.setText(str(stats['targets_lost']))
            
            if status['combat_stats']:
                combat_stats = status['combat_stats']
                self.skills_cast.setText(str(combat_stats['skills_cast']))
                self.skills_per_minute.setText(f"{combat_stats['skills_per_minute']:.1f}")
            
            # 更新当前目标
            if status['current_target']:
                target = status['current_target']
                self.current_target.setText(f"{target['name']} ({target['hp_percent']:.1f}%)")
            else:
                self.current_target.setText("无")
            
            # 更新日志
            if self.auto_combat.is_active:
                if status['current_target']:
                    target = status['current_target']
                    log_text = f"🎯 攻击目标: {target['name']}\n"
                    log_text += f"血量: {target['hp_percent']:.1f}%\n"
                    log_text += f"攻击时长: {target['attack_duration']:.1f}秒\n"
                    log_text += f"模式: {status['mode']} | 策略: {status['strategy']}"
                    self.log_text.setText(log_text)
                else:
                    self.log_text.setText("🔍 搜索目标中...")
            else:
                self.log_text.setText("⏸ 战斗已暂停")
                
        except Exception as e:
            logger.error(f"更新状态时出错: {e}")
    
    def add_game_log(self, message):
        """添加游戏日志 - 兼容原有接口"""
        pass#
 ==================== 主函数 ====================

def check_requirements():
    """检查运行要求"""
    issues = []
    
    # 检查管理员权限
    try:
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if not is_admin:
            issues.append("需要管理员权限")
    except:
        issues.append("无法检查管理员权限")
    
    # 检查游戏进程
    game_found = False
    for proc in psutil.process_iter(['name']):
        try:
            if 'aion' in proc.info['name'].lower():
                game_found = True
                break
        except:
            continue
    
    if not game_found:
        issues.append("未找到游戏进程")
    
    return issues

def main():
    """主函数"""
    print("=" * 60)
    print("🎮 永恒之塔游戏增强工具 - 修复版")
    print("=" * 60)
    
    # 检查运行要求
    issues = check_requirements()
    if issues:
        print("\n❌ 发现问题:")
        for issue in issues:
            print(f"  - {issue}")
        
        if "需要管理员权限" in issues:
            print("\n解决方案: 右键程序 -> '以管理员身份运行'")
        
        if "未找到游戏进程" in issues:
            print("\n解决方案: 请先启动永恒之塔游戏")
        
        input("\n按回车键退出...")
        return False
    
    try:
        # 创建Qt应用
        app = QApplication(sys.argv)
        app.setApplicationName("永恒之塔游戏增强工具")
        app.setApplicationVersion("2.0.0")
        
        logger.info("初始化系统组件...")
        
        # 创建核心组件
        memory = MemoryReader()
        input_sim = EnhancedInputSimulator()
        monster_selector = MonsterSelector(memory)
        battle_manager = BattleManager(memory, input_sim, monster_selector)
        
        # 创建增强自动战斗系统
        auto_combat = EnhancedAutoCombatSystem(memory, input_sim, monster_selector)
        
        # 创建主窗口
        window = EnhancedMainWindow(memory, battle_manager, auto_combat)
        window.show()
        
        # 设置日志回调
        battle_manager.set_log_callback(window.add_game_log)
        
        logger.info("=" * 60)
        logger.info("🎉 系统启动完成!")
        logger.info("")
        logger.info("功能特点:")
        logger.info("✅ 智能目标管理 - 专注击杀单个目标")
        logger.info("✅ 高速技能释放 - 支持1-9和Alt+1-9,0")
        logger.info("✅ 多种战斗模式 - 快速/正常/慢速")
        logger.info("✅ 智能技能策略 - 基础/Alt/混合")
        logger.info("✅ 实时战斗统计 - 击杀效率监控")
        logger.info("✅ 强化输入系统 - 多重按键发送")
        logger.info("")
        logger.info("使用说明:")
        logger.info("1. 在'战斗控制'选项卡中设置参数")
        logger.info("2. 点击'开始自动战斗'启动系统")
        logger.info("3. 系统会自动选择目标并专注击杀")
        logger.info("4. 在'系统状态'选项卡中查看统计")
        logger.info("=" * 60)
        
        # 启动应用
        sys.exit(app.exec())
        
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        print(f"\n❌ 导入失败: {e}")
        print("某些模块可能缺失，但程序会尝试继续运行")
        return False
        
    except Exception as e:
        logger.error(f"系统启动失败: {e}")
        print(f"\n❌ 系统错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 程序崩溃: {e}")
        sys.exit(1)