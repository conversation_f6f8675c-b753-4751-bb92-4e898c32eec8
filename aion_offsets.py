#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Aion游戏内存偏移量配置文件
"""

# 注意：这些偏移量需要根据游戏版本进行更新
# 以下偏移量基于特定版本的Aion游戏，可能需要根据游戏版本调整

AION_OFFSETS = {
    # 角色相关偏移量
    "character_base": {
        "pointer_path": ["0x01A5E320", 0x44, 0x24, 0x10],  # 角色基址指针链
        "hp_offset": 0xA8,       # 血量偏移
        "mp_offset": 0xAC,       # 魔法值偏移
        "level_offset": 0x64,    # 等级偏移
        "combat_state": 0x228,   # 战斗状态偏移
        "x_coord": 0x30,         # X坐标偏移
        "y_coord": 0x34,         # Y坐标偏移
        "z_coord": 0x38,         # Z坐标偏移
        "rotation": 0x40,        # 旋转角度偏移
        "target_id": 0x500,      # 当前目标ID偏移
        "class_id": 0x70,        # 职业ID偏移
        "exp_current": 0x88,     # 当前经验值偏移
        "exp_max": 0x8C,         # 升级所需经验偏移
    },
    
    # 怪物列表相关偏移量
    "monster_list": {
        "base_pointer": "0x01B45780",  # 怪物列表基址
        "offset_to_first": 0x24,       # 到第一个怪物的偏移
        "next_monster": 0x58,          # 下一个怪物的偏移
        "monster_id": 0x18,            # 怪物ID偏移
        "monster_hp": 0xA8,            # 怪物血量偏移
        "monster_level": 0x64,         # 怪物等级偏移
        "monster_type": 0x74,          # 怪物类型偏移 (0=普通, 1=精英, 2=BOSS)
        "monster_name_ptr": 0x28,      # 怪物名称指针偏移
    },
    
    # 技能相关偏移量
    "skills": {
        "cooldown_base": "0x01C56920",  # 技能冷却基址
        "skill_1_cd": 0x24,             # 技能1冷却偏移
        "skill_2_cd": 0x28,             # 技能2冷却偏移
        "skill_3_cd": 0x2C,             # 技能3冷却偏移
        "skill_4_cd": 0x30,             # 技能4冷却偏移
        "skill_5_cd": 0x34,             # 技能5冷却偏移
        "skill_6_cd": 0x38,             # 技能6冷却偏移
    },
    
    # 背包相关偏移量
    "inventory": {
        "base_pointer": "0x01A75C40",   # 背包基址
        "first_item_offset": 0x3C,      # 第一个物品偏移
        "next_item_offset": 0x10,       # 下一个物品偏移
        "item_id": 0x18,                # 物品ID偏移
        "item_count": 0x20,             # 物品数量偏移
        "item_quality": 0x24,           # 物品品质偏移
    },
    
    # UI相关偏移量
    "ui": {
        "minimap_base": "0x01D25680",   # 小地图基址
        "chatbox_base": "0x01D22340",   # 聊天框基址
        "target_frame": "0x01D18760",   # 目标框架基址
    }
}

# 技能ID映射
SKILL_IDS = {
    # 通用技能
    "attack": 1001,       # 普通攻击
    "rest": 1002,         # 休息
    "harvesting": 1003,   # 采集
    
    # 战士技能
    "slash": 2001,        # 斩击
    "shield_bash": 2002,  # 盾击
    "charge": 2003,       # 冲锋
    "provoke": 2004,      # 挑衅
    
    # 法师技能
    "fireball": 3001,     # 火球术
    "ice_spike": 3002,    # 冰刺
    "lightning": 3003,    # 闪电链
    "mana_shield": 3004,  # 法力护盾
    
    # 牧师技能
    "heal": 4001,         # 治疗术
    "buff": 4002,         # 强化祝福
    "resurrect": 4003,    # 复活
    "dispel": 4004,       # 驱散
    
    # 刺客技能
    "backstab": 5001,     # 背刺
    "poison": 5002,       # 涂毒
    "stealth": 5003,      # 潜行
    "sprint": 5004,       # 疾跑
}

# 物品ID映射
ITEM_IDS = {
    "health_potion_minor": 8001,    # 小型生命药水
    "health_potion": 8002,          # 中型生命药水
    "health_potion_major": 8003,    # 大型生命药水
    
    "mana_potion_minor": 8011,      # 小型法力药水
    "mana_potion": 8012,            # 中型法力药水
    "mana_potion_major": 8013,      # 大型法力药水
}