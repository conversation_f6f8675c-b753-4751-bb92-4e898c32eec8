#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
目标检测调试工具 - 帮助诊断和调试目标检测问题
"""

import sys
import os
import time
import cv2
import numpy as np
from PIL import ImageGrab

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def capture_and_analyze_screen():
    """截取并分析当前屏幕"""
    print("=" * 50)
    print("屏幕截图分析")
    print("=" * 50)
    
    try:
        # 截取屏幕
        screenshot = ImageGrab.grab()
        screenshot_np = np.array(screenshot)
        
        print(f"屏幕尺寸: {screenshot_np.shape}")
        
        # 转换为HSV
        hsv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2HSV)
        
        # 分析颜色分布
        analyze_colors(hsv, screenshot_np)
        
        # 保存截图用于分析
        timestamp = int(time.time())
        screenshot_path = f"debug_screenshot_{timestamp}.png"
        screenshot.save(screenshot_path)
        print(f"截图已保存: {screenshot_path}")
        
        return screenshot_np, hsv
        
    except Exception as e:
        print(f"截图分析失败: {str(e)}")
        return None, None

def analyze_colors(hsv_image, rgb_image):
    """分析图像中的颜色分布"""
    print("\n颜色分析:")
    
    # 定义要检测的颜色范围
    color_ranges = {
        '红色1': (np.array([0, 120, 120]), np.array([10, 255, 255])),
        '红色2': (np.array([170, 120, 120]), np.array([180, 255, 255])),
        '绿色': (np.array([45, 120, 120]), np.array([75, 255, 255])),
        '黄色': (np.array([20, 150, 150]), np.array([30, 255, 255])),
        '蓝色': (np.array([100, 150, 150]), np.array([140, 255, 255])),
    }
    
    for color_name, (lower, upper) in color_ranges.items():
        mask = cv2.inRange(hsv_image, lower, upper)
        pixel_count = cv2.countNonZero(mask)
        percentage = (pixel_count / (hsv_image.shape[0] * hsv_image.shape[1])) * 100
        
        print(f"  {color_name}: {pixel_count} 像素 ({percentage:.2f}%)")
        
        # 如果有足够的像素，查找轮廓
        if pixel_count > 1000:
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            large_contours = [c for c in contours if cv2.contourArea(c) > 50]
            print(f"    找到 {len(large_contours)} 个较大的{color_name}区域")

def test_target_detection():
    """测试目标检测功能"""
    print("=" * 50)
    print("目标检测测试")
    print("=" * 50)
    
    try:
        from screen_detector import ScreenDetector
        
        detector = ScreenDetector()
        
        # 测试各种检测方法
        print("1. 测试目标指示器检测...")
        target_indicator = detector.detect_target_indicator()
        print(f"   结果: {'检测到' if target_indicator else '未检测到'}")
        
        print("2. 测试伤害数字检测...")
        damage_numbers = detector.detect_damage_numbers()
        print(f"   结果: {'检测到' if damage_numbers else '未检测到'}")
        
        print("3. 测试UI元素检测...")
        ui_elements = detector.detect_ui_elements(['health_bar', 'button'])
        print(f"   结果: 检测到 {len(ui_elements)} 个UI元素")
        for element in ui_elements:
            print(f"     - {element['type']}: {element.get('location', 'N/A')}")
        
        print("4. 测试简单目标扫描...")
        target_count = detector.simple_target_scan()
        print(f"   结果: 检测到 {target_count} 个目标区域")
        
        print("5. 测试任意目标检测...")
        any_targets = detector.detect_any_targets()
        print(f"   结果: {'检测到目标' if any_targets else '未检测到目标'}")
        
        return detector
        
    except Exception as e:
        print(f"目标检测测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_tactical_ai():
    """测试战术AI目标扫描"""
    print("=" * 50)
    print("战术AI测试")
    print("=" * 50)
    
    try:
        from tactical_ai import TacticalAI
        from screen_detector import ScreenDetector
        
        # 创建模拟的内存读取器
        class MockMemoryReader:
            def __init__(self):
                self.process_id = 1234
                self.module_base = 0x400000
        
        memory_reader = MockMemoryReader()
        screen_detector = ScreenDetector()
        
        ai = TacticalAI(memory_reader, screen_detector)
        
        print("1. 扫描目标...")
        targets = ai.scan_for_targets()
        print(f"   结果: 找到 {len(targets)} 个目标")
        
        for i, target in enumerate(targets):
            print(f"     目标{i+1}: ID={target.id}, 距离={target.distance:.1f}, 优先级={target.priority.name}")
        
        if targets:
            print("2. 选择最佳目标...")
            best_target = ai.select_best_target(targets)
            if best_target:
                print(f"   最佳目标: ID={best_target.id}, 优先级={best_target.priority.name}")
            else:
                print("   未找到合适的目标")
        
        return ai, targets
        
    except Exception as e:
        print(f"战术AI测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, []

def create_test_recommendations():
    """创建测试建议"""
    print("=" * 50)
    print("调试建议")
    print("=" * 50)
    
    recommendations = [
        "1. 确保游戏窗口可见且未被遮挡",
        "2. 确保游戏中有敌人或可攻击目标",
        "3. 尝试手动选择一个目标，然后运行检测",
        "4. 检查游戏分辨率和UI缩放设置",
        "5. 确保角色在副本或有怪物的区域",
        "6. 检查是否有红色血条或目标指示器可见",
        "7. 尝试调整检测参数（颜色范围、区域大小等）"
    ]
    
    for rec in recommendations:
        print(f"  {rec}")
    
    print("\n如果问题持续存在:")
    print("  - 可以启用模拟目标模式进行测试")
    print("  - 检查日志文件获取详细错误信息")
    print("  - 调整AI参数以适应当前游戏环境")

def main():
    """主函数"""
    print("目标检测调试工具")
    print("=" * 50)
    
    # 1. 截图分析
    screenshot, hsv = capture_and_analyze_screen()
    
    # 2. 目标检测测试
    detector = test_target_detection()
    
    # 3. 战术AI测试
    ai, targets = test_tactical_ai()
    
    # 4. 创建建议
    create_test_recommendations()
    
    # 5. 总结
    print("\n" + "=" * 50)
    print("调试总结")
    print("=" * 50)
    
    if targets:
        print(f"✅ 成功检测到 {len(targets)} 个目标")
        print("   AI系统应该能够正常工作")
    else:
        print("❌ 未检测到任何目标")
        print("   建议:")
        print("   1. 检查游戏状态和环境")
        print("   2. 启用模拟目标模式进行测试")
        print("   3. 调整检测参数")
    
    # 提供快速修复选项
    print("\n快速修复选项:")
    print("1. 在tactical_ai.py中将_should_create_mock_target()返回True")
    print("2. 这将创建模拟目标用于测试AI功能")
    print("3. 确认AI逻辑正常后，再调试实际目标检测")

if __name__ == "__main__":
    main()
