"""
许可证系统集成模块
将增强的许可证系统集成到游戏增强工具中
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QGroupBox, QFormLayout, QMessageBox, QApplication
)
from PyQt6.QtCore import QTimer

# 导入增强的许可证系统
from license_system import HardwareInfo, EnhancedLicenseChecker

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('license_integration.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('license_integration')

class EnhancedActivationDialog(QDialog):
    """增强的激活对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("软件激活 - 增强版")
        self.setMinimumWidth(450)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("请输入激活码以继续使用软件")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        layout.addWidget(title_label)
        
        # 添加机器ID显示
        id_group = QGroupBox("您的机器ID")
        id_layout = QVBoxLayout()
        
        # 获取机器ID
        self.machine_id = HardwareInfo.generate_machine_id()
        
        # 显示机器ID的文本框
        id_text = QLineEdit(self.machine_id)
        id_text.setReadOnly(True)
        id_text.setStyleSheet("font-family: Consolas, monospace; font-size: 10pt;")
        id_layout.addWidget(id_text)
        
        # 添加复制按钮
        copy_button = QPushButton("复制机器ID")
        def copy_to_clipboard():
            QApplication.clipboard().setText(self.machine_id)
            copy_button.setText("已复制!")
            QTimer.singleShot(1500, lambda: copy_button.setText("复制机器ID"))
        
        copy_button.clicked.connect(copy_to_clipboard)
        id_layout.addWidget(copy_button)
        
        id_group.setLayout(id_layout)
        layout.addWidget(id_group)
        
        # 激活状态
        status_layout = QHBoxLayout()
        
        status_label = QLabel("状态:")
        status_label.setStyleSheet("font-weight: bold;")
        
        self.activation_status = QLabel("检查中...")
        self.activation_status.setStyleSheet("font-weight: bold; color: red;")
        status_layout.addWidget(status_label)
        status_layout.addWidget(self.activation_status)
        status_layout.addStretch()
        
        # 过期时间
        self.expiry_status = QLabel("")
        status_layout.addWidget(self.expiry_status)
        
        layout.addLayout(status_layout)
        
        # 激活码输入区域
        form_layout = QFormLayout()
        
        self.key_input = QLineEdit()
        self.key_input.setPlaceholderText("XXXXX-XXXXX-XXXXX-XXXXX-XXXXX")
        form_layout.addRow("激活码:", self.key_input)
        
        layout.addLayout(form_layout)
        
        # 状态消息
        self.status_label = QLabel("")
        self.status_label.setWordWrap(True)
        layout.addWidget(self.status_label)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        activate_btn = QPushButton("激活")
        activate_btn.clicked.connect(self.activate_software)
        
        self.continue_btn = QPushButton("继续使用")
        self.continue_btn.clicked.connect(self.accept)
        self.continue_btn.setEnabled(False)  # 默认禁用，只有在已激活时才启用
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(activate_btn)
        button_layout.addWidget(self.continue_btn)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
        
        # 检查激活状态
        self.check_activation()
    
    def check_activation(self):
        """检查当前激活状态"""
        is_activated, message, expiry_date = EnhancedLicenseChecker.check_activation()
        
        if is_activated:
            self.activation_status.setText("已激活")
            self.activation_status.setStyleSheet("font-weight: bold; color: green;")
            
            if expiry_date:
                self.expiry_status.setText(f"有效期至: {expiry_date.strftime('%Y-%m-%d')}")
            
            # 启用继续按钮
            self.continue_btn.setEnabled(True)
            
            # 尝试填充当前的激活码
            try:
                # 基于机器ID生成解密密钥
                key = EnhancedLicenseChecker.generate_encryption_key(self.machine_id)
                
                # 读取加密的激活信息
                with open(EnhancedLicenseChecker.LICENSE_FILE, "rb") as f:
                    encrypted_data = f.read()
                
                # 尝试解密
                activation_info = EnhancedLicenseChecker.decrypt_data(encrypted_data, key)
                
                if activation_info and "activation_key" in activation_info:
                    self.key_input.setText(activation_info["activation_key"])
            except Exception as e:
                logger.error(f"读取激活信息出错: {str(e)}")
        else:
            self.activation_status.setText("未激活")
            self.activation_status.setStyleSheet("font-weight: bold; color: red;")
            self.expiry_status.setText(message)
            self.continue_btn.setEnabled(False)
    
    def activate_software(self):
        """激活软件"""
        activation_key = self.key_input.text().strip()
        
        if not activation_key:
            self.status_label.setText("请输入激活码")
            self.status_label.setStyleSheet("color: red;")
            return
        
        # 验证激活码格式
        parts = activation_key.split('-')
        if len(parts) != 5 or any(len(part) != 5 for part in parts):
            self.status_label.setText("无效的激活码格式 (应为: XXXXX-XXXXX-XXXXX-XXXXX-XXXXX)")
            self.status_label.setStyleSheet("color: red;")
            return
        
        # 激活软件
        is_success, message = EnhancedLicenseChecker.activate(activation_key)
        
        if is_success:
            self.status_label.setText(message)
            self.status_label.setStyleSheet("color: green;")
            
            # 重新检查激活状态
            self.check_activation()
        else:
            self.status_label.setText(message)
            self.status_label.setStyleSheet("color: red;")


def check_license_status():
    """检查许可证状态，如果未激活或已过期则显示激活对话框"""
    is_activated, message, expiry_date = EnhancedLicenseChecker.check_activation()
    
    if not is_activated or (expiry_date and datetime.now() > expiry_date):
        # 创建并显示激活对话框
        dialog = EnhancedActivationDialog()
        result = dialog.exec()
        
        # 如果用户取消，则退出应用
        if result != QDialog.DialogCode.Accepted:
            QMessageBox.critical(None, "激活失败", 
                               "软件未激活，程序将退出。")
            QApplication.quit()
            sys.exit(0)
        
        # 再次检查激活状态
        is_activated, message, expiry_date = EnhancedLicenseChecker.check_activation()
        if not is_activated:
            QMessageBox.critical(None, "激活失败", 
                               "软件未激活，程序将退出。")
            QApplication.quit()
            sys.exit(0)
    
    return is_activated, message, expiry_date


def setup_periodic_check(main_window, interval_minutes=30):
    """设置定期检查激活状态的定时器"""
    timer = QTimer(main_window)
    
    def check_activation_status():
        """定期检查激活状态的回调函数"""
        is_activated, message, expiry_date = EnhancedLicenseChecker.periodic_check()
        
        if not is_activated or (expiry_date and datetime.now() > expiry_date):
            QMessageBox.critical(main_window, "激活已过期", 
                               f"您的软件许可已失效。\n\n原因: {message}\n\n"
                               "请重新激活软件。")
            
            # 显示激活对话框
            dialog = EnhancedActivationDialog(main_window)
            result = dialog.exec()
            
            # 如果用户取消，则退出应用
            if result != QDialog.DialogCode.Accepted:
                main_window.close()
                QApplication.quit()
    
    # 设置定时器
    timer.timeout.connect(check_activation_status)
    timer.start(interval_minutes * 60 * 1000)  # 转换为毫秒
    
    return timer


# 示例：如何在主程序中使用
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 检查许可证状态
    is_activated, message, expiry_date = check_license_status()
    
    if is_activated:
        # 这里通常会创建主窗口并显示
        # main_window = MainWindow()
        # main_window.show()
        
        # 设置定期检查
        # timer = setup_periodic_check(main_window)
        
        print("软件已激活，可以继续使用")
        if expiry_date:
            print(f"有效期至: {expiry_date.strftime('%Y-%m-%d')}")
    
    sys.exit(app.exec())
