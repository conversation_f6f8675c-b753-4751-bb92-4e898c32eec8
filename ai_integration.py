#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI集成模块 - 将增强战斗AI集成到main.py中
"""

import sys
import os
import logging
from enhanced_battle_ai import EnhancedBattleAI, AttackIntention, BattleState

logger = logging.getLogger('ai_integration')

def integrate_enhanced_ai_to_main():
    """将增强AI集成到main.py的代码片段"""
    
    integration_code = '''
# ==================== 增强战斗AI集成代码 ====================

# 在main.py的导入部分添加
from enhanced_battle_ai import EnhancedBattleAI, AttackIntention, BattleState

# 在main()函数中替换原有的battle_manager创建代码
def main():
    global battle_manager, memory_helper, current_version
    app = QApplication(sys.argv)
    
    # 设置默认游戏版本
    current_version = "4.5核心8880版[64位]"
    update_offsets(current_version)
    
    # 创建组件
    memory = MemoryReader()
    input_sim = InputSimulator()
    monster_selector = MonsterSelector(memory)
    
    # 使用增强战斗AI替换原有的BattleManager
    battle_manager = EnhancedBattleAI(memory, input_sim, monster_selector)
    
    # 创建主窗口
    window = MainWindow(memory, battle_manager)
    window.show()
    
    # 设置游戏动作日志回调
    battle_manager.set_log_callback(window.add_game_log)
    
    # 设置更新定时器 (每100ms更新一次)
    update_timer = QTimer()
    update_timer.timeout.connect(lambda: battle_manager._think() if battle_manager.is_active else None)
    update_timer.start(100)
    
    # 注册全局热键
    if not register_global_hotkey(int(window.winId())):
        register_keyboard_hotkey()
    
    register_all_hotkeys()
    
    sys.exit(app.exec())

# ==================== 增强UI控制代码 ====================

# 在MainWindow类中添加增强AI控制方法
class MainWindow(QMainWindow):
    
    def create_enhanced_dungeon_tab(self):
        """创建增强副本辅助选项卡"""
        dungeon_tab = QWidget()
        dungeon_layout = QVBoxLayout(dungeon_tab)
        
        # AI状态显示卡片
        ai_status_card = FeatureCard(
            "增强AI状态", 
            "基于Aion服务端AI逻辑的智能战斗系统"
        )
        
        # AI状态显示区域
        status_layout = QGridLayout()
        
        # 战斗状态
        self.ai_battle_state_label = QLabel("战斗状态: 待机")
        self.ai_battle_state_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        status_layout.addWidget(QLabel("战斗状态:"), 0, 0)
        status_layout.addWidget(self.ai_battle_state_label, 0, 1)
        
        # 当前目标
        self.ai_current_target_label = QLabel("当前目标: 无")
        status_layout.addWidget(QLabel("当前目标:"), 1, 0)
        status_layout.addWidget(self.ai_current_target_label, 1, 1)
        
        # 目标数量
        self.ai_target_count_label = QLabel("附近目标: 0")
        status_layout.addWidget(QLabel("附近目标:"), 2, 0)
        status_layout.addWidget(self.ai_target_count_label, 2, 1)
        
        # 技能就绪数
        self.ai_skills_ready_label = QLabel("就绪技能: 0/18")
        status_layout.addWidget(QLabel("就绪技能:"), 3, 0)
        status_layout.addWidget(self.ai_skills_ready_label, 3, 1)
        
        ai_status_card.buttons_layout.addLayout(status_layout)
        dungeon_layout.addWidget(ai_status_card)
        
        # AI设置卡片
        ai_settings_card = FeatureCard(
            "AI设置", 
            "调整增强AI的战斗参数"
        )
        
        settings_layout = QFormLayout()
        
        # 攻击范围设置
        self.ai_attack_range_slider = QSlider(Qt.Orientation.Horizontal)
        self.ai_attack_range_slider.setRange(10, 50)
        self.ai_attack_range_slider.setValue(30)
        self.ai_attack_range_slider.valueChanged.connect(self.update_ai_settings)
        self.ai_attack_range_label = QLabel("30")
        
        range_layout = QHBoxLayout()
        range_layout.addWidget(self.ai_attack_range_slider)
        range_layout.addWidget(self.ai_attack_range_label)
        settings_layout.addRow("攻击范围:", range_layout)
        
        # 治疗阈值设置
        self.ai_heal_threshold_slider = QSlider(Qt.Orientation.Horizontal)
        self.ai_heal_threshold_slider.setRange(10, 80)
        self.ai_heal_threshold_slider.setValue(50)
        self.ai_heal_threshold_slider.valueChanged.connect(self.update_ai_settings)
        self.ai_heal_threshold_label = QLabel("50%")
        
        heal_layout = QHBoxLayout()
        heal_layout.addWidget(self.ai_heal_threshold_slider)
        heal_layout.addWidget(self.ai_heal_threshold_label)
        settings_layout.addRow("治疗阈值:", heal_layout)
        
        # 目标选择算法
        self.ai_target_algorithm_combo = QComboBox()
        self.ai_target_algorithm_combo.addItems([
            "最高仇恨优先", "最近距离优先", "最低血量优先"
        ])
        self.ai_target_algorithm_combo.currentTextChanged.connect(self.update_ai_settings)
        settings_layout.addRow("目标选择:", self.ai_target_algorithm_combo)
        
        ai_settings_card.buttons_layout.addLayout(settings_layout)
        dungeon_layout.addWidget(ai_settings_card)
        
        # 控制按钮卡片
        control_card = FeatureCard(
            "AI控制", 
            "启动/停止增强战斗AI"
        )
        
        control_layout = QHBoxLayout()
        
        self.start_enhanced_ai_btn = QPushButton("启动增强AI")
        self.start_enhanced_ai_btn.clicked.connect(self.toggle_enhanced_ai)
        self.start_enhanced_ai_btn.setStyleSheet(AppStyles().success_button)
        control_layout.addWidget(self.start_enhanced_ai_btn)
        
        self.stop_enhanced_ai_btn = QPushButton("停止增强AI")
        self.stop_enhanced_ai_btn.clicked.connect(self.toggle_enhanced_ai)
        self.stop_enhanced_ai_btn.setEnabled(False)
        self.stop_enhanced_ai_btn.setStyleSheet(AppStyles().danger_button)
        control_layout.addWidget(self.stop_enhanced_ai_btn)
        
        # 紧急停止按钮
        self.emergency_stop_btn = QPushButton("紧急停止")
        self.emergency_stop_btn.clicked.connect(self.emergency_stop_ai)
        self.emergency_stop_btn.setStyleSheet(AppStyles().warning_button)
        control_layout.addWidget(self.emergency_stop_btn)
        
        control_card.buttons_layout.addLayout(control_layout)
        dungeon_layout.addWidget(control_card)
        
        # AI日志卡片
        ai_log_card = FeatureCard(
            "AI日志", 
            "增强AI的决策和行动日志"
        )
        
        self.ai_log_display = QTextEdit()
        self.ai_log_display.setMaximumHeight(200)
        self.ai_log_display.setReadOnly(True)
        self.ai_log_display.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #00ff00;
                font-family: 'Consolas', monospace;
                font-size: 10px;
                border: 1px solid #333;
                border-radius: 5px;
            }
        """)
        
        ai_log_card.buttons_layout.addWidget(self.ai_log_display)
        dungeon_layout.addWidget(ai_log_card)
        
        dungeon_layout.addStretch()
        return dungeon_tab
    
    def toggle_enhanced_ai(self):
        """切换增强AI状态"""
        if not self.memory.is_connected():
            QMessageBox.warning(self, "警告", "请先连接到游戏进程！")
            return
        
        if hasattr(self, 'battle_manager') and isinstance(self.battle_manager, EnhancedBattleAI):
            if self.battle_manager.is_active:
                # 停止AI
                self.battle_manager.stop()
                self.start_enhanced_ai_btn.setEnabled(True)
                self.stop_enhanced_ai_btn.setEnabled(False)
                self.add_game_log("增强AI已停止")
            else:
                # 启动AI
                self.battle_manager.start()
                self.start_enhanced_ai_btn.setEnabled(False)
                self.stop_enhanced_ai_btn.setEnabled(True)
                self.add_game_log("增强AI已启动")
    
    def emergency_stop_ai(self):
        """紧急停止AI"""
        if hasattr(self, 'battle_manager'):
            self.battle_manager.stop()
            self.start_enhanced_ai_btn.setEnabled(True)
            self.stop_enhanced_ai_btn.setEnabled(False)
            self.add_game_log("紧急停止AI - 所有操作已终止")
    
    def update_ai_settings(self):
        """更新AI设置"""
        if not hasattr(self, 'battle_manager'):
            return
        
        # 更新攻击范围
        attack_range = self.ai_attack_range_slider.value()
        self.ai_attack_range_label.setText(str(attack_range))
        
        # 更新治疗阈值
        heal_threshold = self.ai_heal_threshold_slider.value()
        self.ai_heal_threshold_label.setText(f"{heal_threshold}%")
        
        # 更新目标算法
        algorithm_map = {
            "最高仇恨优先": "most_hated",
            "最近距离优先": "nearest",
            "最低血量优先": "weakest"
        }
        algorithm = algorithm_map.get(
            self.ai_target_algorithm_combo.currentText(), 
            "most_hated"
        )
        
        # 应用设置
        settings = {
            "attack_range": attack_range,
            "heal_threshold": heal_threshold,
            "target_algorithm": algorithm
        }
        
        if isinstance(self.battle_manager, EnhancedBattleAI):
            self.battle_manager.update_settings(settings)
    
    def update_ai_status_display(self):
        """更新AI状态显示"""
        if not hasattr(self, 'battle_manager') or not isinstance(self.battle_manager, EnhancedBattleAI):
            return
        
        try:
            status = self.battle_manager.get_status()
            
            # 更新战斗状态
            state_colors = {
                "idle": "#888888",
                "searching": "#FFC107",
                "engaging": "#FF9800",
                "fighting": "#F44336",
                "retreating": "#9C27B0"
            }
            
            state_text = {
                "idle": "待机",
                "searching": "搜索目标",
                "engaging": "接敌中",
                "fighting": "战斗中",
                "retreating": "撤退中"
            }
            
            battle_state = status.get("battle_state", "idle")
            color = state_colors.get(battle_state, "#888888")
            text = state_text.get(battle_state, "未知")
            
            self.ai_battle_state_label.setText(text)
            self.ai_battle_state_label.setStyleSheet(f"color: {color}; font-weight: bold;")
            
            # 更新其他状态
            self.ai_current_target_label.setText(status.get("current_target", "无"))
            self.ai_target_count_label.setText(str(status.get("target_count", 0)))
            self.ai_skills_ready_label.setText(f"{status.get('skills_ready', 0)}/18")
            
        except Exception as e:
            logger.error(f"更新AI状态显示错误: {e}")
    
    def add_ai_log(self, message: str):
        """添加AI日志"""
        if hasattr(self, 'ai_log_display'):
            timestamp = time.strftime("%H:%M:%S")
            log_message = f"[{timestamp}] {message}"
            self.ai_log_display.append(log_message)
            
            # 限制日志行数
            if self.ai_log_display.document().blockCount() > 100:
                cursor = self.ai_log_display.textCursor()
                cursor.movePosition(cursor.MoveOperation.Start)
                cursor.select(cursor.SelectionType.BlockUnderCursor)
                cursor.removeSelectedText()

# ==================== 热键集成代码 ====================

# 在register_all_hotkeys()函数中添加增强AI热键
def register_enhanced_ai_hotkeys():
    """注册增强AI热键"""
    try:
        # F8 - 切换增强AI
        keyboard.add_hotkey('f8', lambda: toggle_enhanced_ai_hotkey())
        
        # Ctrl+F8 - 紧急停止
        keyboard.add_hotkey('ctrl+f8', lambda: emergency_stop_ai_hotkey())
        
        logger.info("增强AI热键注册成功")
    except Exception as e:
        logger.error(f"注册增强AI热键失败: {e}")

def toggle_enhanced_ai_hotkey():
    """增强AI热键切换"""
    global battle_manager
    if battle_manager and isinstance(battle_manager, EnhancedBattleAI):
        if battle_manager.is_active:
            battle_manager.stop()
            logger.info("通过热键停止增强AI")
        else:
            battle_manager.start()
            logger.info("通过热键启动增强AI")

def emergency_stop_ai_hotkey():
    """紧急停止AI热键"""
    global battle_manager
    if battle_manager:
        battle_manager.stop()
        logger.info("通过热键紧急停止AI")

# ==================== 配置文件集成 ====================

# 增强AI配置
ENHANCED_AI_CONFIG = {
    "attack_range": 30.0,
    "heal_threshold": 50.0,
    "retreat_threshold": 20.0,
    "target_algorithm": "most_hated",
    "skill_priority": "damage_first",
    "auto_buff": True,
    "auto_heal": True,
    "think_interval": 0.1,
    "global_cooldown": 0.05
}

def save_enhanced_ai_config():
    """保存增强AI配置"""
    try:
        with open("enhanced_ai_config.json", "w", encoding="utf-8") as f:
            json.dump(ENHANCED_AI_CONFIG, f, indent=2, ensure_ascii=False)
        logger.info("增强AI配置已保存")
    except Exception as e:
        logger.error(f"保存增强AI配置失败: {e}")

def load_enhanced_ai_config():
    """加载增强AI配置"""
    global ENHANCED_AI_CONFIG
    try:
        if os.path.exists("enhanced_ai_config.json"):
            with open("enhanced_ai_config.json", "r", encoding="utf-8") as f:
                ENHANCED_AI_CONFIG.update(json.load(f))
            logger.info("增强AI配置已加载")
    except Exception as e:
        logger.error(f"加载增强AI配置失败: {e}")
'''
    
    return integration_code

def create_integration_guide():
    """创建集成指南"""
    guide = """
# 增强战斗AI集成指南

## 1. 文件准备
确保以下文件存在于私服垃圾文件夹中：
- enhanced_battle_ai.py (增强战斗AI核心)
- ai_integration.py (本文件)

## 2. main.py集成步骤

### 步骤1: 添加导入
在main.py文件顶部的导入部分添加：
```python
from enhanced_battle_ai import EnhancedBattleAI, AttackIntention, BattleState
```

### 步骤2: 替换BattleManager
在main()函数中，将原有的：
```python
battle_manager = BattleManager(memory, input_sim, monster_selector)
```
替换为：
```python
battle_manager = EnhancedBattleAI(memory, input_sim, monster_selector)
```

### 步骤3: 更新UI
在MainWindow类中添加增强AI的UI控制方法（参考integration_code）

### 步骤4: 添加热键支持
在热键注册部分添加增强AI的热键支持

## 3. 功能特性

### 核心AI功能
- 基于Aion服务端AI逻辑的智能决策
- 自动目标选择和切换
- 智能技能释放顺序
- 自动治疗和buff管理
- 仇恨值管理系统

### 技能系统
- 支持1-9基础技能
- 支持Alt+1到Alt+9高级技能
- 智能技能冷却管理
- 技能优先级算法

### 目标系统
- 多种目标选择算法
- 智能威胁评估
- 自动目标切换
- 距离和血量优化

### 安全功能
- 紧急停止机制
- 血量监控和自动撤退
- 异常处理和恢复

## 4. 使用方法

### 基本使用
1. 启动程序并连接游戏进程
2. 在"副本辅助"选项卡中配置AI参数
3. 点击"启动增强AI"开始自动战斗
4. 使用F8热键快速切换AI状态

### 高级设置
- 攻击范围: 10-50米可调
- 治疗阈值: 10-80%可调
- 目标算法: 仇恨/距离/血量优先
- 技能优先级: 伤害/冷却时间优先

### 热键操作
- F8: 启动/停止增强AI
- Ctrl+F8: 紧急停止所有AI操作
- Home: 显示/隐藏主窗口

## 5. 故障排除

### 常见问题
1. AI无法启动 -> 检查游戏进程连接
2. 技能不释放 -> 检查技能键位设置
3. 目标选择异常 -> 调整目标算法
4. 性能问题 -> 调整思考间隔

### 日志调试
- 查看AI日志窗口获取详细信息
- 检查game_memory_tool.log文件
- 启用调试模式获取更多信息

## 6. 性能优化

### 推荐设置
- 思考间隔: 100ms (平衡性能和响应)
- 全局冷却: 50ms (极速技能释放)
- 目标更新: 每次思考循环
- 日志级别: INFO (生产环境)

### 系统要求
- CPU: 双核以上
- 内存: 4GB以上
- 显卡: 支持DirectX 11
- 操作系统: Windows 10/11

## 7. 更新和维护

### 配置备份
- 定期备份enhanced_ai_config.json
- 记录个人优化的参数设置
- 保存成功的战斗模式配置

### 版本更新
- 关注新版本的功能改进
- 测试新算法的稳定性
- 反馈使用中的问题和建议
"""
    
    return guide

if __name__ == "__main__":
    print("增强战斗AI集成模块")
    print("=" * 50)
    
    # 生成集成代码
    integration_code = integrate_enhanced_ai_to_main()
    
    # 保存集成代码到文件
    with open("integration_code.txt", "w", encoding="utf-8") as f:
        f.write(integration_code)
    
    # 生成集成指南
    guide = create_integration_guide()
    
    # 保存集成指南到文件
    with open("integration_guide.md", "w", encoding="utf-8") as f:
        f.write(guide)
    
    print("✅ 集成代码已生成: integration_code.txt")
    print("✅ 集成指南已生成: integration_guide.md")
    print("✅ 增强战斗AI已准备就绪: enhanced_battle_ai.py")
    
    print("\n🚀 下一步操作:")
    print("1. 查看 integration_guide.md 了解详细集成步骤")
    print("2. 按照指南修改 main.py 文件")
    print("3. 测试增强AI功能")
    print("4. 享受智能化的副本辅助体验！")