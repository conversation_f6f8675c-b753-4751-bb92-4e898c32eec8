#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
新功能测试脚本 - 测试自动范围打怪和手动点怪自动技能功能
"""

import sys
import os
import time
import logging

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_auto_aoe_feature():
    """测试自动范围打怪功能"""
    print("=" * 60)
    print("自动范围打怪功能测试")
    print("=" * 60)
    
    try:
        from battle_logic import BattleManager
        from memory_reader import MemoryReader
        from input_simulator import InputSimulator
        from monster_select import MonsterSelector
        from tactical_ai import Target
        
        # 创建组件
        memory = MemoryReader()
        input_sim = InputSimulator()
        monster_selector = MonsterSelector(memory)
        battle_manager = BattleManager(memory, input_sim, monster_selector)
        
        print("✓ 战斗管理器初始化成功")
        
        # 启用自动范围打怪
        battle_manager.toggle_ai_mode(True)
        battle_manager.toggle_auto_aoe(True)
        battle_manager.is_active = True
        
        print(f"  - 自动范围打怪: {battle_manager.auto_aoe_enabled}")
        print(f"  - 范围技能: {battle_manager.aoe_skills}")
        print(f"  - 最少目标数: {battle_manager.aoe_min_targets}")
        print(f"  - 技能间隔: {battle_manager.aoe_skill_interval}秒")
        
        # 测试范围技能设置
        print("\n测试范围技能设置:")
        test_skills = ["2,4,7", ["3", "6", "9"], "1,5,8,9"]
        for skills in test_skills:
            battle_manager.set_aoe_skills(skills)
            print(f"  设置技能 {skills} -> 实际: {battle_manager.aoe_skills}")
        
        # 测试最少目标数设置
        print("\n测试最少目标数设置:")
        for min_targets in [1, 3, 5, 8]:
            battle_manager.set_aoe_min_targets(min_targets)
            print(f"  设置最少目标 {min_targets} -> 实际: {battle_manager.aoe_min_targets}")
        
        # 创建多个测试目标
        test_targets = []
        for i in range(6):
            target = Target(
                id=4000 + i,
                name=f"范围目标{i+1}",
                level=50,
                hp_percent=0.8,
                distance=12.0 + i,
                position=(900 + i * 30, 500),
                last_seen=time.time(),
                threat_level=2
            )
            test_targets.append(target)
        
        print(f"\n创建了 {len(test_targets)} 个测试目标")
        
        # 模拟战术AI的目标扫描
        if battle_manager.tactical_ai:
            # 模拟扫描结果
            original_scan = battle_manager.tactical_ai.scan_for_targets
            battle_manager.tactical_ai.scan_for_targets = lambda: test_targets
        
        # 测试范围技能使用
        print("\n测试范围技能使用:")
        for round_num in range(5):
            print(f"\n--- 第 {round_num + 1} 轮测试 ---")
            
            # 测试不同目标数量
            target_count = 2 + round_num
            current_targets = test_targets[:target_count]
            
            # 更新扫描结果
            if battle_manager.tactical_ai:
                battle_manager.tactical_ai.scan_for_targets = lambda: current_targets
            
            print(f"当前目标数量: {len(current_targets)}")
            
            # 检查并使用范围技能
            result = battle_manager.check_and_use_aoe_skills()
            
            if result:
                print(f"✓ 成功使用范围技能")
            else:
                print(f"○ 未使用范围技能 (目标数量不足或技能冷却中)")
            
            time.sleep(0.5)
        
        return True
        
    except Exception as e:
        print(f"✗ 自动范围打怪测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_target_auto_skill():
    """测试手动点怪自动技能功能"""
    print("\n" + "=" * 60)
    print("手动点怪自动技能功能测试")
    print("=" * 60)
    
    try:
        from battle_logic import BattleManager
        from memory_reader import MemoryReader
        from input_simulator import InputSimulator
        from monster_select import MonsterSelector
        from tactical_ai import Target
        
        # 创建组件
        memory = MemoryReader()
        input_sim = InputSimulator()
        monster_selector = MonsterSelector(memory)
        battle_manager = BattleManager(memory, input_sim, monster_selector)
        
        # 启用手动点怪自动技能
        battle_manager.toggle_ai_mode(True)
        battle_manager.toggle_manual_target_auto_skill(True)
        battle_manager.is_active = True
        
        print("✓ 手动点怪自动技能已启用")
        print(f"  - 功能状态: {battle_manager.manual_target_auto_skill}")
        print(f"  - 自动技能选择: {battle_manager.auto_skill_for_manual_target}")
        
        # 测试手动目标设置
        print("\n测试手动目标设置:")
        
        # 创建测试目标
        manual_target = Target(
            id=5001,
            name="手动选择的BOSS",
            level=55,
            hp_percent=0.9,
            distance=18.0,
            position=(960, 540),
            last_seen=time.time(),
            threat_level=4,
            is_boss=True
        )
        
        print(f"创建手动目标: {manual_target.name}")
        
        # 强制设置手动目标
        battle_manager.force_manual_target(manual_target.id)
        print(f"手动目标ID: {battle_manager.manual_target_id}")
        print(f"手动目标检测状态: {battle_manager.manual_target_detected}")
        
        # 测试为手动目标自动释放技能
        print("\n测试自动技能释放:")
        for round_num in range(8):
            print(f"\n--- 第 {round_num + 1} 轮技能释放 ---")
            
            # 处理手动目标自动技能
            result = battle_manager.handle_manual_target_auto_skill()
            
            if result:
                print(f"✓ 为手动目标自动释放技能")
            else:
                print(f"○ 未释放技能 (冷却中或其他原因)")
            
            time.sleep(0.2)
        
        # 测试手动目标对象创建
        print("\n测试手动目标对象创建:")
        manual_target_obj = battle_manager._create_manual_target_object()
        
        if manual_target_obj:
            print(f"✓ 成功创建手动目标对象")
            print(f"  - 目标名称: {manual_target_obj.name}")
            print(f"  - 目标ID: {manual_target_obj.id}")
            print(f"  - 威胁等级: {manual_target_obj.threat_level}")
        else:
            print(f"✗ 创建手动目标对象失败")
        
        # 测试清除手动目标
        print("\n测试清除手动目标:")
        battle_manager.manual_target_detected = False
        battle_manager.manual_target_id = 0
        print(f"手动目标已清除")
        print(f"手动目标检测状态: {battle_manager.manual_target_detected}")
        
        return True
        
    except Exception as e:
        print(f"✗ 手动点怪自动技能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_integration():
    """测试功能集成"""
    print("\n" + "=" * 60)
    print("功能集成测试")
    print("=" * 60)
    
    try:
        from battle_logic import BattleManager
        from memory_reader import MemoryReader
        from input_simulator import InputSimulator
        from monster_select import MonsterSelector
        
        # 创建组件
        memory = MemoryReader()
        input_sim = InputSimulator()
        monster_selector = MonsterSelector(memory)
        battle_manager = BattleManager(memory, input_sim, monster_selector)
        
        # 启用所有功能
        battle_manager.toggle_ai_mode(True)
        battle_manager.toggle_auto_attack(True)
        battle_manager.toggle_focus_kill_mode(True)
        battle_manager.toggle_auto_aoe(True)
        battle_manager.toggle_manual_target_auto_skill(True)
        battle_manager.is_active = True
        
        print("✓ 所有功能已启用")
        
        # 测试功能优先级
        print("\n测试功能优先级:")
        print("1. 自动范围打怪 (最高优先级)")
        print("2. 手动点怪自动技能")
        print("3. 自动攻击周围怪物")
        print("4. 专注击杀模式")
        
        # 模拟战斗状态更新
        print("\n模拟战斗状态更新:")
        for i in range(3):
            print(f"\n--- 战斗更新 {i+1} ---")
            
            # 模拟战斗状态更新
            battle_manager.update_battle_state()
            
            print(f"自动范围打怪: {'启用' if battle_manager.auto_aoe_enabled else '禁用'}")
            print(f"手动目标自动技能: {'启用' if battle_manager.manual_target_auto_skill else '禁用'}")
            print(f"自动攻击: {'启用' if battle_manager.auto_attack_enabled else '禁用'}")
            print(f"专注击杀: {'启用' if battle_manager.focus_kill_mode else '禁用'}")
            
            time.sleep(0.3)
        
        return True
        
    except Exception as e:
        print(f"✗ 功能集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_new_features():
    """显示新功能特性"""
    print("\n" + "=" * 60)
    print("新功能特性介绍")
    print("=" * 60)
    
    print("🌀 自动范围打怪功能:")
    print("  ✅ 自动检测周围目标数量")
    print("  ✅ 智能选择最佳范围技能")
    print("  ✅ 可配置范围技能列表")
    print("  ✅ 可设置最少目标数量")
    print("  ✅ 技能冷却时间管理")
    print("  ✅ 与其他功能协调工作")
    
    print("\n👆 手动点怪自动技能功能:")
    print("  ✅ 检测手动选择的目标")
    print("  ✅ AI自动选择最佳技能")
    print("  ✅ 智能技能释放时机")
    print("  ✅ 手动目标管理")
    print("  ✅ 一键设置/清除功能")
    print("  ✅ 与AI决策系统集成")
    
    print("\n🎮 使用场景:")
    print("  🌀 范围打怪: 适合刷怪、清理小怪群")
    print("  👆 手动技能: 适合打BOSS、精英怪")
    print("  🔄 功能切换: 可根据情况灵活切换")
    print("  ⚙️ 参数调节: 可根据需要调整参数")
    
    print("\n📊 功能优先级:")
    print("  1️⃣ 自动范围打怪 (最高)")
    print("  2️⃣ 手动点怪自动技能")
    print("  3️⃣ 自动攻击周围怪物")
    print("  4️⃣ 专注击杀模式")

def main():
    """主测试函数"""
    print("新功能测试 - 自动范围打怪 & 手动点怪自动技能")
    print("=" * 60)
    
    # 执行测试
    test_results = []
    
    test_results.append(("自动范围打怪功能", test_auto_aoe_feature()))
    test_results.append(("手动点怪自动技能", test_manual_target_auto_skill()))
    test_results.append(("功能集成测试", test_feature_integration()))
    
    # 显示功能特性
    show_new_features()
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 新功能实现成功！")
        print("\n现在您的副本辅助具有:")
        print("🌀 自动范围打怪 - 智能使用范围技能")
        print("👆 手动点怪自动技能 - 手动选择+AI技能")
        print("🎯 专注击杀模式 - 一个一个清理")
        print("⚔️ 自动攻击周围怪物 - 全自动战斗")
        print("🧠 AI智能决策 - 最佳技能选择")
        
        print("\n使用方法:")
        print("1. 重新启动程序")
        print("2. 在'AI增强'选项卡中启用相应功能")
        print("3. 根据需要调整参数设置")
        print("4. 开始副本辅助")
        print("5. 享受多样化的战斗模式")
    else:
        print("⚠ 部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
