"""
反调试和反篡改技术
防止软件被逆向工程和调试
"""

import os
import sys
import time
import ctypes
import platform
import threading
import random
import hashlib
import logging
import traceback
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('anti_debug.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('anti_debug')

# Windows API常量
if platform.system() == "Windows":
    try:
        kernel32 = ctypes.windll.kernel32
        user32 = ctypes.windll.user32
        ntdll = ctypes.windll.ntdll
        
        # 调试相关常量
        DEBUG_PROCESS = 0x00000001
        DEBUG_ONLY_THIS_PROCESS = 0x00000002
        CREATE_SUSPENDED = 0x00000004
        PROCESS_ALL_ACCESS = 0x001F0FFF
        
        # 异常常量
        EXCEPTION_DEBUG_EVENT = 1
        EXCEPTION_BREAKPOINT = 0x80000003
        EXCEPTION_SINGLE_STEP = 0x80000004
        
        # 内存保护常量
        PAGE_EXECUTE_READWRITE = 0x40
        PAGE_READWRITE = 0x04
        
        # 窗口常量
        SW_HIDE = 0
        SW_SHOW = 5
    except Exception as e:
        logger.error(f"加载Windows API出错: {str(e)}")


class AntiDebug:
    """反调试技术集合"""
    
    def __init__(self):
        self.is_debugging = False
        self.debug_counter = 0
        self.integrity_checks = {}
        self.last_check_time = datetime.now()
        self.check_interval = 2.0  # 秒
        self.random_interval = True
        self.debug_detected_callback = None
        self.tampering_detected_callback = None
    
    def set_debug_callback(self, callback):
        """设置调试检测回调函数"""
        self.debug_detected_callback = callback
    
    def set_tampering_callback(self, callback):
        """设置篡改检测回调函数"""
        self.tampering_detected_callback = callback
    
    def _debug_detected(self, technique, details=None):
        """调试检测处理"""
        self.is_debugging = True
        self.debug_counter += 1
        
        logger.warning(f"检测到调试尝试! 技术: {technique}, 详情: {details}")
        
        if self.debug_detected_callback:
            try:
                self.debug_detected_callback(technique, details)
            except Exception as e:
                logger.error(f"调试检测回调出错: {str(e)}")
    
    def _tampering_detected(self, component, details=None):
        """篡改检测处理"""
        logger.warning(f"检测到篡改尝试! 组件: {component}, 详情: {details}")
        
        if self.tampering_detected_callback:
            try:
                self.tampering_detected_callback(component, details)
            except Exception as e:
                logger.error(f"篡改检测回调出错: {str(e)}")
    
    def check_debugger_windows(self):
        """检查常见调试器窗口"""
        if platform.system() != "Windows":
            return False
        
        debugger_windows = [
            "OLLYDBG",
            "x64dbg",
            "x32dbg",
            "ida",
            "ida64",
            "immunity debugger",
            "cheat engine",
            "process hacker",
            "process monitor",
            "process explorer",
            "pestudio",
            "wireshark",
            "fiddler",
            "dnspy",
            "ghidra"
        ]
        
        for window in debugger_windows:
            try:
                hwnd = user32.FindWindowA(None, window.encode('utf-8'))
                if hwnd:
                    self._debug_detected("窗口检测", f"发现调试器窗口: {window}")
                    return True
                
                # 不区分大小写的检查
                hwnd = user32.FindWindowA(None, window.upper().encode('utf-8'))
                if hwnd:
                    self._debug_detected("窗口检测", f"发现调试器窗口: {window.upper()}")
                    return True
            except Exception as e:
                logger.error(f"窗口检测出错: {str(e)}")
        
        return False
    
    def check_debugger_isdebuggerpresent(self):
        """使用IsDebuggerPresent API检测调试器"""
        if platform.system() != "Windows":
            return False
        
        try:
            if kernel32.IsDebuggerPresent():
                self._debug_detected("IsDebuggerPresent", "API返回True")
                return True
        except Exception as e:
            logger.error(f"IsDebuggerPresent检测出错: {str(e)}")
        
        return False
    
    def check_debugger_checkremotedebuggerpresent(self):
        """使用CheckRemoteDebuggerPresent API检测调试器"""
        if platform.system() != "Windows":
            return False
        
        try:
            is_debugged = ctypes.c_bool()
            if kernel32.CheckRemoteDebuggerPresent(kernel32.GetCurrentProcess(), ctypes.byref(is_debugged)) and is_debugged.value:
                self._debug_detected("CheckRemoteDebuggerPresent", "API返回True")
                return True
        except Exception as e:
            logger.error(f"CheckRemoteDebuggerPresent检测出错: {str(e)}")
        
        return False
    
    def check_debugger_ntglobalflag(self):
        """检查NtGlobalFlag"""
        if platform.system() != "Windows":
            return False
        
        try:
            # PEB偏移量可能因Windows版本而异
            peb = ctypes.c_void_p()
            ntdll.NtQueryInformationProcess(
                kernel32.GetCurrentProcess(),
                0,  # ProcessBasicInformation
                ctypes.byref(peb),
                ctypes.sizeof(peb),
                None
            )
            
            # NtGlobalFlag在PEB中的偏移量
            nt_global_flag_offset = 0x68
            nt_global_flag = ctypes.c_int.from_address(peb.value + nt_global_flag_offset).value
            
            # 检查调试标志
            if nt_global_flag & 0x70:  # 0x70 = FLG_HEAP_ENABLE_TAIL_CHECK | FLG_HEAP_ENABLE_FREE_CHECK | FLG_HEAP_VALIDATE_PARAMETERS
                self._debug_detected("NtGlobalFlag", f"发现调试标志: 0x{nt_global_flag:X}")
                return True
        except Exception as e:
            logger.error(f"NtGlobalFlag检测出错: {str(e)}")
        
        return False
    
    def check_debugger_timing(self):
        """使用时间检测调试器（调试时程序运行会变慢）"""
        try:
            start_time = time.time()
            
            # 执行一些计算密集型操作
            for i in range(1000000):
                hash_value = hashlib.sha256(str(i).encode()).hexdigest()
            
            end_time = time.time()
            elapsed = end_time - start_time
            
            # 根据系统性能调整阈值
            threshold = 1.0  # 秒
            
            if elapsed > threshold:
                self._debug_detected("时间检测", f"操作耗时过长: {elapsed:.2f}秒 > {threshold:.2f}秒")
                return True
        except Exception as e:
            logger.error(f"时间检测出错: {str(e)}")
        
        return False
    
    def check_debugger_environment(self):
        """检查环境变量和进程"""
        try:
            # 检查可疑环境变量
            suspicious_env_vars = ["_DEBUG", "DEBUG", "DEBUGGER"]
            for var in suspicious_env_vars:
                if var in os.environ:
                    self._debug_detected("环境变量检测", f"发现可疑环境变量: {var}={os.environ[var]}")
                    return True
            
            # 检查父进程（在某些情况下，调试器会是父进程）
            if platform.system() == "Windows":
                try:
                    import psutil
                    current_process = psutil.Process(os.getpid())
                    parent = current_process.parent()
                    
                    if parent:
                        suspicious_parents = ["ida", "ida64", "ollydbg", "x64dbg", "x32dbg", "windbg", "gdb"]
                        for name in suspicious_parents:
                            if name in parent.name().lower():
                                self._debug_detected("父进程检测", f"可疑父进程: {parent.name()}")
                                return True
                except ImportError:
                    pass  # psutil可能未安装
        except Exception as e:
            logger.error(f"环境检测出错: {str(e)}")
        
        return False
    
    def check_debugger_hardware_breakpoints(self):
        """检查硬件断点"""
        if platform.system() != "Windows":
            return False
        
        try:
            # 获取线程上下文
            context = ctypes.c_ulong()
            thread_handle = kernel32.GetCurrentThread()
            
            # 检查调试寄存器
            if ntdll.NtGetContextThread(thread_handle, ctypes.byref(context)) == 0:
                # 检查DR0-DR3寄存器（硬件断点）
                for i in range(4):
                    dr_value = getattr(context, f"Dr{i}")
                    if dr_value != 0:
                        self._debug_detected("硬件断点检测", f"DR{i}寄存器值非零: 0x{dr_value:X}")
                        return True
        except Exception as e:
            logger.error(f"硬件断点检测出错: {str(e)}")
        
        return False
    
    def check_file_integrity(self, file_path):
        """检查文件完整性"""
        try:
            if not os.path.exists(file_path):
                self._tampering_detected("文件完整性", f"文件不存在: {file_path}")
                return False
            
            # 计算文件哈希
            with open(file_path, 'rb') as f:
                file_hash = hashlib.sha256(f.read()).hexdigest()
            
            # 如果是首次检查，保存哈希值
            if file_path not in self.integrity_checks:
                self.integrity_checks[file_path] = file_hash
                return True
            
            # 比较哈希值
            if self.integrity_checks[file_path] != file_hash:
                self._tampering_detected("文件完整性", f"文件已被修改: {file_path}")
                return False
            
            return True
        except Exception as e:
            logger.error(f"文件完整性检查出错: {str(e)}")
            return False
    
    def check_memory_integrity(self, address, size, original_data=None):
        """检查内存完整性"""
        if platform.system() != "Windows":
            return False
        
        try:
            # 分配内存缓冲区
            buffer = ctypes.create_string_buffer(size)
            bytes_read = ctypes.c_size_t(0)
            
            # 读取内存
            if not kernel32.ReadProcessMemory(
                kernel32.GetCurrentProcess(),
                ctypes.c_void_p(address),
                buffer,
                size,
                ctypes.byref(bytes_read)
            ):
                self._tampering_detected("内存完整性", f"无法读取内存: 0x{address:X}")
                return False
            
            # 如果是首次检查，保存原始数据
            if original_data is None:
                return bytes(buffer)
            
            # 比较数据
            if bytes(buffer) != original_data:
                self._tampering_detected("内存完整性", f"内存已被修改: 0x{address:X}")
                return False
            
            return True
        except Exception as e:
            logger.error(f"内存完整性检查出错: {str(e)}")
            return False
    
    def check_stack_trace(self):
        """检查调用堆栈中的可疑函数"""
        try:
            stack = traceback.extract_stack()
            suspicious_functions = ["debug", "trace", "hook", "patch", "inject", "attach"]
            
            for frame in stack:
                frame_file = frame.filename.lower()
                frame_func = frame.name.lower()
                
                # 检查可疑文件名
                for name in ["debugger", "ida", "olly", "x64dbg", "immunity"]:
                    if name in frame_file:
                        self._debug_detected("堆栈检测", f"可疑文件名: {frame.filename}")
                        return True
                
                # 检查可疑函数名
                for name in suspicious_functions:
                    if name in frame_func:
                        self._debug_detected("堆栈检测", f"可疑函数名: {frame.name}")
                        return True
        except Exception as e:
            logger.error(f"堆栈检测出错: {str(e)}")
        
        return False
    
    def run_all_checks(self):
        """运行所有反调试检查"""
        # 检查是否应该运行检查（基于时间间隔）
        current_time = datetime.now()
        if (current_time - self.last_check_time).total_seconds() < self.check_interval:
            return False
        
        # 更新上次检查时间
        self.last_check_time = current_time
        
        # 如果使用随机间隔，为下次检查设置新的间隔
        if self.random_interval:
            self.check_interval = random.uniform(1.0, 5.0)
        
        # 运行所有检查
        checks = [
            self.check_debugger_isdebuggerpresent,
            self.check_debugger_checkremotedebuggerpresent,
            self.check_debugger_windows,
            self.check_debugger_ntglobalflag,
            self.check_debugger_timing,
            self.check_debugger_environment,
            self.check_debugger_hardware_breakpoints,
            self.check_stack_trace
        ]
        
        # 随机打乱检查顺序，使检测更难预测
        random.shuffle(checks)
        
        for check in checks:
            try:
                if check():
                    return True  # 发现调试器
            except Exception as e:
                logger.error(f"检查函数出错: {check.__name__}, 错误: {str(e)}")
        
        return False
    
    def start_monitoring(self, interval=2.0, random_interval=True):
        """开始监控调试尝试"""
        self.check_interval = interval
        self.random_interval = random_interval
        
        def monitor_thread():
            while True:
                try:
                    self.run_all_checks()
                    
                    # 使用随机睡眠时间，使检测更难预测
                    sleep_time = random.uniform(0.5, 2.0) if random_interval else interval
                    time.sleep(sleep_time)
                except Exception as e:
                    logger.error(f"监控线程出错: {str(e)}")
                    time.sleep(1.0)  # 出错后短暂暂停
        
        # 创建并启动监控线程
        thread = threading.Thread(target=monitor_thread, daemon=True)
        thread.start()
        
        return thread


class AntiTampering:
    """反篡改技术集合"""
    
    def __init__(self):
        self.file_hashes = {}
        self.memory_regions = {}
        self.tampering_detected_callback = None
    
    def set_tampering_callback(self, callback):
        """设置篡改检测回调函数"""
        self.tampering_detected_callback = callback
    
    def _tampering_detected(self, technique, details=None):
        """篡改检测处理"""
        logger.warning(f"检测到篡改尝试! 技术: {technique}, 详情: {details}")
        
        if self.tampering_detected_callback:
            try:
                self.tampering_detected_callback(technique, details)
            except Exception as e:
                logger.error(f"篡改检测回调出错: {str(e)}")
    
    def protect_file(self, file_path):
        """保护文件不被修改"""
        try:
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return False
            
            # 计算文件哈希
            with open(file_path, 'rb') as f:
                file_hash = hashlib.sha256(f.read()).hexdigest()
            
            # 保存哈希值
            self.file_hashes[file_path] = file_hash
            
            return True
        except Exception as e:
            logger.error(f"保护文件出错: {str(e)}")
            return False
    
    def check_file_integrity(self, file_path=None):
        """检查文件完整性"""
        try:
            # 如果未指定文件，检查所有已保护的文件
            files_to_check = [file_path] if file_path else list(self.file_hashes.keys())
            
            for path in files_to_check:
                if not os.path.exists(path):
                    self._tampering_detected("文件完整性", f"文件不存在: {path}")
                    return False
                
                # 计算当前哈希
                with open(path, 'rb') as f:
                    current_hash = hashlib.sha256(f.read()).hexdigest()
                
                # 比较哈希值
                if current_hash != self.file_hashes.get(path):
                    self._tampering_detected("文件完整性", f"文件已被修改: {path}")
                    return False
            
            return True
        except Exception as e:
            logger.error(f"文件完整性检查出错: {str(e)}")
            return False
    
    def protect_memory_region(self, address, size):
        """保护内存区域不被修改"""
        if platform.system() != "Windows":
            return None
        
        try:
            # 分配内存缓冲区
            buffer = ctypes.create_string_buffer(size)
            bytes_read = ctypes.c_size_t(0)
            
            # 读取内存
            if not kernel32.ReadProcessMemory(
                kernel32.GetCurrentProcess(),
                ctypes.c_void_p(address),
                buffer,
                size,
                ctypes.byref(bytes_read)
            ):
                logger.error(f"无法读取内存: 0x{address:X}")
                return None
            
            # 保存内存数据
            memory_data = bytes(buffer)
            self.memory_regions[(address, size)] = memory_data
            
            return memory_data
        except Exception as e:
            logger.error(f"保护内存区域出错: {str(e)}")
            return None
    
    def check_memory_integrity(self, address=None, size=None):
        """检查内存完整性"""
        if platform.system() != "Windows":
            return False
        
        try:
            # 如果未指定地址和大小，检查所有已保护的内存区域
            regions_to_check = [(address, size)] if address is not None and size is not None else list(self.memory_regions.keys())
            
            for addr, sz in regions_to_check:
                # 分配内存缓冲区
                buffer = ctypes.create_string_buffer(sz)
                bytes_read = ctypes.c_size_t(0)
                
                # 读取内存
                if not kernel32.ReadProcessMemory(
                    kernel32.GetCurrentProcess(),
                    ctypes.c_void_p(addr),
                    buffer,
                    sz,
                    ctypes.byref(bytes_read)
                ):
                    self._tampering_detected("内存完整性", f"无法读取内存: 0x{addr:X}")
                    return False
                
                # 比较内存数据
                current_data = bytes(buffer)
                original_data = self.memory_regions.get((addr, sz))
                
                if current_data != original_data:
                    self._tampering_detected("内存完整性", f"内存已被修改: 0x{addr:X}")
                    return False
            
            return True
        except Exception as e:
            logger.error(f"内存完整性检查出错: {str(e)}")
            return False
    
    def protect_critical_sections(self, code_sections):
        """保护关键代码段"""
        for section in code_sections:
            if isinstance(section, tuple) and len(section) == 2:
                addr, size = section
                self.protect_memory_region(addr, size)
    
    def start_monitoring(self, interval=5.0):
        """开始监控篡改尝试"""
        def monitor_thread():
            while True:
                try:
                    # 检查文件完整性
                    self.check_file_integrity()
                    
                    # 检查内存完整性
                    self.check_memory_integrity()
                    
                    # 随机睡眠时间
                    sleep_time = random.uniform(interval * 0.5, interval * 1.5)
                    time.sleep(sleep_time)
                except Exception as e:
                    logger.error(f"监控线程出错: {str(e)}")
                    time.sleep(1.0)  # 出错后短暂暂停
        
        # 创建并启动监控线程
        thread = threading.Thread(target=monitor_thread, daemon=True)
        thread.start()
        
        return thread


# 示例：如何使用反调试和反篡改功能
if __name__ == "__main__":
    # 反调试示例
    anti_debug = AntiDebug()
    
    # 设置调试检测回调
    def debug_detected(technique, details):
        print(f"警告：检测到调试尝试！技术：{technique}，详情：{details}")
        # 在实际应用中，可以在这里采取措施，如退出程序或清除敏感数据
    
    anti_debug.set_debug_callback(debug_detected)
    
    # 启动监控
    anti_debug.start_monitoring()
    
    # 反篡改示例
    anti_tampering = AntiTampering()
    
    # 设置篡改检测回调
    def tampering_detected(technique, details):
        print(f"警告：检测到篡改尝试！技术：{technique}，详情：{details}")
        # 在实际应用中，可以在这里采取措施
    
    anti_tampering.set_tampering_callback(tampering_detected)
    
    # 保护当前脚本文件
    anti_tampering.protect_file(__file__)
    
    # 启动监控
    anti_tampering.start_monitoring()
    
    # 保持程序运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("程序已退出")
